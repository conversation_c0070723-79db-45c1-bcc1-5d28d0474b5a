import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 处罚订单查询测试客户端
 * 用于测试淘宝客处罚订单查询功能
 */
export class PunishOrderTest extends BaseTestClient {
	private queryParams: {
		af_order_option: {
			relation_id?: number;
			tb_trade_id?: number;
			page_size?: number;
			page_no?: number;
			span?: number;
			start_time?: string;
			adzone_id?: number;
			site_id?: number;
		};
	};

	/**
	 * 构造函数
	 * @param queryParams 处罚订单查询参数
	 */
	constructor(queryParams: {
		af_order_option: {
			relation_id?: number;
			tb_trade_id?: number;
			page_size?: number;
			page_no?: number;
			span?: number;
			start_time?: string;
			adzone_id?: number;
			site_id?: number;
		};
	}) {
		super();
		this.queryParams = queryParams;
	}

	/**
	 * 执行处罚订单查询测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			console.log(
				'发送处罚订单查询请求参数:',
				JSON.stringify(this.queryParams, null, 2)
			);

			// 调用工具并获取结果
			const result = await this.callTool(
				'taobao.getPunishOrder',
				this.queryParams
			);

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('处罚订单查询测试成功!');
				console.log(`查询结果: ${result.content[0].text}`);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 如果直接运行此文件,则执行测试
if (currentFilePath === entryPointPath) {
	const queryParams = {
		af_order_option: {
			tb_trade_id: 2588979561831717983,
		},
	};

	console.log('启动处罚订单查询测试');
	const test = new PunishOrderTest(queryParams);
	test.runTest().catch(console.error);
}
