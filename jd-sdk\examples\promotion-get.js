/**
 * 推广链接生成示例
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.promotion.byunionid.get
 */
require('dotenv').config();
const { JdClient } = require('../index');

// 检查环境变量
console.log('检查环境变量...');
['key', 'appkey', 'secretkey', 'unionId'].forEach(key => {
  console.log(`${key}: ${process.env[key] ? '已设置' : '未设置'}`);
});

console.log('\n环境变量实际值:');
console.log('appkey:', process.env.appkey);
console.log('unionId:', process.env.unionId);

// 创建京东客户端
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main() {
  try {
    // 构建请求参数
    const params = {
      promotionCodeReq: {
        // 必填参数
        materialId: "https://u.jd.com/a11puCE", // 商品ID或链接
        unionId: process.env.unionId, // 联盟ID
        
        // 选填参数
        positionId: "", // 推广位ID，不传会取链接中的推广位
        pid: "", // 联盟子站长身份标识，格式：unionId_siteId_positionId
        couponUrl: "", // 优惠券链接
        subUnionId: "", // 子联盟ID（需申请，申请方式：联系运营支持）
        chainType: 3, // 转链类型，1:长链， 2:短链， 3:长链+短链
        giftCouponKey: "", // 礼金批次ID
        channelId: "" // 渠道ID
      }
    };

    console.log('\n尝试发送的参数:');
    console.log(JSON.stringify(params, null, 2));

    // 执行请求
    const result = await client.execute('UnionOpenPromotionByUnionidGetRequest', params);
    
    console.log('\n完整API响应:');
    console.log(JSON.stringify(result, null, 2));

    // 解析业务响应
    const apiResponse = result.jd_union_open_promotion_byunionid_get_responce;
    if (apiResponse && apiResponse.code === '0') {
      const getResult = JSON.parse(apiResponse.getResult);
      
      console.log('\n业务响应:');
      console.log('  状态码:', getResult.code);
      console.log('  消息:', getResult.message);
      console.log('  请求ID:', getResult.requestId);
      
      if (getResult.code === 200 && getResult.data) {
        console.log('\n生成的推广链接:');
        console.log('  短链接:', getResult.data.shortURL);
        if (getResult.data.clickURL) {
          console.log('  点击链接:', getResult.data.clickURL);
        }
      }
    } else {
      console.error('API调用失败:', apiResponse);
    }

  } catch (err) {
    console.error('发生错误:', err);
    console.log('请检查您的.env文件中是否设置了正确的参数。');
    console.log('查看京东联盟API文档: https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.promotion.byunionid.get');
  }
}

// 执行主函数
main(); 