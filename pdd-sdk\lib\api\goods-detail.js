/**
 * 拼多多商品详情查询API
 * 接口名称：pdd.ddk.oauth.goods.detail
 */

const BaseApi = require('../core/base-api');

class GoodsDetail extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 查询商品详情
   * @param {Object} options 查询参数
   * @param {Number|String} [options.goodsSign] 商品goodsSign，支持通过goodsSign查询商品
   * @param {Number|String} [options.searchId] 搜索id，建议填写，提高收益
   * @param {Number|String} [options.pid] 推广位ID
   * @param {Number|String} [options.customParameters] 自定义参数
   * @param {Number} [options.zsDuoId] 招商多多客ID
   * @param {Boolean} [options.needSkuInfo] 是否需要商品sku信息
   * @returns {Promise} Promise对象
   */
  async getDetail(options) {
    // 参数校验
    if (!options.goodsSign) {
      throw new Error('参数错误: goodsSign不能为空');
    }

    // 执行请求
    return this.execute('pdd.ddk.oauth.goods.detail', options);
  }
}

module.exports = GoodsDetail; 