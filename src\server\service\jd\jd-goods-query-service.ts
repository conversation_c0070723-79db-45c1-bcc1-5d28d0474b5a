import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { log, logError } from "../../../utils/logger.js";
import { JdApiManager } from "../../jd/api-manager.js";

/**
 * 京东商品查询服务类
 * 处理京东联盟商品查询功能
 */
export class JdGoodsQueryService {
  /**
   * 构造函数
   * @param mcpServer MCP服务器实例
   * @param apiManager 京东API管理器实例
   * @param getFullToolName 获取完整工具名称的函数
   */
  constructor(
    private mcpServer: McpServer,
    private apiManager: JdApiManager,
    private getFullToolName: (name: string) => string
  ) {}

  /**
   * 注册商品查询工具
   */
  public registerTool(): void {
    const toolName = this.getFullToolName("goods.query");
    
    this.mcpServer.tool(
      toolName,
      "查询京东商品信息[京东联盟-商品查询]",
      {
        keyword: z.string().optional().describe("【非必填】搜索关键词，支持多个关键词，用英文逗号分隔"),
        skuIds: z.string().optional().describe("【非必填】京东skuID串，逗号分隔，最多100个"),
        cid1: z.number().int().optional().describe("【非必填】一级类目ID"),
        cid2: z.number().int().optional().describe("【非必填】二级类目ID"),
        cid3: z.number().int().optional().describe("【非必填】三级类目ID"),
        owner: z.string().optional().describe("【非必填】商品类型：自营[g]，POP[p]"),
        pageIndex: z.number().int().min(1).optional().describe("【非必填】页码，默认1"),
        pageSize: z.number().int().optional().describe("【非必填】每页数量，默认20，上限50"),
        sortName: z.string().optional().describe("【非必填】排序字段(price:单价,commissionShare:佣金比例,commission:佣金,inOrderCount30Days:30天引单量)"),
        sort: z.string().optional().describe("【非必填】排序方式，asc:升序，desc:降序，默认降序"),
        isCoupon: z.number().int().optional().describe("【非必填】是否是优惠券商品，1：有优惠券，0：无优惠券"),
        isPG: z.number().int().optional().describe("【非必填】是否是拼购商品，1：拼购商品，0：非拼购商品"),
        isHot: z.number().int().optional().describe("【非必填】是否是爆款，1：爆款商品，0：非爆款商品")
      },
      async (params) => {
        try {
          log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);
          
          // 获取环境变量
          const unionId = process.env.JD_UNION_ID;
          const key = process.env.JD_KEY;
          
          // 检查必要的环境变量
          if (!unionId) {
            throw new Error('环境变量JD_UNION_ID未设置');
          }
          
          log(`使用环境变量: JD_UNION_ID=${unionId}, JD_KEY=${key ? '已设置' : '未设置'}`);
          
          // 至少要有一个查询条件
          if (!params.keyword && !params.skuIds && !params.cid1 && !params.cid2 && !params.cid3 && !params.owner) {
            throw new Error('至少需要一个查询条件(keyword/skuIds/cid1/cid2/cid3/owner)');
          }
          
          try {
            // 执行商品查询
            const result = await this.apiManager.goodsQuery.queryGoods({
              ...params,
              unionId: Number(unionId),
              key
            });
            
            log(`工具 ${toolName} 执行结果: ${JSON.stringify(result, null, 2)}`);
            
            // 结果已经是解析后的JSON对象，直接返回
            return {
              content: [{
                type: "text",
                text: JSON.stringify(result, null, 2)
              }]
            };
          } catch (apiError) {
            logError(`商品查询API调用失败: ${apiError instanceof Error ? apiError.message : String(apiError)}`);
            throw apiError;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          logError(`工具 ${toolName} 执行出错: ${errorMessage}`);
          return {
            content: [{
              type: "text",
              text: `工具执行失败: ${errorMessage}`
            }],
            isError: true
          };
        }
      }
    );
  }
}
