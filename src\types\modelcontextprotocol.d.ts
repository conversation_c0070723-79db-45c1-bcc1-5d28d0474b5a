declare module '@modelcontextprotocol/sdk' {
  export class ClientSession {
    constructor(stdio: any, write: any);
    initialize(): Promise<void>;
    list_tools(): Promise<{ tools: Array<{ name: string; description: string }> }>;
    call_tool(name: string, args: Record<string, any>): Promise<any>;
    close(): Promise<void>;
  }

  export class StdioServerParameters {
    constructor(options: {
      command: string;
      args: string[];
      env?: NodeJS.ProcessEnv;
    });
  }
}

declare module '@modelcontextprotocol/sdk/client/stdio' {
  export function stdio_client(params: any): Promise<[any, any]>;
} 