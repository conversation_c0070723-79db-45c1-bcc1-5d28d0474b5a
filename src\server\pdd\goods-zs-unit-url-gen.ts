import { BaseApi } from './base-api.js';
import { log, logError } from '../../utils/logger.js';
import { getRequiredEnvVar } from '../../utils/env.js';
import { GoodsZsUnitUrlGenParams } from './types.js';

/**
 * 多多进宝转链接口 API
 */
export class GoodsZsUnitUrlGen extends BaseApi {
	/**
	 * 转链
	 * @param params 转链参数
	 * @returns 转链结果
	 */
	async generate(params: GoodsZsUnitUrlGenParams): Promise<any> {
		try {
			// 从环境变量获取推广位ID
			const pid = getRequiredEnvVar('PDD_PID');

			// 构造完整参数
			const fullParams: Record<string, any> = {
				...params,
				pid: pid,
			};

			log(`完整参数: ${JSON.stringify(fullParams)}`);

			// 调用API
			return this.callApi(
				'pdd.ddk.oauth.goods.zs.unit.url.gen',
				fullParams
			);
		} catch (error) {
			logError(`多多进宝转链失败: ${error instanceof Error ? error.message : String(error)}`);
			process.exit(1);
		}
	}
}
