/**
 * 拼多多推广位查询API
 * 接口名称：pdd.ddk.oauth.goods.pid.query
 */

const BaseApi = require('../core/base-api');

class GoodsPidQuery extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 查询已经生成的推广位信息
   * @param {Object} options 查询参数
   * @param {Number} [options.page] 返回的页数，默认为1
   * @param {Number} [options.pageSize] 返回的每页推广位数量，默认为100
   * @param {Array<String>} [options.pidList] 推广位ID列表，例如["9415134_303378156"]
   * @returns {Promise} Promise对象
   */
  async query(options = {}) {
    // 默认参数处理
    const defaultOptions = {
      page: 1,
      pageSize: 20
    };

    // 合并参数
    const mergedOptions = { ...defaultOptions, ...options };

    // 执行请求
    return this.execute('pdd.ddk.oauth.goods.pid.query', mergedOptions);
  }
}

module.exports = GoodsPidQuery; 