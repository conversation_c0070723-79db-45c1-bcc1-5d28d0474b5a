import {
  JdCouponQueryParams,
  JdCouponInfo,
  JdCouponQueryResponseData,
  JdApiResponse
} from "./types.js";
import { log, logDebug, logError } from "../../utils/logger.js";
import { BaseApi } from "./base-api.js";

// 通用响应数据接口
export interface JdCommonResponseData<T = any> {
  code: number;
  message: string;
  data: T;
}

/**
 * 京东优惠券查询API
 * 根据京东的商品链接或SKU ID查询可用的优惠券信息
 */
export class CouponQuery extends BaseApi {
  /**
   * API方法名称
   * @private
   */
  private readonly METHOD_NAME = 'jd.union.open.coupon.query';

  /**
   * 构造函数
   */
  constructor() {
    super();
    log('京东优惠券查询API初始化完成');
  }

  /**
   * 查询优惠券信息
   * @param params 查询参数，包括couponsUrl(可为字符串或数组)，通过unionId和key认证
   * @returns 优惠券信息响应
   */
  async queryCoupons(params: JdCouponQueryParams): Promise<any> {
    try {
      const { couponUrls } = params;
      
      // 根据京东SDK的要求格式化参数
      // 注意：这里需要考虑传入的couponUrls可能是字符串或数组
      const formattedUrls = Array.isArray(couponUrls) ? couponUrls : [couponUrls];
      
      // 直接创建API参数对象，确保与SDK示例一致
      // 京东优惠券查询API需要将参数直接传递（不封装在couponReq中）
      const apiParams = {
        couponUrls: formattedUrls
      };
      
      logDebug(`优惠券查询参数: ${JSON.stringify({...apiParams, key: '已设置'})}`);
      
      // 调用基类的callApi方法
      return await this.callApi(this.METHOD_NAME, apiParams);
    } catch (error) {
      logError(`京东优惠券查询错误: ${error instanceof Error ? error.message : String(error)}`);
      throw new Error(`优惠券查询失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
} 