import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { TaobaoApiManager } from '../../taobao/api-manager.js';
import { TaobaoParams } from './params.js';

/**
 * 淘宝订单详情服务类
 * 处理淘宝客订单查询功能
 */
export class TaobaoOrderDetailsService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 淘宝API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: TaobaoApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册订单查询工具
	 * https://open.taobao.com/api.htm?docId=43755&docType=2&scopeId=16322
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('getOrderDetails');

		this.mcpServer.tool(
			toolName,
			'查询淘宝联盟订单信息[淘宝客-服务商-所有订单查询]',
			TaobaoParams.orderDetails,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);
					const result =
						await this.apiManager.orderDetails.getOrderDetails(
							params
						);
					log(`工具 ${toolName} 执行结果: ${JSON.stringify(result)}`);
					return {
						content: [
							{
								type: 'text',
								text: JSON.stringify(result),
							},
						],
					};
				} catch (error) {
					logError(
						`工具 ${toolName} 执行出错: ${
							error instanceof Error
								? error.message
								: String(error)
						}`
					);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${
									error instanceof Error
										? error.message
										: JSON.stringify(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
