import { log, logError } from "../utils/logger.js";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { ServiceRegistry } from "./service/service-registry.js";

/**
 * MCP服务器管理器
 * 管理单一服务器实例，通过ServiceRegistry注册各平台服务
 */
export class McpServerManager {
  private mcpServer: McpServer;
  private serviceRegistry: ServiceRegistry;
  private transport: StdioServerTransport | null = null;
  private isRunning: boolean = false;

  /**
   * 构造函数
   */
  constructor() {
    // 创建MCP服务器，声明支持tools功能
    this.mcpServer = new McpServer({
      name: "mcp-server-manager",
      version: "1.0.0",
      capabilities: {
        tools: true
      }
    });

    // 创建服务注册表
    this.serviceRegistry = new ServiceRegistry(this.mcpServer);
  }

  /**
   * 启动服务器管理器
   */
  public async start(): Promise<void> {
    if (this.isRunning) {
      log('服务器管理器已在运行中');
      return;
    }

    log('正在启动MCP服务器管理器...');

    try {
      // 注册所有平台服务
      this.serviceRegistry.registerAllServices();

      // 连接transport
      this.transport = new StdioServerTransport();
      await this.mcpServer.connect(this.transport);

      this.isRunning = true;
      log('MCP服务器管理器已启动');
    } catch (error) {
      logError(`启动服务器管理器失败: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }

  /**
   * 停止服务器管理器
   */
  public async stop(): Promise<void> {
    if (!this.isRunning) {
      log('服务器管理器未运行');
      return;
    }

    log('正在停止MCP服务器管理器...');

    try {
      // 断开transport连接
      // 注意：MCP SDK目前不提供disconnect方法

      this.isRunning = false;
      log('MCP服务器管理器已停止');
    } catch (error) {
      logError(`停止服务器管理器失败: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }
}