import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { log } from "../../../utils/logger.js";
import { BaseService } from "../base-service.js";
import { JdApiManager } from "../../jd/api-manager.js";

// 导入所有服务类
import { JdPositionQueryService } from "./jd-position-query-service.js";
import { JdPositionCreateService } from "./jd-position-create-service.js";
import { JdPromotionByUnionidService } from "./jd-promotion-by-unionid-service.js";
import { JdGoodsQueryService } from "./jd-goods-query-service.js";
import { JdCouponQueryService } from "./jd-coupon-query-service.js";

/**
 * 京东服务类
 * 实现京东联盟API功能
 */
export class JdService extends BaseService {
  /**
   * 平台名称
   */
  protected readonly platformName = "jd";
  
  /**
   * API管理器实例，使用懒加载模式
   */
  private apiManager?: JdApiManager;
  
  /**
   * 各个服务实例
   */
  private positionQueryService?: JdPositionQueryService;
  private positionCreateService?: JdPositionCreateService;
  private promotionByUnionidService?: JdPromotionByUnionidService;
  private goodsQueryService?: JdGoodsQueryService;
  private couponQueryService?: JdCouponQueryService;
  
  /**
   * 构造函数
   * @param mcpServer MCP服务器实例
   */
  constructor(mcpServer: McpServer) {
    super(mcpServer);
  }
  
  /**
   * 初始化API管理器(延迟加载)
   */
  protected initApiManager(): JdApiManager {
    log('初始化京东API管理器');
    return new JdApiManager();
  }
  
  /**
   * 获取API管理器实例
   * 确保API管理器已初始化
   */
  private getApiManager(): JdApiManager {
    if (!this.apiManager) {
      this.apiManager = this.initApiManager();
    }
    return this.apiManager;
  }
  
  /**
   * 注册所有京东平台工具
   */
  protected registerPlatformTools(): void {
    // 初始化各个服务
    this.initializeServices();
    
    // 注册各个服务的工具
    this.registerServiceTools();
  }
  
  /**
   * 初始化所有服务
   */
  private initializeServices(): void {
    const apiManager = this.getApiManager();
    const getFullToolName = this.getFullToolName.bind(this);
    
    this.positionQueryService = new JdPositionQueryService(
      this.mcpServer,
      apiManager,
      getFullToolName
    );
    
    this.positionCreateService = new JdPositionCreateService(
      this.mcpServer,
      apiManager,
      getFullToolName
    );
    
    this.promotionByUnionidService = new JdPromotionByUnionidService(
      this.mcpServer,
      apiManager,
      getFullToolName
    );
    
    this.goodsQueryService = new JdGoodsQueryService(
      this.mcpServer,
      apiManager,
      getFullToolName
    );
    
    this.couponQueryService = new JdCouponQueryService(
      this.mcpServer,
      apiManager,
      getFullToolName
    );
  }
  
  /**
   * 注册所有服务的工具
   */
  private registerServiceTools(): void {
    // 注册推广位查询工具
    this.positionQueryService?.registerTool();
    
    // 注册推广位创建工具
    this.positionCreateService?.registerTool();
    
    // 注册转推广链接工具
    this.promotionByUnionidService?.registerTool();
    
    // 注册商品查询工具
    this.goodsQueryService?.registerTool();
    
    // 注册优惠券查询工具
    this.couponQueryService?.registerTool();
  }
}
