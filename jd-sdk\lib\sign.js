/**
 * 京东开放平台API签名工具
 */
const crypto = require('crypto-js');

/**
 * 生成签名
 * @param {string} appSecret 应用密钥
 * @param {Object} params 请求参数
 * @returns {string} 签名结果
 */
function sign(appSecret, params) {
  // 按照key升序排序
  const keys = Object.keys(params).sort();
  
  // 拼接字符串
  let stringToSign = appSecret;
  for (const key of keys) {
    // 值为null或undefined不参与签名
    if (params[key] !== null && params[key] !== undefined) {
      stringToSign += key + params[key];
    }
  }
  stringToSign += appSecret;
  
  // MD5加密并转为大写
  return crypto.MD5(stringToSign).toString().toUpperCase();
}

module.exports = {
  sign
};