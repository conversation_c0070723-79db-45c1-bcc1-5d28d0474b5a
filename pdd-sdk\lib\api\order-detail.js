/**
 * 拼多多订单详情查询API
 * 接口名称：pdd.ddk.oauth.order.detail.get
 */

const BaseApi = require('../core/base-api');

class OrderDetail extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 获取订单详情
   * @param {Object} options 查询参数
   * @param {String} options.order_sn 订单号
   * @returns {Promise} Promise对象
   */
  async get(options = {}) {
    // 必要参数检查
    if (!options.order_sn) {
      throw new Error('订单号 (order_sn) 不能为空');
    }
    
    // 构建API参数
    const apiParams = {
      order_sn: options.order_sn
    };
    
    // 执行请求
    return this.execute('pdd.ddk.oauth.order.detail.get', apiParams);
  }
}

module.exports = OrderDetail; 