/**
 * 优惠券查询示例 - 直接复制SDK代码
 */
require('dotenv').config({ path: './jd-sdk/.env' });
const { JdClient } = require('../jd-sdk');

// 检查环境变量
console.log('检查环境变量...');
['appkey', 'secretkey'].forEach(key => {
  console.log(`${key}: ${process.env[key] ? '已设置' : '未设置'}`);
});

console.log('\n环境变量实际值:');
console.log('appkey:', process.env.appkey);

// 创建京东客户端
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main () {
  // 构建请求参数
  const params = {
    couponUrls: [
      // 优惠券链接，如：
      "https://jingfen.jd.com/item.html?sku=751e21CmFHIM2XN7SiUljfoN_3vbTdJlOaFZ4sgyHbj&q=EHATFRVtE3IRExFmIDUWSh03FS9HShQuRzIbSx0zQC8QUEc3Ey8VShE4FyMUJRRnFHEUEhdtEUFldHokSi1RF0wqIHEREBNmE3EREBRf&needRecommendFlag=1&d=aGBOQs1&cu=true&utm_source=lianmeng__10__kong&utm_medium=tuiguang&utm_campaign=t_1000793546_&utm_term=d1e006ec2aa0408794c9b1e4cfb4f882#/pages/common-coupon/common-coupon",
    ]
  };

  console.log('\n尝试发送的参数:');
  console.log(JSON.stringify(params, null, 2));

  // 执行请求
  const result = await client.execute('jd.union.open.coupon.query', params);

  console.log('\n完整API响应:');
  console.log(JSON.stringify(result, null, 2));

  // 解析业务响应
  const apiResponse = result.jd_union_open_coupon_query_responce;
  console.log(apiResponse);
}

// 执行主函数
main().catch(console.error); 