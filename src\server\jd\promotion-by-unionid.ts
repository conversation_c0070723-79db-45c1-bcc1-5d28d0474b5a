import { log, logDebug, logError } from "../../utils/logger.js";
import { BaseApi } from "./base-api.js";
import {
  JdPromotionCodeParams,
  JdApiResponse,
  JdPromotionCodeResponseData
} from "./types.js";

/**
 * 京东转推广链接API实现
 * 工具商媒体帮助子站长获取普通推广链接，可传入PID参数以区分子站长的推广位
 */
export class PromotionByUnionid extends BaseApi {
  /**
   * API方法名称
   * @private
   */
  private readonly METHOD_NAME = 'jd.union.open.promotion.byunionid.get';

  /**
   * 构造函数
   */
  constructor() {
    super();
    log('京东转推广链接API初始化完成');
  }

  /**
   * 生成推广链接
   * @param params 推广链接参数
   * @returns 生成的推广链接信息
   */
  public async getPromotionLink(params: Partial<JdPromotionCodeParams>): Promise<JdApiResponse<JdPromotionCodeResponseData>> {
    try {
      // 准备API参数
      const mergedParams = {
        ...this.getCommonParams(),
        siteId: this.siteId,
        ...params
      };
      
      // 打印日志，但过滤掉敏感数据
      const safeParams = { ...mergedParams };
      if (safeParams.key) {
        safeParams.key = `已设置(长度:${safeParams.key.length})`;
      }
      
      logDebug(`生成京东推广链接, 参数: ${JSON.stringify(safeParams)}`);

      // 验证必填参数
      this.validateParams(mergedParams);
      
      // 调用API（使用新的SDK方式自动处理参数包装）
      return await this.callApi(this.METHOD_NAME, mergedParams);
    } catch (error) {
      logError(`京东推广链接生成错误: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
  
  /**
   * 验证参数
   * @param params 推广链接参数
   */
  private validateParams(params: Partial<JdPromotionCodeParams>): void {
    // 检查必填参数
    if (!params.materialId) {
      throw new Error('业务参数materialId是必填的');
    }
    
    if (!params.unionId) {
      throw new Error('业务参数unionId是必填的');
    }
    
    // 验证链接类型
    if (params.chainType !== undefined && (params.chainType < 1 || params.chainType > 3)) {
      throw new Error('业务参数chainType的值必须在1-3之间（1:长链接 2:短链接 3:长链接+短链接）');
    }
  }
} 