/**
 * 联盟推广链接获取API
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.promotion.byunionid.get
 */
const { BaseApi } = require('../api');

/**
 * 联盟推广链接获取API请求类
 * 工具商媒体帮助子站长获取普通推广链接和优惠券二合一推广链接，可传入PID参数以区分子站长的推广位
 */
class UnionOpenPromotionByUnionidGetRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.promotion.byunionid.get';
  }

  /**
   * 执行链接生成请求
   * @param {Object} params 请求参数
   * @param {PromotionCodeReq} params.promotionCodeReq 推广物料请求对象
   * @returns {Promise<Object>} 请求结果
   */
  async get(params = {}) {
    if (!params.promotionCodeReq) {
      throw new Error('promotionCodeReq不能为空');
    }

    // 验证必填参数
    const { materialId, unionId } = params.promotionCodeReq;
    
    if (!materialId) {
      throw new Error('materialId不能为空');
    }
    
    if (!unionId) {
      throw new Error('unionId不能为空');
    }

    const apiParams = {
      promotionCodeReq: this.cleanParams(params.promotionCodeReq)
    };

    return this.execute(apiParams);
  }

  /**
   * 清理参数，移除空值
   * @param {Object} params 原始参数
   * @returns {Object} 清理后的参数
   */
  cleanParams(params) {
    const cleanedParams = {};
    
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        cleanedParams[key] = params[key];
      }
    }
    
    return cleanedParams;
  }
}

module.exports = {
  UnionOpenPromotionByUnionidGetRequest
}; 