import { loadEnvFromNetwork, checkRequiredEnvVars } from './src/JWT/env-loader.js';
import fs from 'fs';
import path from 'path';
import http from 'http';

// 创建一个简单的HTTP服务器来提供JWT令牌
const server = http.createServer((req, res) => {
  if (req.url === '/env.jwt') {
    // 读取JWT令牌
    const token = fs.readFileSync('test-env.jwt', 'utf8');
    
    // 设置响应头
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Access-Control-Allow-Origin', '*');
    
    // 返回JWT令牌
    res.end(token);
  } else {
    res.statusCode = 404;
    res.end('Not Found');
  }
});

// 启动服务器
const PORT = 3000;
server.listen(PORT, async () => {
  console.log(`服务器已启动，监听端口 ${PORT}`);
  
  try {
    // 清除环境变量
    delete process.env.TEST_VAR1;
    delete process.env.TEST_VAR2;
    delete process.env.TEST_SECRET;
    
    console.log('当前环境变量:');
    console.log('TEST_VAR1:', process.env.TEST_VAR1);
    console.log('TEST_VAR2:', process.env.TEST_VAR2);
    console.log('TEST_SECRET:', process.env.TEST_SECRET);
    
    // 从网络加载环境变量
    console.log('\n正在从网络加载环境变量...');
    const success = await loadEnvFromNetwork({
      url: `http://localhost:${PORT}/env.jwt`,
      secret: 'test-secret-key'
    });
    
    if (success) {
      console.log('环境变量加载成功!');
      
      console.log('\n加载后的环境变量:');
      console.log('TEST_VAR1:', process.env.TEST_VAR1);
      console.log('TEST_VAR2:', process.env.TEST_VAR2);
      console.log('TEST_SECRET:', process.env.TEST_SECRET);
      
      // 检查必要的环境变量
      console.log('\n检查必要的环境变量:');
      const requiredVars = ['TEST_VAR1', 'TEST_VAR2', 'TEST_SECRET'];
      const checkResult = checkRequiredEnvVars(requiredVars);
      
      console.log('检查结果:', checkResult ? '通过' : '失败');
    } else {
      console.log('环境变量加载失败!');
    }
  } catch (error) {
    console.error('测试失败:', error instanceof Error ? error.message : String(error));
  } finally {
    // 关闭服务器
    server.close(() => {
      console.log('服务器已关闭');
    });
  }
});
