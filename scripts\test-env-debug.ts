import dotenv from 'dotenv';
import axios from 'axios';
import { verifyToken } from './src/JWT/jwt-utils.js';

// 加载本地环境变量
dotenv.config();

// 主函数
async function main() {
  console.log('====== 测试从网络加载环境变量 ======');
  
  // 获取环境变量
  const envUrl = process.env.ENV_JWT_URL;
  const envSecret = process.env.ENV_JWT_SECRET || 'mcp-default-secret-key';
  
  console.log('环境变量 URL:', envUrl);
  console.log('环境变量 Secret:', envSecret ? '已设置' : '未设置');
  
  if (!envUrl) {
    console.log('未设置环境变量 JWT URL，无法从网络加载环境变量');
    return;
  }
  
  try {
    // 直接发送请求获取数据
    console.log(`正在从 ${envUrl} 获取数据...`);
    
    const response = await axios.get(envUrl, {
      timeout: 10000,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'MCP-Server/1.0'
      }
    });
    
    console.log('响应状态码:', response.status);
    console.log('响应数据类型:', typeof response.data);
    console.log('响应数据:', JSON.stringify(response.data).substring(0, 200));
    
    // 从响应中提取JWT令牌
    let jwtToken;
    if (typeof response.data === 'object' && response.data !== null && 'data' in response.data) {
      jwtToken = response.data.data;
      console.log('从 JSON 响应中提取了 JWT 令牌');
    } else {
      jwtToken = response.data;
      console.log('使用原始响应作为 JWT 令牌');
    }
    
    console.log('JWT令牌类型:', typeof jwtToken);
    console.log('JWT令牌:', jwtToken ? jwtToken.substring(0, 50) + '...' : 'undefined');
    
    if (typeof jwtToken !== 'string' || !jwtToken.trim()) {
      throw new Error('获取到的JWT令牌无效');
    }
    
    // 尝试解密JWT令牌
    console.log('尝试解密JWT令牌...');
    const decoded = verifyToken(jwtToken, {
      secret: envSecret,
      audience: 'mcp-server',
      issuer: 'mcp-env-encryptor'
    });
    
    console.log('解密成功!');
    console.log('解密结果:', JSON.stringify(decoded).substring(0, 200));
    
    // 设置环境变量
    for (const [key, value] of Object.entries(decoded)) {
      if (typeof value === 'string' && !key.startsWith('iat') && !key.startsWith('exp') && !key.startsWith('aud') && !key.startsWith('iss')) {
        process.env[key] = value;
        console.log(`设置环境变量: ${key} = ${value}`);
      }
    }
    
  } catch (error) {
    console.error('错误:', error instanceof Error ? error.message : String(error));
  }
}

// 执行主函数
main().catch(error => {
  console.error('未处理的错误:', error);
  process.exit(1);
});
