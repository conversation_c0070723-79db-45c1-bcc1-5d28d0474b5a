import { log } from '../../utils/logger.js';
import { GoodsDetail } from './goods-detail.js';
import { CmsPromUrlGenerate } from './cms-prom-url-generate.js';
import { GoodsPidGenerate } from './goods-pid-generate.js';
import { GoodsPidQuery } from './goods-pid-query.js';
import { GoodsPromUrlGenerate } from './goods-prom-url-generate.js';
import { GoodsRecommendGet } from './goods-recommend-get.js';
import { GoodsSearch } from './goods-search.js';
import { GoodsZsUnitUrlGen } from './goods-zs-unit-url-gen.js';
import { MemberAuthorityQuery } from './member-authority-query.js';
import { OrderDetailGet } from './order-detail-get.js';
import { OrderListIncrementGet } from './order-list-increment-get.js';
import { ResourceUrlGen } from './resource-url-gen.js';

/**
 * 拼多多API管理器
 * 统一管理所有拼多多API处理器实例
 */
export class PddApiManager {
	private _goodsDetail?: GoodsDetail;
	private _cmsPromUrlGenerate?: CmsPromUrlGenerate;
	private _goodsPidGenerate?: GoodsPidGenerate;
	private _goodsPidQuery?: GoodsPidQuery;
	private _goodsPromUrlGenerate?: GoodsPromUrlGenerate;
	private _goodsRecommendGet?: GoodsRecommendGet;
	private _goodsSearch?: GoodsSearch;
	private _goodsZsUnitUrlGen?: GoodsZsUnitUrlGen;
	private _memberAuthorityQuery?: MemberAuthorityQuery;
	private _orderDetailGet?: OrderDetailGet;
	private _orderListIncrementGet?: OrderListIncrementGet;
	private _resourceUrlGen?: ResourceUrlGen;

	/**
	 * 构造函数
	 * 初始化所有API处理器
	 */
	constructor() {
		log('初始化拼多多API处理器');
		this._goodsDetail = new GoodsDetail();
		this._cmsPromUrlGenerate = new CmsPromUrlGenerate();
		this._goodsPidGenerate = new GoodsPidGenerate();
		this._goodsPidQuery = new GoodsPidQuery();
		this._goodsPromUrlGenerate = new GoodsPromUrlGenerate();
		this._goodsRecommendGet = new GoodsRecommendGet();
		this._goodsSearch = new GoodsSearch();
		this._goodsZsUnitUrlGen = new GoodsZsUnitUrlGen();
		this._memberAuthorityQuery = new MemberAuthorityQuery();
		this._orderDetailGet = new OrderDetailGet();
		this._orderListIncrementGet = new OrderListIncrementGet();
		this._resourceUrlGen = new ResourceUrlGen();
	}

	/**
	 * 获取商品详情查询API
	 */
	get goodsDetail(): GoodsDetail {
		if (!this._goodsDetail) {
			this._goodsDetail = new GoodsDetail();
		}
		return this._goodsDetail;
	}

	/**
	 * 获取生成商城推广链接 API
	 */
	get cmsPromUrlGenerate(): CmsPromUrlGenerate {
		if (!this._cmsPromUrlGenerate) {
			this._cmsPromUrlGenerate = new CmsPromUrlGenerate();
		}
		return this._cmsPromUrlGenerate;
	}

	/**
	 * 获取推广位生成 API
	 */
	get goodsPidGenerate(): GoodsPidGenerate {
		if (!this._goodsPidGenerate) {
			this._goodsPidGenerate = new GoodsPidGenerate();
		}
		return this._goodsPidGenerate;
	}

	/**
	 * 获取推广位查询 API
	 */
	get goodsPidQuery(): GoodsPidQuery {
		if (!this._goodsPidQuery) {
			this._goodsPidQuery = new GoodsPidQuery();
		}
		return this._goodsPidQuery;
	}

	/**
	 * 获取商品推广链接生成 API
	 */
	get goodsPromUrlGenerate(): GoodsPromUrlGenerate {
		if (!this._goodsPromUrlGenerate) {
			this._goodsPromUrlGenerate = new GoodsPromUrlGenerate();
		}
		return this._goodsPromUrlGenerate;
	}

	/**
	 * 获取商品推荐查询 API
	 */
	get goodsRecommendGet(): GoodsRecommendGet {
		if (!this._goodsRecommendGet) {
			this._goodsRecommendGet = new GoodsRecommendGet();
		}
		return this._goodsRecommendGet;
	}

	/**
	 * 获取商品搜索 API
	 */
	get goodsSearch(): GoodsSearch {
		if (!this._goodsSearch) {
			this._goodsSearch = new GoodsSearch();
		}
		return this._goodsSearch;
	}

	/**
	 * 获取多多进宝转链接口 API
	 */
	get goodsZsUnitUrlGen(): GoodsZsUnitUrlGen {
		if (!this._goodsZsUnitUrlGen) {
			this._goodsZsUnitUrlGen = new GoodsZsUnitUrlGen();
		}
		return this._goodsZsUnitUrlGen;
	}

	/**
	 * 获取查询是否绑定备案 API
	 */
	get memberAuthorityQuery(): MemberAuthorityQuery {
		if (!this._memberAuthorityQuery) {
			this._memberAuthorityQuery = new MemberAuthorityQuery();
		}
		return this._memberAuthorityQuery;
	}

	/**
	 * 获取订单详情 API
	 */
	get orderDetailGet(): OrderDetailGet {
		if (!this._orderDetailGet) {
			this._orderDetailGet = new OrderDetailGet();
		}
		return this._orderDetailGet;
	}

	/**
	 * 按照更新时间段增量同步推广订单信息 API
	 */
	get orderListIncrementGet(): OrderListIncrementGet {
		if (!this._orderListIncrementGet) {
			this._orderListIncrementGet = new OrderListIncrementGet();
		}
		return this._orderListIncrementGet;
	}

	/**
	 * 拼多多主站频道推广 API
	 */
	get resourceUrlGen(): ResourceUrlGen {
		if (!this._resourceUrlGen) {
			this._resourceUrlGen = new ResourceUrlGen();
		}
		return this._resourceUrlGen;
	}
}
