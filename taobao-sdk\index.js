'use strict';

/**
 * 淘宝开放平台SDK
 * 支持CommonJS和ESM双模式导入
 */

// 导入原始客户端
var apiClient = require('./lib/api/topClient.js').TopClient;
var dingtalkClient = require('./lib/api/dingtalkClient.js').DingTalkClient;
var tmcClient = require('./lib/tmc/tmcClient.js').TmcClient;

// 导出客户端
module.exports = {
    // 兼容原有导出
    ApiClient: apiClient,
    TmcClient: tmcClient,
    DingTalkClient: dingtalkClient,
    
    // 添加TopClient直接导出，方便使用
    TopClient: apiClient
};
