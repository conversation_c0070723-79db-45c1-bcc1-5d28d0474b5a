/**
 * 执行API请求
 * @param {Object} params 请求参数
 * @returns {Promise<Object>} 请求结果
 */
async execute(params = {}) {
  // 获取请求参数
  const apiName = this.getApiName();
  if (!apiName) {
    throw new Error('API名称不能为空，请在子类中实现getApiName方法');
  }

  // 准备系统参数
  const systemParams = {
    method: apiName,
    app_key: this.config.appKey,
    timestamp: this.getTimestamp(),
    format: 'json',
    v: '1.0',
    sign_method: 'md5'
  };

  // 只有在设置了accessToken的情况下才添加到系统参数中
  if (this.config.accessToken) {
    systemParams.access_token = this.config.accessToken;
  } else {
    console.log('Execute方法中:');
    console.log('- 不添加系统参数access_token');
  }

  // 合并业务参数
  const requestParams = {
    ...params
  };

  // 计算签名
  const sign = this.generateSign(systemParams, requestParams, this.config.secretKey);

  // 构造最终请求参数
  const finalParams = {
    ...systemParams,
    '360buy_param_json': JSON.stringify(requestParams),
    sign: sign
  };

  // 打印完整的请求参数，用于调试
  console.log('请求参数:', {
    url: this.serverUrl,
    systemParams,
    businessParams: requestParams,
    sign
  });

  try {
    // 发送请求
    const response = await axios({
      method: 'POST',
      url: this.serverUrl,
      data: this.createQueryString(finalParams),
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    // 返回响应数据
    return response.data;
  } catch (error) {
 