/**
 * 京东SDK类型定义
 */

declare module '@liuliang520500/jd-sdk' {
  /**
   * 京东API客户端配置
   */
  export interface JdClientConfig {
    /**
     * 应用Key
     */
    appKey?: string;
    
    /**
     * 应用密钥
     */
    secretKey?: string;
    
    /**
     * 访问令牌
     */
    accessToken?: string;
  }
  
  /**
   * 京东API客户端
   */
  export interface JdApiErrorData {
    /**
     * 错误代码
     */
    code: string;
    
    /**
     * 错误消息
     */
    message: string;
    
    /**
     * 子错误代码
     */
    subCode?: string;
    
    /**
     * 子错误消息
     */
    subMessage?: string;
  }
  
  export class JdApiError extends Error {
    /**
     * 错误代码
     */
    code: string;
    
    /**
     * 子错误代码
     */
    subCode?: string;
    
    /**
     * 子错误消息
     */
    subMessage?: string;
    
    constructor(code: string, message: string, subCode?: string, subMessage?: string);
  }
  
  export class JdClient {
    /**
     * 构造函数
     * @param config 客户端配置
     */
    constructor(config?: JdClientConfig);
    
    /**
     * 执行API调用
     * @param method API方法名
     * @param params API参数
     * @param options 请求选项
     * @returns API响应结果
     */
    execute(method: string, params: any, options?: any): Promise<any>;
    
    /**
     * 创建API实例
     * @param ApiClass API类
     * @returns API实例
     */
    createAPI(ApiClass: any): any;
    
    /**
     * 获取推广位查询API
     * @returns 推广位查询API实例
     * @deprecated 推荐使用execute方法
     */
    position(): any;
    
    /**
     * 获取推广位创建API
     * @returns 推广位创建API实例
     * @deprecated 推荐使用execute方法
     */
    positionCreate(): any;
    
    /**
     * 获取推广链接API
     * @returns 推广链接API实例
     * @deprecated 推荐使用execute方法
     */
    promotion(): any;
    
    /**
     * 获取商品查询API
     * @returns 商品查询API实例
     * @deprecated 推荐使用execute方法
     */
    goods(): any;
    
    /**
     * 获取优惠券查询API
     * @returns 优惠券查询API实例
     * @deprecated 推荐使用execute方法
     */
    coupon(): any;
    
    /**
     * 获取订单查询API
     * @returns 订单查询API实例
     * @deprecated 推荐使用execute方法
     */
    order(): any;
    
    /**
     * 获取订单行查询API
     */
    orderRow(): any;
    
    /**
     * 获取京粉精选商品查询API
     */
    goodsJingfen(): any;
    
    /**
     * 获取商品类目查询API
     */
    category(): any;
    
    /**
     * 获取活动查询API
     */
    activity(): any;
  }
} 