import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import path from 'path';
import { fileURLToPath } from 'url';

/**
 * 基础京东测试客户端
 */
export class BaseJdTestClient {
	private client: Client;
	private transport: StdioClientTransport;

	/**
	 * 工具名称映射
	 */
	private readonly TOOL_NAME_MAP: Record<string, string> = {
		'position.query': 'jd.position.query',
		'position.create': 'jd.position.create',
		'promotion.byunionid.get': 'jd.promotion.byunionid.get',
		'goods.query': 'jd.goods.query',
		'coupon.query': 'jd.coupon.query',
	};

	/**
	 * 构造函数
	 */
	constructor() {
		const __filename = fileURLToPath(import.meta.url);
		const __dirname = path.dirname(__filename);

		// 计算入口文件路径
		const executable = path.resolve(__dirname, '../../out/dist/cli.js');

		// 创建MCP客户端
		this.client = new Client({
			name: 'JdClientTest',
			version: '1.0.0',
		});

		// 创建标准输入输出传输层
		this.transport = new StdioClientTransport({
			command: 'node',
			args: [executable, 'logs'],
			cwd: process.cwd(),
		});
	}

	/**
	 * 连接到服务器
	 */
	async connect(): Promise<void> {
		try {
			console.log('连接到MCP服务器...');
			await this.client.connect(this.transport);
			console.log('已连接到MCP服务器');

			// 获取服务器信息
			const serverInfo = this.client.getServerVersion();
			console.log(
				`服务器信息: ${serverInfo?.name} v${serverInfo?.version}`
			);
		} catch (error) {
			console.error(
				`连接服务器失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 断开与服务器的连接
	 */
	async disconnect(): Promise<void> {
		try {
			console.log('正在断开与MCP服务器的连接...');
			await this.client.close();
			console.log('已断开与MCP服务器的连接');
		} catch (error) {
			console.error(
				`断开服务器连接失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 获取工具列表
	 */
	async getToolsList() {
		try {
			await this.connect();

			// 构造请求
			const request = {
				jsonrpc: '2.0',
				id: Date.now(),
				method: 'tools/search',
				params: {},
			};

			// 发送请求并等待响应
			const response = await this.sendRequest(request);

			await this.disconnect();
			return response.result;
		} catch (error) {
			console.error(
				`获取工具列表失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 调用京东工具
	 * @param name 工具名称，不包含jd.前缀
	 * @param args 工具参数
	 * @returns 执行结果
	 */
	async callJdTool(name: string, args: Record<string, any>) {
		// 使用工具名称映射获取全名，如果没有则添加jd.前缀
		const fullToolName =
			this.TOOL_NAME_MAP[name] ||
			(name.startsWith('jd.') ? name : `jd.${name}`);

		try {
			await this.connect();
			console.log(`调用工具 ${fullToolName}: ${JSON.stringify(args)}`);

			// 构造请求
			const request = {
				jsonrpc: '2.0',
				id: Date.now(),
				method: 'tools/call',
				params: {
					name: fullToolName,
					arguments: args,
				},
			};

			// 发送请求并等待响应
			const response = await this.sendRequest(request);
			console.log(
				`工具 ${fullToolName} 返回结果: ${JSON.stringify(response)}`
			);

			if (response.error) {
				throw new Error(
					`工具调用返回错误: ${JSON.stringify(response.error)}`
				);
			}

			await this.disconnect();
			return response.result;
		} catch (error) {
			console.error(
				`工具调用失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 发送请求到服务器并等待响应
	 * @param request 请求对象
	 * @returns 响应对象
	 */
	private async sendRequest(request: any): Promise<any> {
		return new Promise((resolve, reject) => {
			try {
				console.log(`发送请求: ${JSON.stringify(request)}`);

				const requestId = request.id;
				const onResponse = (response: any) => {
					if (response.id === requestId) {
						// 移除监听器
						this.transport.onmessage = undefined;
						resolve(response);
					}
				};

				// 设置响应监听器
				this.transport.onmessage = onResponse;

				// 发送请求
				this.transport.send(request);

				// 设置超时
				setTimeout(() => {
					if (this.transport.onmessage === onResponse) {
						this.transport.onmessage = undefined;
						reject(
							new Error(`请求超时: ${JSON.stringify(request)}`)
						);
					}
				}, 60000); // 60秒超时，适当延长
			} catch (error) {
				reject(error);
			}
		});
	}

	/**
	 * 关闭客户端
	 */
	close() {
		// 关闭客户端连接
		this.client.close();
	}
}
