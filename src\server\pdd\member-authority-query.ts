import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getRequiredEnvVar } from '../../utils/env.js';
import { MemberAuthorityQueryParams } from './types.js';

/**
 * 查询是否绑定备案 API
 */
export class MemberAuthorityQuery extends BaseApi {
	/**
	 * 查询是否绑定备案
	 * @param params 查询参数
	 * @returns 查询结果
	 */
	async query(params: MemberAuthorityQueryParams = {}): Promise<any> {
		try {
			params.pid = params.pid || getRequiredEnvVar('PDD_PID');

			log(`API参数: ${JSON.stringify(params)}`);

			// 调用API
			return this.callApi('pdd.ddk.oauth.member.authority.query', params);
		} catch (error) {
			log(`查询是否绑定备案失败: ${error}`);
			throw error;
		}
	}
}
