import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config({ path: path.resolve(process.cwd(), '.env') });

/**
 * 商品推广链接生成测试客户端
 * 用于测试拼多多商品推广链接生成功能
 */
export class GoodsPromUrlGenerateTest extends BaseTestClient {
	private goods_sign_list: string[];

	/**
	 * 构造函数
	 * @param goods_sign_list 商品sign列表
	 */
	constructor(goods_sign_list: string[]) {
		super();
		this.goods_sign_list = goods_sign_list;
	}

	/**
	 * 执行商品推广链接生成测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			// 调用工具并获取结果
			const result = await this.callTool('pdd.goods.prom.url', {
				goods_sign_list: this.goods_sign_list,
			});

			// 打印结果
			if (result.content?.[0]?.text) {
				console.log('商品推广链接生成测试成功!');
				console.log('生成结果:');
				console.log(result.content[0].text);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
	const goods_sign_list = ['E9v2C1WswZpGSg7BwtLekCH3d2V7JFX5_JQTjBciCAT'];
	const test = new GoodsPromUrlGenerateTest(goods_sign_list);
	test.runTest().catch(console.error);
}
