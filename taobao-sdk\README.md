# 淘宝开放平台SDK

淘宝开放平台API的Node.js SDK，支持ESM和CommonJS双模式导入。

## 安装

```bash
npm install taobao-topclient --save
```

## 作者联系方式:
- vx: liuliangzheng

## 使用方法

### ESM方式导入 (推荐)

```javascript
import { TopClient } from 'taobao-topclient';

const client = new TopClient({
  appkey: 'YOUR_APP_KEY',
  appsecret: 'YOUR_APP_SECRET',
  REST_URL: 'https://eco.taobao.com/router/rest'
});

// 使用回调函数调用API
client.execute('taobao.tbk.item.convert', {
  fields: 'num_iid,click_url',
  num_iids: '123456789',
  adzone_id: 'YOUR_ADZONE_ID'
}, function(error, response) {
  if (error) {
    console.error(error);
    return;
  }
  console.log(response);
});
```

### CommonJS方式导入

```javascript
const { TopClient } = require('taobao-topclient');

const client = new TopClient({
  appkey: 'YOUR_APP_KEY',
  appsecret: 'YOUR_APP_SECRET',
  REST_URL: 'https://eco.taobao.com/router/rest'
});

// 使用方式与ESM相同
```

## API参考

### 创建客户端

```javascript
const client = new TopClient({
  appkey: 'YOUR_APP_KEY',       // 必填，应用Key
  appsecret: 'YOUR_APP_SECRET', // 必填，应用Secret
  REST_URL: 'https://eco.taobao.com/router/rest' // 选填，API地址
});
```

### 调用API

```javascript
// 使用回调函数调用API
client.execute('taobao.tbk.item.convert', {
  fields: 'num_iid,click_url',
  num_iids: '123456789',
  adzone_id: 'YOUR_ADZONE_ID',
  platform: 1,
  session: 'YOUR_SESSION'
}, function(error, response) {
  if (error) {
    console.error(error);
    return;
  }
  console.log(response);
});

// 使用Promise封装回调
async function callApi() {
  try {
    const result = await new Promise((resolve, reject) => {
      client.execute('taobao.tbk.item.convert', {
        fields: 'num_iid,click_url',
        num_iids: '123456789',
        adzone_id: 'YOUR_ADZONE_ID',
        platform: 1,
        session: 'YOUR_SESSION'
      }, function(error, response) {
        if (error) {
          reject(error);
          return;
        }
        resolve(response);
      });
    });
    console.log(result);
  } catch (error) {
    console.error(error);
  }
}
```

## 常用API示例

### 链接转换

```javascript
client.execute('taobao.tbk.item.convert', {
  fields: 'num_iid,click_url',
  num_iids: '123456789',
  adzone_id: 'YOUR_ADZONE_ID',
  platform: 1
}, function(error, response) {
  if (error) {
    console.error(error);
    return;
  }
  console.log(response);
});
```

### 链接解析

```javascript
client.execute('taobao.tbk.sc.link.parse', {
  url: 'YOUR_TAOBAO_URL'
}, function(error, response) {
  if (error) {
    console.error(error);
    return;
  }
  console.log(response);
});
```

## 其他客户端

本SDK还提供以下客户端：

```javascript
// 淘宝客户端 (上述TopClient的别名)
import { ApiClient } from 'taobao-topclient';

// TMC消息客户端
import { TmcClient } from 'taobao-topclient';

// 钉钉客户端
import { DingTalkClient } from 'taobao-topclient';
```

## 版权和许可

ISC