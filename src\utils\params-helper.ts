/**
 * 参数处理工具函数
 */

/**
 * 过滤空值参数
 * @param params 原始参数
 * @returns 过滤后的参数
 */
export function filterEmptyParams(
	params: Record<string, any>
): Record<string, any> {
	const result: Record<string, any> = {};

	for (const [key, value] of Object.entries(params)) {
		// 检查值是否有效 (不为undefined、null、空字符串，如果是数组则不为空数组)
		// 同时检查字符串形式的空数组 "[]"
		if (
			value !== undefined &&
			value !== null &&
			value !== '' &&
			value !== '[]' &&
			!(Array.isArray(value) && value.length === 0)
		) {
			// 如果是对象，递归过滤
			if (
				typeof value === 'object' &&
				!Array.isArray(value) &&
				value !== null
			) {
				const filteredObj = filterEmptyParams(value);
				// 只有当过滤后的对象不为空时才添加
				if (Object.keys(filteredObj).length > 0) {
					result[key] = filteredObj;
				}
			} else {
				result[key] = value;
			}
		}
	}

	return result;
}
