/**
 * 清理 src 目录下所有 TypeScript 文件对应的 JavaScript 文件
 * 使用方法: npx tsx scripts/clean-js-files.ts
 */

import fs from 'fs';
import path from 'path';

// 源代码目录
const SRC_DIR = path.resolve(process.cwd(), 'src');

// 统计信息
let filesScanned = 0;
let filesRemoved = 0;
let errors = 0;

/**
 * 检查文件是否为 TypeScript 文件
 */
function isTypeScriptFile(filePath: string): boolean {
  return filePath.endsWith('.ts') && !filePath.endsWith('.d.ts');
}

/**
 * 获取 TypeScript 文件对应的 JavaScript 文件路径
 */
function getJavaScriptFilePath(tsFilePath: string): string {
  return tsFilePath.replace(/\.ts$/, '.js');
}

/**
 * 递归处理目录
 */
function processDirectory(dirPath: string): void {
  try {
    // 读取目录内容
    const items = fs.readdirSync(dirPath);
    
    // 遍历目录中的所有项
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = fs.statSync(itemPath);
      
      if (stats.isDirectory()) {
        // 如果是目录，递归处理
        processDirectory(itemPath);
      } else if (stats.isFile() && isTypeScriptFile(itemPath)) {
        // 如果是 TypeScript 文件，检查对应的 JavaScript 文件
        filesScanned++;
        const jsFilePath = getJavaScriptFilePath(itemPath);
        
        // 检查 JavaScript 文件是否存在
        if (fs.existsSync(jsFilePath)) {
          try {
            // 删除 JavaScript 文件
            fs.unlinkSync(jsFilePath);
            console.log(`已删除: ${jsFilePath}`);
            filesRemoved++;
            
            // 检查是否存在对应的 source map 文件
            const mapFilePath = `${jsFilePath}.map`;
            if (fs.existsSync(mapFilePath)) {
              fs.unlinkSync(mapFilePath);
              console.log(`已删除: ${mapFilePath}`);
            }
          } catch (error) {
            console.error(`删除文件失败: ${jsFilePath}`, error);
            errors++;
          }
        }
      }
    }
  } catch (error) {
    console.error(`处理目录失败: ${dirPath}`, error);
    errors++;
  }
}

// 主函数
function main() {
  console.log('开始清理 src 目录下的 JavaScript 文件...');
  console.log(`源目录: ${SRC_DIR}`);
  
  // 检查 src 目录是否存在
  if (!fs.existsSync(SRC_DIR)) {
    console.error(`错误: 源目录不存在: ${SRC_DIR}`);
    process.exit(1);
  }
  
  // 处理 src 目录
  processDirectory(SRC_DIR);
  
  // 输出统计信息
  console.log('\n清理完成!');
  console.log(`扫描的 TypeScript 文件数: ${filesScanned}`);
  console.log(`删除的 JavaScript 文件数: ${filesRemoved}`);
  
  if (errors > 0) {
    console.log(`发生错误数: ${errors}`);
    process.exit(1);
  }
}

// 执行主函数
main();
