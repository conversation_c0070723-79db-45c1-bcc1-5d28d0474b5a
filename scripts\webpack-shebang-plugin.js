/**
 * Webpack 插件：为指定的输出文件添加 shebang 行
 */
export class ShebangPlugin {
  constructor(options) {
    this.options = options || {};
    this.shebang = options.shebang || '#!/usr/bin/env node';
    this.files = options.files || ['cli.js'];
  }

  apply(compiler) {
    compiler.hooks.emit.tapAsync('ShebangPlugin', (compilation, callback) => {
      // 遍历所有输出文件
      for (const filename in compilation.assets) {
        // 检查文件名是否匹配
        if (this.files.some(file => filename.endsWith(file))) {
          const asset = compilation.assets[filename];
          const content = asset.source();
          
          // 添加 shebang 行
          const newContent = `${this.shebang}\n${content}`;
          
          // 更新资源
          compilation.assets[filename] = {
            source: () => newContent,
            size: () => newContent.length
          };
        }
      }
      
      callback();
    });
  }
}
