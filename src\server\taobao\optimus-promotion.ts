import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getRequiredEnvVar } from '../../utils/env.js';
import {
	OptimusPromotionParams,
	TaobaoApiResponse,
	OptimusPromotionResponseData,
} from './types.js';

/**
 * 淘宝客-服务商-权益物料精选API
 * 提供权益物料查询功能
 * 基于淘宝开放平台API文档：taobao.tbk.sc.optimus.promotion
 */
export class OptimusPromotion extends BaseApi {
	/**
	 * 查询权益物料
	 * @param params 权益物料精选参数
	 * @returns 原始API响应
	 */
	async getOptimusPromotion(
		params: OptimusPromotionParams
	): Promise<TaobaoApiResponse<OptimusPromotionResponseData>> {
		try {
			log(`执行权益物料精选查询: ${JSON.stringify(params)}`);

			// 从环境变量获取广告位ID和站点ID
			const adzone_id = getRequiredEnvVar('TAOBAO_ADZONE_ID');
			const site_id = getRequiredEnvVar('TAOBAO_SITE_ID');

			// 构建API参数
			const apiParams = {
				...params,
				adzone_id: Number(adzone_id),
				site_id: Number(site_id),
			};

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.sc.optimus.promotion',
				apiParams
			);

			log(`权益物料精选查询结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`权益物料精选查询失败: ${error}`);
			throw error;
		}
	}
}
