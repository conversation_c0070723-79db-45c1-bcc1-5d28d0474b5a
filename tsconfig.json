{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "rootDir": "./src", "declaration": true, "sourceMap": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"*": ["node_modules/*"]}, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"]}