import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getEnvVar } from '../../utils/env.js';
import { GoodsSearchParams } from './types.js';

/**
 * 商品搜索 API
 */
export class GoodsSearch extends BaseApi {
	/**
	 * 搜索商品
	 * @param params 搜索参数
	 * @returns 搜索结果
	 */
	async search(params: GoodsSearchParams): Promise<any> {
		try {
			// 从环境变量获取 PID，如果存在则添加到参数中
			const pid = getEnvVar('PDD_PID');

			// 调用API
			return this.callApi('pdd.ddk.oauth.goods.search', {
				...params,
				pid: pid,
			});
		} catch (error) {
			log(`商品搜索失败: ${error}`);
			throw error;
		}
	}
}
