import { log, logDebug, logError } from "../../utils/logger.js";
import { BaseApi } from "./base-api.js";
import {
  JdGoodsQueryParams,
  JdApiResponse,
  JdGoodsQueryResponseData
} from "./types.js";

/**
 * 京东商品查询API实现
 * 查询京东商品及优惠券信息，返回的结果可调用转链接口生成单品或二合一推广链接
 */
export class GoodsQuery extends BaseApi {
  /**
   * API方法名称
   * @private
   */
  private readonly METHOD_NAME = 'jd.union.open.goods.query';

  /**
   * 构造函数
   */
  constructor() {
    super();
    log('京东商品查询API初始化完成');
  }

  /**
   * 查询商品信息
   * @param params 商品查询参数
   * @returns 商品查询结果
   */
  public async queryGoods(params: Partial<JdGoodsQueryParams>): Promise<JdApiResponse<JdGoodsQueryResponseData>> {
    try {
      // 准备API参数
      const mergedParams = {
        ...this.getCommonParams(),
        siteId: this.siteId,
        ...params
      };

      // 打印日志，但过滤掉敏感数据
      const safeParams = { ...mergedParams };
      if (safeParams.key) {
        safeParams.key = `已设置(长度:${safeParams.key.length})`;
      }

      logDebug(`查询京东商品, 参数: ${JSON.stringify(safeParams)}`);

      // 验证必填参数
      this.validateParams(mergedParams);

      // 调用API（SDK会自动将参数包装到goodsReqDTO中）
      return await this.callApi(this.METHOD_NAME, mergedParams);
    } catch (error) {
      logError(`京东商品查询错误: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }

  /**
   * 验证参数
   * @param params 商品查询参数
   */
  private validateParams(params: Partial<JdGoodsQueryParams>): void {
    // 商品查询至少需要提供一个搜索条件
    const hasSearchCondition = params.keyword ||
                              params.skuIds ||
                              params.cid1 ||
                              params.cid2 ||
                              params.cid3 ||
                              params.owner;

    if (!hasSearchCondition) {
      logError('商品查询参数至少需要设置一个搜索条件，如keyword、skuIds、cid1等');
      process.exit(1);
    }

    // 验证页码参数
    if (params.pageIndex !== undefined && params.pageIndex < 1) {
      logError('页码pageIndex不能小于1');
      process.exit(1);
    }

    if (params.pageSize !== undefined && (params.pageSize < 1 || params.pageSize > 100)) {
      logError('每页条数pageSize的值必须在1-100之间');
      process.exit(1);
    }
  }
}