import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { log, logError } from "../../utils/logger.js";

/**
 * 平台服务基类
 * 为不同电商平台提供统一的服务注册接口
 */
export abstract class BaseService {
  /**
   * 平台名称，用作工具名称前缀
   */
  protected abstract readonly platformName: string;
  
  /**
   * 构造函数
   * @param mcpServer MCP服务器实例
   */
  constructor(protected readonly mcpServer: McpServer) {}
  
  /**
   * 注册该平台的所有工具
   */
  public registerTools(): void {
    log(`正在注册${this.platformName}平台工具...`);
    try {
      this.registerPlatformTools();
      log(`${this.platformName}平台工具注册完成`);
    } catch (error) {
      logError(`${this.platformName}平台工具注册失败: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
  
  /**
   * 获取带平台前缀的工具名称
   * @param toolName 工具名称
   * @returns 带前缀的完整工具名称
   */
  protected getFullToolName(toolName: string): string {
    return `${this.platformName}.${toolName}`;
  }
  
  /**
   * 注册平台特定的工具
   * 子类必须实现此方法
   */
  protected abstract registerPlatformTools(): void;
  
  /**
   * 懒加载初始化API管理器
   * 子类必须实现此方法
   */
  protected abstract initApiManager(): void;
} 