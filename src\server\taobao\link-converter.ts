import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getRequiredEnvVar } from '../../utils/env.js';
import {
	LinkConvertParams,
	TaobaoApiResponse,
	LinkConvertResponseData,
} from './types.js';

/**
 * 淘宝链接转换API
 * 提供链接转换功能
 */
export class LinkConverter extends BaseApi {
	/**
	 * 转换淘宝链接
	 * @param params 转换参数
	 * @returns 转换响应
	 */
	public async convertLink(
		params: LinkConvertParams
	): Promise<TaobaoApiResponse<LinkConvertResponseData>> {
		try {
			log(
				`执行链接转换: ${JSON.stringify({
					url: params.url,
					itemId: params.itemId,
					adzoneId: params.adzoneId,
				})}`
			);

			// 获取环境变量
			const adzoneId =
				params.adzoneId || getRequiredEnvVar('TAOBAO_ADZONE_ID');
			const siteId = getRequiredEnvVar('TAOBAO_SITE_ID');

			// 调用淘宝API
			let apiParams: any = {
				biz_scene_id: '1',
				promotion_type: '2',
				adzone_id: adzoneId,
				site_id: siteId,
			};

			// 直接使用传入的参数，只添加必要的环境变量参数
			// 将传入的参数复制到apiParams中
			Object.assign(apiParams, params);

			// 检查是否提供了必要的参数
			const hasRequiredParam = !!(
				params.material_list ||
				params.item_id_list ||
				params.seller_id_list ||
				params.page_id_list ||
				(params.material_dto && params.material_dto.length > 0) ||
				(params.item_dto && params.item_dto.length > 0) ||
				(params.shop_dto && params.shop_dto.length > 0) ||
				(params.page_dto && params.page_dto.length > 0)
			);

			if (!hasRequiredParam) {
				throw new Error(
					'必须提供至少一个有效的参数，如material_list、item_id、item_id_list等'
				);
			}

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.sc.general.link.convert',
				apiParams
			);

			// 返回响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`链接转换失败: ${error}`);
			throw error;
		}
	}
}
