/**
 * 京东联盟活动查询示例
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.activity.query
 */
require('dotenv').config();
const { JdClient } = require('../index');

// 检查环境变量
console.log('检查环境变量...');
['appkey', 'secretkey'].forEach(key => {
  console.log(`${key}: ${process.env[key] ? '已设置' : '未设置'}`);
});

console.log('\n环境变量实际值:');
console.log('appkey:', process.env.appkey);

// 创建京东客户端
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main() {
  try {
    // 构建请求参数
    const params = {
      activityReq: {
        pageIndex: 1,        // 页码，默认1
        pageSize: 10,        // 每页数量，默认20，上限50
        activityType: 1      // 活动类型，1：营销活动，2：热门主题，3：热门词，4：热门榜单，6：九块九，10：新人活动
      }
    };

    console.log('\n尝试发送的参数:');
    console.log(JSON.stringify(params, null, 2));

    // 执行请求
    const result = await client.execute('UnionOpenActivityQueryRequest', params);
    
    console.log('\n完整API响应:');
    console.log(JSON.stringify(result, null, 2));

    // 解析业务响应
    const apiResponse = result.jd_union_open_activity_query_responce;
    if (apiResponse && apiResponse.code === '0') {
      const queryResult = JSON.parse(apiResponse.queryResult);
      
      console.log('\n业务响应:');
      console.log('  状态码:', queryResult.code);
      console.log('  消息:', queryResult.message);
      
      if (queryResult.code === 200 && queryResult.data) {
        console.log('\n查询到的活动:');
        console.log('  总数:', queryResult.totalCount || '未知');
        
        queryResult.data.forEach((activity, index) => {
          console.log(`\n活动 ${index + 1}:`);
          console.log('  活动ID:', activity.id);
          console.log('  活动名称:', activity.title);
          
          if (activity.startTime && activity.endTime) {
            console.log('  活动时间:');
            console.log('    开始时间:', formatDate(activity.startTime));
            console.log('    结束时间:', formatDate(activity.endTime));
          }
          
          console.log('  活动链接:', activity.url || '无');
          
          if (activity.coverImage) {
            console.log('  封面图片:', activity.coverImage);
          }
          
          if (activity.content) {
            console.log('  活动内容:', activity.content);
          }
        });
      }
    } else {
      console.error('API调用失败:', apiResponse);
    }

  } catch (err) {
    console.error('发生错误:', err);
    console.log('请检查您的.env文件中是否设置了正确的参数。');
    console.log('查看京东联盟API文档: https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.activity.query');
  }
}

// 格式化日期
function formatDate(timestamp) {
  if (!timestamp) return '未知';
  const date = new Date(timestamp);
  return date.toLocaleString();
}

// 执行主函数
main(); 