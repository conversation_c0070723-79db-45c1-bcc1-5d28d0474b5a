import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 工具列表测试客户端
 * 用于测试获取服务端可用工具列表
 */
export class ToolsListTest {
	private client: Client;
	private transport: StdioClientTransport;
	private requestId: number = 0;

	/**
	 * 构造函数
	 */
	constructor() {
		const __filename = fileURLToPath(import.meta.url);
		const __dirname = path.dirname(__filename);

		// 计算入口文件路径
		const executable = path.resolve(__dirname, '../../out/dist/cli.js');

		// 创建transport和client
		this.transport = new StdioClientTransport({
			command: 'node',
			args: [executable, 'logs'],
			cwd: process.cwd(),
		});

		this.client = new Client({
			name: 'tools-list-test',
			version: '1.0.0',
		});
	}

	/**
	 * 连接到服务器
	 */
	private async connect(): Promise<void> {
		try {
			await this.client.connect(this.transport);
			console.log('已连接到MCP服务器');

			// 获取服务器信息
			const serverInfo = this.client.getServerVersion();
			console.log(
				`服务器信息: ${serverInfo?.name} v${serverInfo?.version}`
			);
		} catch (error) {
			console.error(
				`连接服务器失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 断开连接
	 */
	private async disconnect(): Promise<void> {
		try {
			await this.client.close();
			console.log('已断开与MCP服务器的连接');
		} catch (error) {
			console.error(
				`断开连接失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 发送请求获取工具列表
	 */
	private async getToolsList(): Promise<any> {
		try {
			console.log('请求工具列表');

			// 构造请求
			const request = {
				jsonrpc: '2.0',
				id: ++this.requestId,
				method: 'tools/list',
				params: {},
			};

			// 发送请求并等待响应
			const response = await this.sendRequest(request);
			console.log(`获取工具列表响应:`, JSON.stringify(response, null, 2));

			if (response.error) {
				throw new Error(
					`获取工具列表失败: ${JSON.stringify(response.error)}`
				);
			}

			return response.result;
		} catch (error) {
			console.error(
				`获取工具列表失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 发送请求到服务器并等待响应
	 * @param request 请求对象
	 * @returns 响应对象
	 */
	private async sendRequest(request: any): Promise<any> {
		return new Promise((resolve, reject) => {
			try {
				console.log(`发送请求: ${JSON.stringify(request)}`);

				const requestId = request.id;
				const onResponse = (response: any) => {
					if (response.id === requestId) {
						// 移除监听器
						this.transport.onmessage = undefined;
						resolve(response);
					}
				};

				// 设置响应监听器
				this.transport.onmessage = onResponse;

				// 发送请求
				this.transport.send(request);

				// 设置超时
				setTimeout(() => {
					if (this.transport.onmessage === onResponse) {
						this.transport.onmessage = undefined;
						reject(
							new Error(`请求超时: ${JSON.stringify(request)}`)
						);
					}
				}, 30000); // 30秒超时
			} catch (error) {
				reject(error);
			}
		});
	}

	/**
	 * 执行工具列表测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			// 获取工具列表
			const toolsList = await this.getToolsList();

			if (toolsList && Array.isArray(toolsList.tools)) {
				console.log('工具列表测试成功!');
				console.log(`发现 ${toolsList.tools.length} 个工具:`);

				// 打印工具详情
				toolsList.tools.forEach((tool: any, index: number) => {
					console.log(`\n工具 #${index + 1}: ${tool.name}`);
					console.log(`描述: ${tool.description || '无描述'}`);
					console.log(
						`输入模式: ${JSON.stringify(
							tool.inputSchema || {},
							null,
							2
						)}`
					);
				});

				// 处理分页
				if (toolsList.nextCursor) {
					console.log(
						`\n存在更多工具，下一页光标: ${toolsList.nextCursor}`
					);
				}
			} else {
				console.warn('响应格式不符合预期', toolsList);
			}

			// 断开连接
			await this.disconnect();
		} catch (error) {
			console.error(
				`测试运行失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			process.exit(1);
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 如果直接运行此文件，则执行测试
if (currentFilePath === entryPointPath) {
	console.log('启动工具列表测试');
	const test = new ToolsListTest();
	test.runTest().catch(console.error);
}
