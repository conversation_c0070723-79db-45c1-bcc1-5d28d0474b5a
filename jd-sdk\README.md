# 京东开放平台SDK

京东开放平台API Node.js SDK，支持京东联盟API接口。

## 安装

```bash
npm install @liuliang520500/jd-sdk --save
```

## 环境变量配置

在项目根目录创建`.env`文件，配置以下环境变量：

```
appkey=您的京东联盟AppKey
secretkey=您的京东联盟SecretKey
unionId=您的京东联盟ID
key=您的推广位的Key
siteId=您的站点ID
```

## 联系作者
- vx: liuliangzheng

## 示例

```javascript
// 引入SDK
const { JdClient } = require('@liuliang520500/jd-sdk');

// 创建客户端实例
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 调用推广位查询接口
async function queryPositions() {
  try {
    const result = await client.execute('UnionOpenPositionQueryRequest', {
      unionId: process.env.unionId,
      key: process.env.key,
      pageIndex: 1,
      pageSize: 20
    });
    console.log(result);
  } catch (error) {
    console.error('查询失败:', error);
  }
}

queryPositions();
```

## 支持的接口

- 推广位创建(UnionOpenPositionCreateRequest)
- 推广位查询(UnionOpenPositionQueryRequest)
- 订单查询(UnionOpenOrderQueryRequest)
- 订单行查询(UnionOpenOrderRowQueryRequest)
- 商品详情查询(UnionOpenGoodsQueryRequest)
- 京粉精选商品查询(UnionOpenGoodsJingfenQueryRequest)
- 获取推广链接(UnionOpenPromotionByUnionidGetRequest)
- 优惠券查询(UnionOpenCouponQueryRequest)
- 活动查询(UnionOpenActivityQueryRequest)
- 商品类目查询(UnionOpenCategoryGoodsGetRequest)

## API文档

详细API文档请参考[京东联盟开放平台](https://union.jd.com/openplatform/api)

## License

MIT

## 参考文档

- [京东联盟API文档](https://union.jd.com/openplatform/api) 