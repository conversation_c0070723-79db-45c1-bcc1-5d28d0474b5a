import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { OrderDetailGetParams } from './types.js';

/**
 * 获取订单详情 API
 */
export class OrderDetailGet extends BaseApi {
	/**
	 * 获取订单详情
	 * @param params 查询参数
	 * @returns 查询结果
	 */
	async getDetail(params: OrderDetailGetParams): Promise<any> {
		try {
			log(`获取订单详情参数: ${JSON.stringify(params)}`);

			// 调用API
			return this.callApi('pdd.ddk.oauth.order.detail.get', params);
		} catch (error) {
			log(`获取订单详情失败: ${error}`);
			throw error;
		}
	}
}
