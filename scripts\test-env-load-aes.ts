import dotenv from 'dotenv';
import { loadEnvFromNetwork } from '../src/JWT/env-loader.js';

// 加载本地环境变量
dotenv.config();

// 主函数
async function main() {
  console.log('====== 测试从网络加载JWT-AES双重加密的环境变量 ======');
  
  // 获取环境变量
  const envUrl = process.env.ENV_URL;
  const jwtSecret = process.env.ENV_SECRET || 'url:mcp.sinataoke.cn';
  const aesKey = process.env.ENV_AES_KEY || 'liuliangzhengsinataoke';
  
  console.log('环境变量 URL:', envUrl);
  console.log('JWT Secret:', jwtSecret ? '已设置' : '未设置');
  console.log('AES Key:', aesKey ? '已设置' : '未设置');
  
  if (!envUrl) {
    console.log('未设置环境变量 URL，无法从网络加载环境变量');
    return;
  }
  
  try {
    // 从网络加载环境变量
    console.log(`正在从 ${envUrl} 加载环境变量...`);
    
    // 先测试只使用JWT解密
    console.log('\n1. 测试只使用JWT解密:');
    const jwtSuccess = await loadEnvFromNetwork({
      url: envUrl,
      secret: jwtSecret,
      override: true
    });
    
    console.log('JWT解密结果:', jwtSuccess ? '成功' : '失败');
    
    // 再测试使用JWT-AES双重解密
    console.log('\n2. 测试使用JWT-AES双重解密:');
    const aesSuccess = await loadEnvFromNetwork({
      url: envUrl,
      secret: jwtSecret,
      aesKey: aesKey,
      override: true
    });
    
    console.log('JWT-AES双重解密结果:', aesSuccess ? '成功' : '失败');
    
    // 显示一些关键环境变量
    console.log('\n关键环境变量:');
    console.log('TAOBAO_APP_KEY:', process.env.TAOBAO_APP_KEY ? '已设置' : '未设置');
    console.log('TAOBAO_APP_SECRET:', process.env.TAOBAO_APP_SECRET ? '已设置' : '未设置');
    console.log('TAOBAO_SESSION:', process.env.TAOBAO_SESSION ? '已设置' : '未设置');
    console.log('TAOBAO_ADZONE_ID:', process.env.TAOBAO_ADZONE_ID ? '已设置' : '未设置');
    console.log('TAOBAO_SITE_ID:', process.env.TAOBAO_SITE_ID ? '已设置' : '未设置');
    
  } catch (error) {
    console.error('从网络加载环境变量失败:', error instanceof Error ? error.message : String(error));
  }
}

// 执行主函数
main().catch(error => {
  console.error('未处理的错误:', error);
  process.exit(1);
});
