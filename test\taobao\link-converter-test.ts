import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 链接转换测试客户端
 * 用于测试淘宝客链接转换功能
 */
export class LinkConverterTest extends BaseTestClient {
	private testUrl: string;

	/**
	 * 构造函数
	 * @param testUrl 要测试的淘宝链接
	 */
	constructor(testUrl: string) {
		super();
		this.testUrl = testUrl;
	}

	/**
	 * 执行链接转换测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			// 调用工具并获取结果
			const result = await this.callTool('taobao.convertLink', {
				material_dto: [
					{
						material_url: this.testUrl,
					},
				],
			});

			console.log(result);

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('链接转换测试成功!');
				console.log(`转换结果: ${result.content[0].text}`);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
	console.log('开始测试链接转换');
	const testUrl = 'https://s.click.taobao.com/jxLuM3s';
	const test = new LinkConverterTest(testUrl);
	test.runTest().catch(console.error);
}
