import chalk from 'chalk';
import { BaseJdTestClient } from './base-test-client.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

/**
 * 测试京东推广位创建
 */
async function testPositionCreate() {
	console.log(chalk.blue('开始测试京东推广位创建API...'));

	// 客户端不需要检查环境变量，这些环境变量是服务端从网络JWT中获取的

	const client = new BaseJdTestClient();

	try {
		// 等待服务启动
		await new Promise((resolve) => setTimeout(resolve, 2000));

		console.log(chalk.gray('正在调用京东推广位创建API...'));

		// 生成唯一的推广位名称
		const timestamp = Date.now();
		const spaceName = `test_${timestamp}`;

		// 测试推广位创建
		const result = await client.callJdTool('positionCreate', {
			unionType: 4, // 联盟后台推广位
			type: 3, // 无线站
			spaceNameList: [spaceName],
			// 不需要显式传入key和unionId，这些参数在服务端从环境变量中获取
		});

		// 打印响应结果
		console.log('推广位创建结果:');
		console.log(JSON.stringify(result, null, 2));

		// 解析返回结果
		try {
			const response = JSON.parse(result.content[0].text);

			if (response.code === 200 && response.data) {
				console.log(chalk.green('推广位创建成功!'));
				console.log('创建的推广位:');

				if (response.data.resultList) {
					// 对于对象形式的resultList，遍历键值对
					Object.entries(response.data.resultList).forEach(
						([name, id]) => {
							console.log(`  ID: ${id}, 名称: ${name}`);
						}
					);
				}

				if (response.data.pid) {
					// 对于对象形式的pid，遍历键值对
					Object.entries(response.data.pid).forEach(([name, pid]) => {
						console.log(`  名称: ${name}, PID: ${pid}`);
					});
				}
			} else {
				console.log(
					chalk.yellow(
						`请求成功，但业务处理失败: ${response.message}`
					)
				);
			}
		} catch (error) {
			console.log(
				chalk.red(
					`解析响应结果失败: ${
						error instanceof Error ? error.message : String(error)
					}`
				)
			);
		}

		console.log(chalk.green('京东推广位创建API测试完成!'));
	} catch (error) {
		console.error(chalk.red('京东推广位创建API测试失败:'), error);
		process.exit(1);
	} finally {
		// 关闭客户端
		client.close();
	}
}

// 直接执行测试函数
testPositionCreate().catch((error) => {
	console.error('测试执行失败:', error);
	process.exit(1);
});

export { testPositionCreate };
