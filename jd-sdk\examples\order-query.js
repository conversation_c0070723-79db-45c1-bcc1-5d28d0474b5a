/**
 * 订单查询示例
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.order.query
 */
require('dotenv').config();
const { JdClient } = require('../index');

// 检查环境变量
console.log('检查环境变量...');
['appkey', 'secretkey', 'unionId', 'key'].forEach(key => {
  console.log(`${key}: ${process.env[key] ? '已设置' : '未设置'}`);
});

console.log('\n环境变量实际值:');
console.log('appkey:', process.env.appkey);
console.log('unionId:', process.env.unionId);

// 创建京东客户端
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main() {
  try {
    // 构建请求参数
    const params = {
      orderReq: {
        pageNo: 1,                     // 页码
        pageSize: 20,                  // 每页数量
        type: 1,                       // 订单时间查询类型(1：下单时间)
        time: getTimeString(),         // 查询时间，如：202404
        childUnionId: process.env.unionId, // 子推客unionID
        key: process.env.key           // 授权key
      }
    };

    console.log('\n尝试发送的参数:');
    console.log(JSON.stringify(params, null, 2));

    // 执行请求
    const result = await client.execute('UnionOpenOrderQueryRequest', params);
    
    console.log('\n完整API响应:');
    console.log(JSON.stringify(result, null, 2));

    // 解析业务响应
    const apiResponse = result.jd_union_open_order_query_responce;
    if (apiResponse && apiResponse.code === '0') {
      const queryResult = JSON.parse(apiResponse.queryResult);
      
      console.log('\n业务响应:');
      console.log('  状态码:', queryResult.code);
      console.log('  消息:', queryResult.message);
      
      if (queryResult.code === 200 && queryResult.data) {
        console.log('\n查询到的订单:');
        console.log('  总数:', queryResult.data.totalCount);
        
        if (queryResult.data.hasNext !== undefined) {
          console.log('  是否有下一页:', queryResult.data.hasNext ? '是' : '否');
        }
        
        if (queryResult.data.orderList && queryResult.data.orderList.length > 0) {
          queryResult.data.orderList.forEach((order, index) => {
            console.log(`\n订单 ${index + 1}:`);
            console.log('  订单号:', order.orderId);
            console.log('  下单时间:', order.orderTime);
            console.log('  订单状态:', getOrderStatusString(order.validCode));
            console.log('  订单金额:', order.estimateFee);
            console.log('  预估佣金:', order.estimateCosPrice);
            console.log('  下单用户ID:', order.customerId);
            
            if (order.skuList && order.skuList.length > 0) {
              console.log('  订单包含商品:');
              order.skuList.forEach((sku, skuIndex) => {
                console.log(`    商品 ${skuIndex + 1}:`);
                console.log('      商品ID:', sku.skuId);
                console.log('      商品名称:', sku.skuName);
                console.log('      商品数量:', sku.skuNum);
                console.log('      商品单价:', sku.price);
                console.log('      商品佣金:', sku.estimateFee);
              });
            }
          });
        } else {
          console.log('  没有查询到订单数据');
        }
      }
    } else {
      console.error('API调用失败:', apiResponse);
    }

  } catch (err) {
    console.error('发生错误:', err);
    console.log('请检查您的.env文件中是否设置了正确的参数。');
    console.log('查看京东联盟API文档: https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.order.query');
  }
}

/**
 * 获取订单状态说明
 * @param {Number} validCode 订单状态码
 * @returns {String} 订单状态说明
 */
function getOrderStatusString(validCode) {
  switch (Number(validCode)) {
    case 2:
      return '无效-拆单';
    case 3:
      return '无效-取消';
    case 4:
      return '无效-京东帮帮主订单';
    case 5:
      return '无效-账号异常';
    case 6:
      return '无效-赠品类目不返佣';
    case 7:
      return '无效-校园订单';
    case 8:
      return '无效-企业订单';
    case 9:
      return '无效-团购订单';
    case 10:
      return '无效-开增值税专用发票订单';
    case 11:
      return '无效-乡村推广员下单';
    case 12:
      return '无效-自己推广自己下单';
    case 13:
      return '无效-违规订单';
    case 14:
      return '无效-来源与备案网址不符';
    case 15:
      return '待付款';
    case 16:
      return '已付款';
    case 17:
      return '已完成';
    case 18:
      return '已结算';
    case 19:
      return '无效-拒收';
    case 20:
      return '无效-退货';
    case 21:
      return '无效-自动取消';
    case 22:
      return '无效-订单来源与备案网址不符';
    case 23:
      return '无效-非双方约定渠道';
    case 24:
      return '无效-商家帮帮主订单';
    case 25:
      return '无效-京东处理中';
    default:
      return '未知状态(' + validCode + ')';
  }
}

/**
 * 获取查询时间字符串 (当前年月)
 * @returns {string} 时间字符串，格式：yyyyMM
 */
function getTimeString() {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  return `${year}${month < 10 ? '0' + month : month}`;
}

// 执行主函数
main(); 