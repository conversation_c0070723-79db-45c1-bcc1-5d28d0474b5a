# 淘宝/京东 MCP 服务

基于 Model Context Protocol (MCP) 的淘宝和京东链接转换和查询服务。

## 功能特性

- 🔄 淘宝链接转换：将普通淘宝链接转换为带返佣的推广链接
- 🔍 淘宝链接解析：解析淘宝链接或淘口令中的商品信息
- 🎯 活动转换：转换淘宝活动页为推广活动页
- 📊 订单查询：查询淘宝联盟订单信息
- 🔍 京东推广位查询：查询京东联盟推广位信息

## 系统架构

项目采用单一入口设计，基于MCP协议标准实现，主要包含以下几个核心组件：

```
+---------------------+       +---------------------+
|                     |       |                     |
|   McpServerManager  |------>|  McpServer          |
|   (服务器管理器)     |       |  (MCP服务器)        |
|                     |       |                     |
+----------+----------+       +---------+-----------+
           |                            |
           |                            |
           v                            v
+----------+----------+       +---------+-----------+
|                     |       |                     |
|     ApiManagers     |<------|  MCP Tools          |
|  (延迟初始化API管理器)|       |  (工具函数)         |
|                     |       |                     |
+-----+-----+-----+---+       +---------------------+
      |     |     |
      |     |     |
      v     v     v
+-----+--+ +--+---+--+ +------------+
|        | |         | |            |
|  链接   | |  活动    | |  订单查询   |
|  API   | |  API    | |   API      |
|        | |         | |            |
+--------+ +---------+ +------------+
```

### 核心组件说明

1. **McpServerManager**：单一服务入口，负责初始化MCP服务器和注册所有工具。
2. **McpServer**：MCP协议的服务器实现，处理JSON-RPC请求。
3. **ApiManagers**：API管理器集合，采用延迟初始化模式，只有在实际调用时才创建对应的API实例。
4. **MCP Tools**：所有工具函数的定义和实现，以平台为前缀命名（如taobao.convertLink）。
5. **各种API处理器**：如LinkConverter、ActivityConverter等，负责具体API的调用和处理。

### 工具流程

```
+--------+           +--------+           +--------+
|        |           |        |           |        |
|  LLM   |           | Client |           | Server |
|        |           |        |           |        |
+---+----+           +---+----+           +---+----+
    |                    |                    |
    |                    |    发现工具         |
    |                    |------------------->|
    |                    |                    |
    |                    |    工具列表         |
    |                    |<-------------------|
    |                    |                    |
    |   选择工具          |                    |
    |<-------------------|                    |
    |                    |                    |
    |  决定调用某个工具    |                    |
    |------------------->|                    |
    |                    |                    |
    |                    |   调用工具          |
    |                    |------------------->|
    |                    |                    |
    |                    |                    |
    |                    |                    |
    |                    |   动态初始化        |
    |                    |   对应平台API       |
    |                    |                    |
    |                    |                    |
    |                    |    工具结果         |
    |                    |<-------------------|
    |                    |                    |
    |    处理结果         |                    |
    |<-------------------|                    |
    |                    |                    |
+---+----+           +---+----+           +---+----+
|        |           |        |           |        |
|  LLM   |           | Client |           | Server |
|        |           |        |           |        |
+--------+           +--------+           +--------+
```

### 架构优势

这种架构有以下优点：
- **符合MCP规范**：严格遵循MCP协议标准，客户端通过标准方式发现和调用工具
- **单一入口**：统一的服务管理入口，简化部署和维护
- **延迟初始化**：API客户端只在实际需要时才创建，提高资源利用率
- **易于扩展**：添加新平台只需注册新工具和对应的API管理器
- **代码清晰**：职责划分明确，每个组件只负责自己的功能

## 安装方法

### 方法一：NPM全局安装

```bash
npm install -g taobao-mcp
```

### 方法二：使用 npx 直接运行

```bash
npx taobao-mcp
```

### 方法三：作为项目依赖安装

```bash
npm install taobao-mcp --save
```

## 环境配置

在运行服务前，需要设置以下环境变量，可以通过 `.env` 文件或直接在系统中设置：

```bash
# 淘宝开放平台应用信息
TAOBAO_APP_KEY=你的淘宝应用Key
TAOBAO_APP_SECRET=你的淘宝应用Secret
TAOBAO_SESSION=你的淘宝会话Token
TAOBAO_ADZONE_ID=你的广告位ID
TAOBAO_SITE_ID=你的站点ID

# 京东开放平台应用信息
JD_APP_KEY=你的京东应用Key
JD_APP_SECRET=你的京东应用Secret
JD_KEY=你的京东联盟授权key
JD_UNION_ID=你的京东联盟ID
JD_SITE_ID=你的京东站点ID

# 服务器配置（可选）
MCP_SERVER_PORT=3000
```

## 使用方法

### 作为命令行工具使用

全局安装后，直接运行：

```bash
taobao-mcp
```

或者使用 npx：

```bash
npx taobao-mcp
```

### 作为库在代码中使用

```javascript
import { startServer } from 'taobao-mcp';

// 启动服务器
startServer().then(() => {
  console.log('淘宝MCP服务已启动');
}).catch(err => {
  console.error('服务启动失败:', err);
});
```

## API功能说明

### 淘宝API

#### 1. 链接转换 (taobao.convertLink)

将普通淘宝商品链接转换为带返佣的推广链接。

```javascript
// 示例请求
{
  "jsonrpc": "2.0",
  "id": 123,
  "method": "tools/call",
  "params": {
    "name": "taobao.convertLink",
    "arguments": {
      "url": "https://item.taobao.com/item.htm?id=123456789"
    }
  }
}
```

### 2. 链接解析 (taobao.parseLink)

解析淘宝链接或淘口令中的商品信息。

```javascript
// 示例请求
{
  "jsonrpc": "2.0",
  "id": 123,
  "method": "tools/call",
  "params": {
    "name": "taobao.parseLink",
    "arguments": {
      "url": "￥ABCDEFg￥"
    }
  }
}
```

### 3. 活动转换 (taobao.convertActivity)

转换淘宝活动页为推广活动页。

```javascript
// 示例请求
{
  "jsonrpc": "2.0",
  "id": 123,
  "method": "tools/call",
  "params": {
    "name": "taobao.convertActivity",
    "arguments": {
      "activityId": "12345678"
    }
  }
}
```

### 4. 订单查询 (taobao.getOrderDetails)

查询淘宝联盟订单信息。

```javascript
// 示例请求
{
  "jsonrpc": "2.0",
  "id": 123,
  "method": "tools/call",
  "params": {
    "name": "taobao.getOrderDetails",
    "arguments": {
      "startTime": "2023-01-01 00:00:00",
      "endTime": "2023-01-02 00:00:00",
      "queryType": 1,
      "pageNo": 1,
      "pageSize": 20
    }
  }
}
```

### 京东API

#### 1. 推广位查询 (jd.positionQuery)

查询京东联盟推广位信息。

```javascript
// 示例请求
{
  "jsonrpc": "2.0",
  "id": 123,
  "method": "tools/call",
  "params": {
    "name": "jd.positionQuery",
    "arguments": {
      "unionType": 3,
      "pageIndex": 1,
      "pageSize": 20
    }
  }
}
```

## 本地开发

### 1. 安装依赖

```bash
npm install
```

### 2. 开发环境运行

```bash
npm run dev
```

### 3. 构建项目

```bash
# 使用 TypeScript 编译（开发）
npm run build

# 使用 webpack 打包（生产）
npm run build:webpack
```

### 4. 运行测试

```bash
# 运行所有测试
npm test

# 运行单个测试
npm run test:link-convert
npm run test:link-parse
npm run test:activity-convert
npm run test:order-details
```

## 关于MCP

Model Context Protocol (MCP) 是一种用于AI模型与外部上下文交互的协议。通过MCP，AI模型可以访问外部工具、数据和服务，扩展其能力范围。

### MCP流程说明

1. **发现阶段**：
   - 客户端通过 `tools/list` 请求获取服务器支持的所有工具列表
   - 服务器返回所有已注册工具的名称、描述和参数定义

2. **选择阶段**：
   - LLM（大型语言模型）根据用户需求和任务上下文，从工具列表中选择合适的工具

3. **调用阶段**：
   - 客户端通过 `tools/call` 请求调用选定的工具
   - 服务器执行对应的工具函数，按需初始化相关API客户端
   - 服务器将结果返回给客户端

4. **处理阶段**：
   - LLM处理工具执行结果，生成回应或执行后续操作

本项目实现了基于MCP的淘宝服务，使AI模型能够直接进行淘宝链接转换、解析和查询操作。

更多关于MCP的信息，请访问：[Model Context Protocol 官网](https://modelcontextprotocol.io)

## 许可证

ISC

## 开发者文档

### 淘宝SDK发布

项目依赖的淘宝SDK已封装为npm包，您可以按照以下步骤发布：

1. 进入SDK目录
```bash
cd nodejs
```

2. 发布到npm (确保已登录npm)
```bash
npm login
npm publish
```

3. 发布后，可直接在项目中使用
```bash
npm install taobao-topclient --save
```

### SDK使用示例
```javascript
// ESM方式 (现代方式，推荐)
import { TopClient } from 'taobao-topclient';

// CommonJS方式 (传统方式)
const { TopClient } = require('taobao-topclient');

// 创建客户端
const client = new TopClient({
  appkey: process.env.TAOBAO_APP_KEY,
  appsecret: process.env.TAOBAO_APP_SECRET,
  REST_URL: 'https://eco.taobao.com/router/rest'
});

// 调用API
const result = await client.execute({
  method: 'taobao.tbk.item.convert',
  fields: 'num_iid,click_url',
  num_iids: 'item_id',
  adzone_id: process.env.TAOBAO_ADZONE_ID,
  platform: 1
});
``` 