import { log, logDebug } from '../../utils/logger.js';
import { TopClient } from '@liuliang520500/taobao-topclient';
import { getEnvVar } from '../../utils/env.js';
import { filterEmptyParams } from '../../utils/params-helper.js';

/**
 * 淘宝API基础类
 * 提供通用的API调用和响应处理方法
 */
export class BaseApi {
	protected client: any;

	constructor() {
		// 初始化淘宝客户端
		this.client = new TopClient({
			appkey: getEnvVar('TAOBAO_APP_KEY'),
			appsecret: getEnvVar('TAOBAO_APP_SECRET'),
			REST_URL: 'http://gw.api.taobao.com/router/rest',
		});
	}

	/**
	 * 调用淘宝API并返回原始响应
	 * @param method API方法名
	 * @param params API参数
	 * @returns 原始API响应
	 */
	protected async callApi(method: string, params: any): Promise<any> {
		try {
			// 获取当前日期并格式化为淘宝API所需的格式
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');
			const day = String(now.getDate()).padStart(2, '0');
			const hours = String(now.getHours()).padStart(2, '0');
			const minutes = String(now.getMinutes()).padStart(2, '0');
			const seconds = String(now.getSeconds()).padStart(2, '0');
			const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

			// 过滤空参数
			const filteredParams = filterEmptyParams(params);

			// 处理数组和对象参数，将它们转换为JSON字符串
			const processedParams: Record<string, any> = {};
			for (const [key, value] of Object.entries(filteredParams)) {
				if (Array.isArray(value)) {
					// 如果是数组，转换为JSON字符串
					processedParams[key] = JSON.stringify(value);
				} else if (typeof value === 'object' && value !== null) {
					// 如果是对象，转换为JSON字符串
					processedParams[key] = JSON.stringify(value);
				} else {
					// 其他类型直接保留
					processedParams[key] = value;
				}
			}

			// 添加公共参数
			const apiParams = {
				...processedParams,
				session: getEnvVar('TAOBAO_SESSION'),
				format: 'json',
				v: '2.0',
				sign_method: 'md5',
				timestamp: formattedDate,
			};

			logDebug(
				`调用淘宝API: ${method}, 参数: ${JSON.stringify(apiParams)}`
			);

			// 使用Promise包装原始的回调式API
			return new Promise((resolve, reject) => {
				this.client.execute(
					method,
					apiParams,
					(error: any, response: any) => {
						if (error) {
							reject(error);
							return;
						}
						resolve(response);
					}
				);
			});
		} catch (error) {
			log(`淘宝API调用失败: ${error}`);
			throw error;
		}
	}

	/**
	 * 处理API响应，返回原始数据
	 * @param response API响应
	 * @returns 原始响应数据
	 */
	protected handleApiResponse(response: any): any {
		if (!response) {
			throw new Error('API响应为空');
		}

		// 检查错误响应
		if (response.error_response) {
			throw new Error(`API错误: ${response.error_response.msg}`);
		}

		// 返回原始响应数据
		return response;
	}
}
