import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getEnvVar } from '../../utils/env.js';

/**
 * 淘宝活动转换API
 * 提供活动转换功能
 */
export class ActivityConverter extends BaseApi {
	/**
	 * 转换活动链接
	 * @param params 活动转换参数
	 * @returns 原始API响应
	 */
	public async convertActivity(params: any): Promise<any> {
		try {
			log(`执行活动转换: ${JSON.stringify(params)}`);

			// 构建API参数，添加必要的环境变量参数
			const apiParams: Record<string, any> = {
				...params,
				adzone_id: getEnvVar('TAOBAO_ADZONE_ID'),
				site_id: getEnvVar('TAOBAO_SITE_ID'),
			};

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.sc.activity.info.get',
				apiParams
			);

			log(`活动转换结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`活动转换失败: ${error}`);
			throw error;
		}
	}
}
