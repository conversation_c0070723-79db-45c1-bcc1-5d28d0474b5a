/**
 * 联盟商品类目查询API
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.category.goods.get
 */
const { BaseApi } = require('../api');

/**
 * 联盟商品类目查询API请求类
 * 提供查询京东商品类目信息的接口
 */
class UnionOpenCategoryGoodsGetRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.category.goods.get';
  }

  /**
   * 执行商品类目查询请求
   * @param {Object} params 请求参数
   * @param {Object} params.req 类目查询请求对象
   * @param {Number} [params.req.parentId] 父类目id(一级类目为0)
   * @param {Number} [params.req.grade] 类目级别(类目级别 0，1，2 代表一、二、三级类目)
   * @returns {Promise<Object>} 请求结果
   */
  async get(params = {}) {
    if (!params.req) {
      throw new Error('req不能为空');
    }

    const apiParams = {
      req: this.cleanParams(params.req)
    };

    return this.execute(apiParams);
  }

  /**
   * 清理参数，移除空值
   * @param {Object} params 原始参数
   * @returns {Object} 清理后的参数
   */
  cleanParams(params) {
    const cleanedParams = {};
    
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        cleanedParams[key] = params[key];
      }
    }
    
    return cleanedParams;
  }
}

module.exports = {
  UnionOpenCategoryGoodsGetRequest
}; 