/**
 * 环境变量工具函数
 * 提供环境变量获取和验证功能
 */

import { log, logError } from './logger.js';

/**
 * 获取必需的环境变量，如果不存在则抛出错误
 * @param name 环境变量名称
 * @returns 环境变量值
 * @throws 如果环境变量未设置则抛出错误
 */
export function getRequiredEnvVar(name: string): string {
  const value = process.env[name];
  if (!value) {
    const errorMsg = `环境变量 ${name} 未设置`;
    log(errorMsg);
    logError(errorMsg);
    process.exit(1);
  }
  return value;
}

/**
 * 获取可选的环境变量，如果不存在则返回默认值
 * @param name 环境变量名称
 * @param defaultValue 默认值
 * @returns 环境变量值或默认值
 */
export function getEnvVar(name: string, defaultValue: string = ''): string {
  return process.env[name] || defaultValue;
}

/**
 * 检查必需的环境变量是否都已设置
 * @param names 环境变量名称数组
 * @returns 是否所有环境变量都已设置
 * @throws 如果任一环境变量未设置则抛出错误
 */
export function checkRequiredEnvVars(names: string[]): boolean {
  for (const name of names) {
    getRequiredEnvVar(name);
  }
  return true;
}

/**
 * 获取淘宝API所需的所有环境变量
 * @returns 包含所有淘宝API环境变量的对象
 */
export function getTaobaoEnvVars() {
  return {
    appKey: getRequiredEnvVar('TAOBAO_APP_KEY'),
    appSecret: getRequiredEnvVar('TAOBAO_APP_SECRET'),
    session: getRequiredEnvVar('TAOBAO_SESSION'),
    adzoneId: getRequiredEnvVar('TAOBAO_ADZONE_ID'),
    siteId: getRequiredEnvVar('TAOBAO_SITE_ID')
  };
}