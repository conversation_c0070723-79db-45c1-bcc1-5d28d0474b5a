import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { ItemInfoParams, TaobaoApiResponse } from './types.js';

/**
 * 淘宝客-公用-淘宝客商品详情查询(简版)API
 * 提供根据商品ID查询商品详情的功能
 */
export class ItemInfo extends BaseApi {
	/**
	 * 查询商品详情
	 * @param numIids 商品ID串，用英文逗号分隔，最大支持40个
	 * @param platform 链接形式：1：PC，2：无线，默认：1
	 * @param ip ip地址，影响邮费获取
	 * @param bizSceneId 场景ID，1-动态ID转链场景，2-消费者比价场景，3-商品库导购场景（不填默认为1）
	 * @param promotionType 推广类型，1-自购省，2-推广赚（代理模式专属ID，代理模式必填，非代理模式不用填写）
	 * @param relationId 渠道关系ID
	 * @param manageItemPubId 商品管理类目ID
	 * @returns 原始API响应
	 */
	async getItemInfo(
		numIids: string,
		platform?: number,
		ip?: string,
		bizSceneId?: string,
		promotionType?: string,
		relationId?: string,
		manageItemPubId?: number
	): Promise<any> {
		try {
			log(
				`执行商品详情查询: numIids=${numIids}, platform=${platform}, ip=${ip}, bizSceneId=${bizSceneId}, promotionType=${promotionType}, relationId=${relationId}, manageItemPubId=${manageItemPubId}`
			);

			// 构建API参数
			const apiParams: Record<string, any> = {
				num_iids: numIids,
			};

			// 映射其他可选参数
			if (platform !== undefined) apiParams.platform = platform;
			if (ip !== undefined) apiParams.ip = ip;
			if (bizSceneId !== undefined) apiParams.biz_scene_id = bizSceneId;
			if (promotionType !== undefined)
				apiParams.promotion_type = promotionType;
			if (relationId !== undefined) apiParams.relation_id = relationId;
			if (manageItemPubId !== undefined)
				apiParams.manage_item_pub_id = manageItemPubId;

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.item.info.get',
				apiParams
			);

			log(`商品详情查询结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`商品详情查询失败: ${error}`);
			throw error;
		}
	}
}
