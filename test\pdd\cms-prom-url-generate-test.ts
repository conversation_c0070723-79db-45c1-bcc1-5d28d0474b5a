import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 商城推广链接生成测试客户端
 * 用于测试拼多多商城推广链接生成功能
 */
export class CmsPromUrlGenerateTest extends BaseTestClient {
  private keyword: string;

  /**
   * 构造函数
   * @param keyword 搜索关键词
   */
  constructor(keyword: string) {
    super();
    this.keyword = keyword;
  }

  /**
   * 执行商城推广链接生成测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.cms.prom.url", {
        keyword: this.keyword,
        generate_short_url: true
      });

      // 验证响应
      if (result.content?.[0]?.text) {
        console.log('商城推广链接生成测试成功!');
        console.log(`生成结果: ${result.content[0].text}`);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const testKeyword = '女装';
  const test = new CmsPromUrlGenerateTest(testKeyword);
  test.runTest().catch(console.error);
} 