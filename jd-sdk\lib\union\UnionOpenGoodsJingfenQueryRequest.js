/**
 * 联盟京粉精选商品查询API
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.goods.jingfen.query
 */
const { BaseApi } = require('../api');

/**
 * 联盟京粉精选商品查询API请求类
 * 提供京东联盟推荐的精选商品查询接口
 */
class UnionOpenGoodsJingfenQueryRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.goods.jingfen.query';
  }

  /**
   * 执行京粉精选商品查询请求
   * @param {Object} params 请求参数
   * @param {Object} params.goodsReq 商品查询请求对象
   * @param {Number} [params.goodsReq.eliteId] 频道ID，1-好券商品，2-京粉APP.大咖推荐，3-小程序-好券商品，4-京粉APP-热销爆品
   * @param {Number} [params.goodsReq.pageIndex] 页码，默认1
   * @param {Number} [params.goodsReq.pageSize] 每页数量，默认20
   * @param {Number} [params.goodsReq.sortName] 排序字段(price：单价, commissionShare：佣金比例, commission：佣金，inOrderCount30Days：30天引单量)
   * @param {String} [params.goodsReq.sort] 排序方式(asc:升序,desc:降序)
   * @param {Number} [params.goodsReq.pid] 联盟id_应用id_推广位id
   * @param {String} [params.goodsReq.fields] 支持出参数据筛选，逗号分隔，目前可用：videoInfo,documentInfo
   * @param {Number} [params.goodsReq.forbidTypes] 10微信京东购物小程序禁售，11微信京喜小程序禁售
   * @returns {Promise<Object>} 请求结果
   */
  async query(params = {}) {
    if (!params.goodsReq) {
      throw new Error('goodsReq不能为空');
    }

    const apiParams = {
      goodsReq: this.cleanParams(params.goodsReq)
    };

    return this.execute(apiParams);
  }

  /**
   * 清理参数，移除空值
   * @param {Object} params 原始参数
   * @returns {Object} 清理后的参数
   */
  cleanParams(params) {
    const cleanedParams = {};
    
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        cleanedParams[key] = params[key];
      }
    }
    
    return cleanedParams;
  }
}

module.exports = {
  UnionOpenGoodsJingfenQueryRequest
}; 