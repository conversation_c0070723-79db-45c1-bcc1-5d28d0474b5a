/**
 * 拼多多多多进宝转链API
 * 接口名称：pdd.ddk.oauth.goods.zs.unit.url.gen
 */

const BaseApi = require('../core/base-api');

class GoodsZsUnitUrlGenerate extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 生成多多进宝转链接
   * 本功能适用于采集群场景，将其他推广者的推广链接转为自己的；通过此api，可以将他人的招商推广链接，转换成自己的招商推广链接。
   * @param {Object} options 生成参数
   * @param {String} options.pid 推广位ID
   * @param {String} options.sourceUrl 需要转链的链接
   * @param {Boolean} [options.generateSchemaUrl] 是否生成schema URL
   * @param {Boolean} [options.generateWeAppInfo] 是否生成小程序信息
   * @param {Boolean} [options.generateShortUrl] 是否生成短链接
   * @param {Boolean} [options.generateWeAppLongLink] 是否生成小程序长链接
   * @returns {Promise} Promise对象
   */
  async generate(options = {}) {
    // 必要参数检查
    if (!options.pid) {
      throw new Error('推广位ID (pid) 不能为空');
    }
    
    if (!options.sourceUrl) {
      throw new Error('需要转链的链接 (sourceUrl) 不能为空');
    }
    
    // 由于拼多多API对参数名称有特殊要求，手动处理参数名称
    const apiParams = {};
    
    // 必填参数
    apiParams.pid = options.pid;
    apiParams.source_url = options.sourceUrl;
    
    // 可选参数
    if (options.generateSchemaUrl !== undefined) {
      apiParams.generate_schema_url = options.generateSchemaUrl;
    }
    
    if (options.generateWeAppInfo !== undefined) {
      apiParams.generate_we_app = options.generateWeAppInfo;
    }
    
    if (options.generateShortUrl !== undefined) {
      apiParams.generate_short_url = options.generateShortUrl;
    }
    
    if (options.generateWeAppLongLink !== undefined) {
      apiParams.generate_we_app_long_link = options.generateWeAppLongLink;
    }
    
    // 执行请求，不转换参数，因为已手动处理
    return this.execute('pdd.ddk.oauth.goods.zs.unit.url.gen', apiParams, false);
  }
}

module.exports = GoodsZsUnitUrlGenerate; 