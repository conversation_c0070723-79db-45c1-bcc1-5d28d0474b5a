import { BaseJdTestClient } from './base-test-client.js';
import dotenv from 'dotenv';
import chalk from 'chalk';

// 加载环境变量
dotenv.config();

/**
 * 测试京东优惠券查询API
 */
async function testCouponQuery() {
	console.log(chalk.blue('开始测试京东优惠券查询API...'));

	// 客户端不需要检查环境变量，这些环境变量是服务端从网络JWT中获取的

	console.log(chalk.yellow('\n正在调用京东优惠券查询API...'));

	const client = new BaseJdTestClient();

	try {
		// 使用提供的京东精粉优惠券链接
		const couponUrls = [
			'https://jingfen.jd.com/item.html?sku=751e21CmFHIM2XN7SiUljfoN_3vbTdJlOaFZ4sgyHbj&q=EHATFRVtE3IRExFmIDUWSh03FS9HShQuRzIbSx0zQC8QUEc3Ey8VShE4FyMUJRRnFHEUEhdtEUFldHokSi1RF0wqIHEREBNmE3EREBRf&needRecommendFlag=1&d=aGBOQs1&cu=true&utm_source=lianmeng__10__kong&utm_medium=tuiguang&utm_campaign=t_1000793546_&utm_term=d1e006ec2aa0408794c9b1e4cfb4f882#/pages/common-coupon/common-coupon',
		];

		const response = await client.callJdTool('coupon.query', {
			couponUrls,
		});

		console.log(chalk.green('\n京东优惠券查询响应:'));
		console.log(response);

		const responseData = response.content?.[0]?.text;
	} catch (error) {
		console.error(chalk.red('\n测试失败:'), error);
	} finally {
		client.close();
	}
}

// 直接执行测试
testCouponQuery().catch(console.error);
