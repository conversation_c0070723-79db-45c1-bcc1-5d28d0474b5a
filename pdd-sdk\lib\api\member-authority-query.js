/**
 * 拼多多会员备案查询API
 * 接口名称：pdd.ddk.oauth.member.authority.query
 */

const BaseApi = require('../core/base-api');

class MemberAuthorityQuery extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 查询会员是否绑定备案
   * 通过pid和自定义参数来查询是否已经绑定备案
   * @param {Object} options 查询参数
   * @param {String} options.pid 推广位ID
   * @returns {Promise} Promise对象
   */
  async query(options = {}) {
    // 必要参数检查
    if (!options.pid) {
      throw new Error('推广位ID (pid) 不能为空');
    }
    
    // 构建API参数
    const apiParams = {
      pid: options.pid
    };
    
    // 执行请求
    return this.execute('pdd.ddk.oauth.member.authority.query', apiParams);
  }
}

module.exports = MemberAuthorityQuery; 