import { BaseApi } from './base-api.js';
import { log, logError } from '../../utils/logger.js';
import { getRequiredEnvVar } from '../../utils/env.js';

/**
 * 淘宝客-服务商-创建推广者位API
 * 提供创建淘宝推广位的功能
 */
export class CreatePromotionZone extends BaseApi {
	/**
	 * 创建淘宝推广位
	 * @param params 推广位参数，包含 adzone_name
	 * @returns 原始API响应
	 */
	async createPromotionZone(params: { adzone_name: string }): Promise<any> {
		try {
			log(`执行创建推广位: ${JSON.stringify(params)}`);

			// 从环境变量获取网站ID
			const site_id = getRequiredEnvVar('TAOBAO_SITE_ID');

			// 调用淘宝API
			const result = await this.callApi('taobao.tbk.sc.adzone.create', {
				site_id,
				adzone_name: params.adzone_name,
			});

			log(`创建推广位结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			logError(`创建推广位失败: ${error instanceof Error ? error.message : String(error)}`);
			process.exit(1);
		}
	}
}
