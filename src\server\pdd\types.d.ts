declare module '@liuliang520500/pdd-sdk' {
  export interface PddClientConfig {
    clientId: string;
    clientSecret: string;
    accessToken?: string;
    debug?: boolean;
  }

  export interface RequestOptions {
    type: string;
    data: Record<string, any>;
  }

  export class PddClient {
    constructor(config: PddClientConfig);
    
    execute(method: string, params: Record<string, any>): Promise<any>;
    generateSign(params: Record<string, any>): string;

    goodsDetail: {
      getDetail(options: Record<string, any>): Promise<any>;
    };

    goodsSearch: {
      search(options: Record<string, any>): Promise<any>;
    };

    oauth: {
      generatePid(options: Record<string, any>): Promise<any>;
      queryPid(options: Record<string, any>): Promise<any>;
      generatePromUrl(options: Record<string, any>): Promise<any>;
      zsUnitUrlGen(options: Record<string, any>): Promise<any>;
    };

    order: {
      getDetail(options: Record<string, any>): Promise<any>;
    };
  }
} 