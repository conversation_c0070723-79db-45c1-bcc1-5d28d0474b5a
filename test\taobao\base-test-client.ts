import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';
import path from 'path';
import { fileURLToPath } from 'url';

/**
 * 基础测试客户端
 */
export class BaseTestClient {
	protected client: Client;
	protected transport: StdioClientTransport;

	/**
	 * 构造函数
	 */
	constructor() {
		const __filename = fileURLToPath(import.meta.url);
		const __dirname = path.dirname(__filename);

		// 计算入口文件路径 - 修复路径，指向项目根目录下的dist
		const executable = path.resolve(__dirname, '../../out/dist/cli.js');

		// 创建MCP客户端
		this.client = new Client({
			name: 'TaobaoClientTest',
			version: '1.0.0',
		});

		// 创建标准输入输出传输层
		this.transport = new StdioClientTransport({
			command: 'node',
			args: [executable, 'logs'],
			cwd: process.cwd(),
		});
	}

	/**
	 * 连接到服务器
	 */
	async connect(): Promise<void> {
		try {
			console.log('连接到MCP服务器...');
			await this.client.connect(this.transport);
			console.log('已连接到MCP服务器');

			// 获取服务器信息
			const serverInfo = this.client.getServerVersion();
			console.log(
				`服务器信息: ${serverInfo?.name} v${serverInfo?.version}`
			);
		} catch (error) {
			console.error(
				`连接服务器失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 断开与服务器的连接
	 */
	async disconnect(): Promise<void> {
		try {
			console.log('正在断开与MCP服务器的连接...');
			await this.client.close();
			console.log('已断开与MCP服务器的连接');
		} catch (error) {
			console.error(
				`断开服务器连接失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 调用工具
	 * @param toolName 工具名称
	 * @param params 工具参数
	 * @returns 工具调用结果
	 */
	async callTool(
		toolName: string,
		params: Record<string, any>
	): Promise<any> {
		try {
			console.log(`调用工具 ${toolName}: ${JSON.stringify(params)}`);

			// 构造请求
			const request = {
				jsonrpc: '2.0',
				id: Date.now(),
				method: 'tools/call',
				params: {
					name: toolName,
					arguments: params,
				},
			};

			// 发送请求并等待响应
			const response = await this.sendRequest(request);
			console.log(
				`工具 ${toolName} 返回结果: ${JSON.stringify(response)}`
			);

			if (response.error) {
				throw new Error(
					`工具调用返回错误: ${JSON.stringify(response.error)}`
				);
			}

			return response.result;
		} catch (error) {
			console.error(
				`工具调用失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}

	/**
	 * 发送请求到服务器并等待响应
	 * @param request 请求对象
	 * @returns 响应对象
	 */
	private async sendRequest(request: any): Promise<any> {
		return new Promise((resolve, reject) => {
			try {
				console.log(`发送请求: ${JSON.stringify(request)}`);

				const requestId = request.id;
				const onResponse = (response: any) => {
					if (response.id === requestId) {
						// 移除监听器
						this.transport.onmessage = undefined;
						resolve(response);
					}
				};

				// 设置响应监听器
				this.transport.onmessage = onResponse;

				// 发送请求
				this.transport.send(request);

				// 设置超时
				setTimeout(() => {
					if (this.transport.onmessage === onResponse) {
						this.transport.onmessage = undefined;
						reject(
							new Error(`请求超时: ${JSON.stringify(request)}`)
						);
					}
				}, 30000); // 30秒超时
			} catch (error) {
				reject(error);
			}
		});
	}
}
