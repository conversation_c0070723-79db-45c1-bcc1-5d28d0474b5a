import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 按照更新时间段增量同步推广订单信息测试客户端
 * 用于测试拼多多按照更新时间段增量同步推广订单信息功能
 */
export class OrderListIncrementGetTest extends BaseTestClient {
  /**
   * 执行按照更新时间段增量同步推广订单信息测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 计算时间范围（最近24小时）
      const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
      const oneDayAgo = now - 24 * 60 * 60; // 24小时前的时间戳（秒）

      // 调用工具并获取结果
      const result = await this.callTool("pdd.order.list.increment.get", {
        start_update_time: oneDayAgo,
        end_update_time: now
      });

      // 打印结果
      if (result.content?.[0]?.text) {
        console.log('按照更新时间段增量同步推广订单信息测试成功!');
        console.log('查询结果:');
        console.log(result.content[0].text);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const test = new OrderListIncrementGetTest();
  test.runTest().catch(console.error);
}
