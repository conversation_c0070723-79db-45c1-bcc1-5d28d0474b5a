/**
 * 联盟优惠券查询API
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.coupon.query
 */
const { BaseApi } = require('../api');

/**
 * 联盟优惠券查询API请求类
 * 查询优惠券的平台、面额、期限、可用状态、剩余数量等详细信息
 */
class UnionOpenCouponQueryRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.coupon.query';
  }

  /**
   * 执行优惠券查询请求
   * @param {Object} params 请求参数
   * @param {Array<string>} params.couponUrls 优惠券链接集合
   * @returns {Promise<Object>} 请求结果
   */
  async query(params = {}) {
    if (!params.couponUrls || !Array.isArray(params.couponUrls) || params.couponUrls.length === 0) {
      throw new Error('couponUrls不能为空且必须为数组');
    }

    const apiParams = {
      couponUrls: params.couponUrls
    };

    return this.execute(apiParams);
  }
}

module.exports = {
  UnionOpenCouponQueryRequest
}; 