import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 商品搜索测试客户端
 * 用于测试拼多多商品搜索功能
 */
export class GoodsSearchTest extends BaseTestClient {
	private keyword: string;

	/**
	 * 构造函数
	 * @param keyword 搜索关键词
	 */
	constructor(keyword: string) {
		super();
		this.keyword = keyword;
	}

	/**
	 * 执行商品搜索测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			// 调用工具并获取结果
			const result = await this.callTool('pdd.goods.search', {
				keyword: this.keyword,
			});

			// 打印结果
			if (result.content?.[0]?.text) {
				console.log('商品搜索测试成功!');
				console.log('搜索结果:');
				console.log(result.content[0].text);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
	const testKeyword = '飞机杯';
	const test = new GoodsSearchTest(testKeyword);
	test.runTest().catch(console.error);
}
