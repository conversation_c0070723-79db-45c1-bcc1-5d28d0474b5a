import { LinkConverter } from '../../src/server/taobao/link-converter.js';
import { LinkConvertParams } from '../../src/server/taobao/types.js';
import dotenv from 'dotenv';
import chalk from 'chalk';

// 加载环境变量
dotenv.config();

/**
 * 测试更新后的链接转换API
 */
async function testLinkConverter() {
  console.log(chalk.blue('测试更新后的链接转换API...'));
  
  try {
    // 创建实例
    const converter = new LinkConverter();
    
    // 测试商品URL转换功能
    const urlParams: LinkConvertParams = {
      url: 'https://detail.tmall.com/item.htm?id=681287356047',
      adzoneId: process.env.TAOBAO_ADZONE_ID
    };
    
    console.log(`URL转换参数: ${JSON.stringify(urlParams, null, 2)}`);
    const urlResult = await converter.convertLink(urlParams);
    console.log(`URL转换结果: ${JSON.stringify(urlResult, null, 2)}`);
    
    // 测试商品ID转换功能
    const itemParams: LinkConvertParams = {
      itemId: '681287356047',
      adzoneId: process.env.TAOBAO_ADZONE_ID
    };
    
    console.log(`ItemID转换参数: ${JSON.stringify(itemParams, null, 2)}`);
    const itemResult = await converter.convertLink(itemParams);
    console.log(`ItemID转换结果: ${JSON.stringify(itemResult, null, 2)}`);
    
    console.log(chalk.green('链接转换API测试成功!'));
  } catch (error) {
    console.error(chalk.red('链接转换API测试失败:'), error);
    process.exit(1);
  }
}

// 执行测试
testLinkConverter().catch(console.error);

export { testLinkConverter }; 