'use strict';

/**
 * 拼多多开放平台SDK
 * 支持CommonJS和ESM双模式导入
 */

const PddClient = require('./lib/client');
const BaseApi = require('./lib/core/base-api');
const GoodsDetail = require('./lib/api/goods-detail');
const GoodsPidGenerate = require('./lib/api/goods-pid-generate');
const GoodsPidQuery = require('./lib/api/goods-pid-query');
const GoodsPromUrlGenerate = require('./lib/api/goods-prom-url-generate');
const GoodsSearch = require('./lib/api/goods-search');
const GoodsZsUnitUrlGenerate = require('./lib/api/goods-zs-unit-url-generate');
const MemberAuthorityQuery = require('./lib/api/member-authority-query');
const OrderDetail = require('./lib/api/order-detail');
const ResourceUrlGenerate = require('./lib/api/resource-url-generate');

// 导出客户端和API类
module.exports = {
    PddClient,
    BaseApi,
    GoodsDetail,
    GoodsPidGenerate,
    GoodsPidQuery,
    GoodsPromUrlGenerate,
    GoodsSearch,
    GoodsZsUnitUrlGenerate,
    MemberAuthorityQuery,
    OrderDetail,
    ResourceUrlGenerate
}; 