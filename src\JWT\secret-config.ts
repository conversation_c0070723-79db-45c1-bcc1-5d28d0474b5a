/**
 * Secret 配置模块
 * 用于获取和解析 SERVER_SECRET、issuer 和 audience
 */

import axios from 'axios';
import { logError, log } from '../utils/logger.js';

/**
 * Secret 配置接口
 */
export interface SecretConfig {
	/** 服务器密钥 */
	serverSecret: string;
	/** JWT 签发者 */
	issuer: string;
	/** JWT 接收者 */
	audience: string;
}

/**
 * 从远程服务器获取 Secret 配置
 * @returns Promise<SecretConfig> Secret 配置
 */
export async function fetchSecretConfig(): Promise<SecretConfig> {

	// 发送请求获取数据
	const response = await axios.get(
		'https://config.sinataoke.cn/api/mcp/key'
	);

	if (response.status !== 200) {
		logError(`请求失败，状态码: ${response.status}`)
		process.exit(1);
	}

	if (
		typeof response.data !== 'object' ||
		!response.data ||
		!('data' in response.data)
	) {
		logError('响应数据格式不正确')
		process.exit(1);
	}

	// 获取数据
	const secretData = response.data.data;

	// log(secretData)

	if (typeof secretData !== 'string' || !secretData.trim()) {
		logError('获取到的 Secret 数据无效')
		process.exit(1);
	}

	// 解析数据
	const parts = secretData.split('|');

	if (parts.length !== 3) {
		logError(`Secret 数据格式不正确，期望 3 个部分，实际 ${parts.length} 个部分`)
		process.exit(1);
	}

	const [serverSecret, issuer, audience] = parts;

	return {
		serverSecret,
		issuer,
		audience,
	};

}
/**
 * 将 Secret 配置设置到环境变量中
 * @param config Secret 配置
 */
export function setSecretConfigToEnv(config: SecretConfig): void {
	try {
		// 设置环境变量
		process.env.SERVER_SECRET = config.serverSecret;
		process.env.JWT_ISSUER = config.issuer;
		process.env.JWT_AUDIENCE = config.audience;
	} catch (error) {
		logError(
			`设置 Secret 配置到环境变量失败: ${error instanceof Error ? error.message : String(error)
			}`
		);
		process.exit(1);
	}
}

/**
 * 获取并设置 Secret 配置
 * @returns Promise<SecretConfig> Secret 配置
 */
export async function initSecretConfig(): Promise<SecretConfig> {
	try {
		// 获取 Secret 配置
		const config = await fetchSecretConfig();

		// 设置到环境变量中
		setSecretConfigToEnv(config);

		return config;
	} catch (error) {
		logError(
			`初始化 Secret 配置失败: ${error instanceof Error ? error.message : String(error)
			}`
		);
		process.exit(1);
	}
}
