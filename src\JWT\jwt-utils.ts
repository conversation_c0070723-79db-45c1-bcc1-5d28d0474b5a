/**
 * JWT工具模块
 * 提供JWT令牌的加密和解密功能，以及AES加密和解密功能
 */

import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { log, logError } from '../utils/logger.js';

/**
 * JWT配置选项
 */
interface JwtOptions {
	/**
	 * 密钥
	 */
	secret: string;

	/**
	 * 过期时间（秒）
	 */
	expiresIn?: number | string;

	/**
	 * 签发者
	 */
	issuer?: string;

	/**
	 * 接收者
	 */
	audience?: string;
}

/**
 * 默认JWT配置
 */
const DEFAULT_OPTIONS: JwtOptions = {
	secret: process.env.JWT_SECRET || 'url:mcp.sinataoke.cn',
	expiresIn: '30d', // 默认30天过期
	issuer: process.env.JWT_ISSUER || 'mcp-server',
	audience: process.env.JWT_AUDIENCE || 'mcp-client',
};

/**
 * 生成JWT令牌
 * @param payload 要加密的数据
 * @param options JWT配置选项
 * @returns JWT令牌字符串
 */
export function generateToken(
	payload: any,
	options: Partial<JwtOptions> = {}
): string {
	try {
		const jwtOptions = { ...DEFAULT_OPTIONS, ...options };

		const token = jwt.sign(payload, jwtOptions.secret, {
			expiresIn: jwtOptions.expiresIn,
			issuer: jwtOptions.issuer,
			audience: jwtOptions.audience,
		});

		return token;
	} catch (error) {
		logError(
			`生成JWT令牌失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		throw error;
	}
}

/**
 * 验证并解析JWT令牌
 * @param token JWT令牌字符串
 * @param options JWT配置选项
 * @returns 解析后的数据
 */
export function verifyToken(
	token: string,
	options: Partial<JwtOptions> = {}
): any {
	try {
		const jwtOptions = { ...DEFAULT_OPTIONS, ...options };

		try {
			// 先尝试使用完整验证
			const decoded = jwt.verify(token, jwtOptions.secret, {
				issuer: jwtOptions.issuer,
				audience: jwtOptions.audience,
			});

			return decoded;
		} catch (verifyError) {
			// 如果验证失败，尝试不验证 audience 和 issuer
			if (
				verifyError instanceof Error &&
				verifyError.message.includes('audience invalid')
			) {
				log('不验证 audience 和 issuer，重新尝试解析 JWT 令牌');
				const decoded = jwt.verify(token, jwtOptions.secret, {
					ignoreExpiration: false, // 仍然验证过期时间
				});

				return decoded;
			}

			// 其他错误直接抛出
			throw verifyError;
		}
	} catch (error) {
		logError(
			`验证JWT令牌失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		throw error;
	}
}

/**
 * 解析JWT令牌（不验证签名）
 * @param token JWT令牌字符串
 * @returns 解析后的数据
 */
export function decodeToken(token: string): any {
	try {
		const decoded = jwt.decode(token);
		return decoded;
	} catch (error) {
		logError(
			`解析JWT令牌失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		throw error;
	}
}

/**
 * AES加密配置选项
 */
interface AesOptions {
	/**
	 * 密钥
	 */
	key: string;

	/**
	 * 初始化向量
	 */
	iv?: Buffer;

	/**
	 * 算法
	 */
	algorithm?: string;

	/**
	 * 输出编码
	 */
	outputEncoding?: BufferEncoding;
}

/**
 * 默认AES配置
 */
const DEFAULT_AES_OPTIONS: Partial<AesOptions> = {
	algorithm: 'aes-256-cbc',
	outputEncoding: 'base64',
};

/**
 * 使用AES加密数据
 * @param data 要加密的数据
 * @param options AES配置选项
 * @returns 加密后的数据
 */
export function encryptWithAes(data: string, options: AesOptions): string {
	try {
		const mergedOptions = { ...DEFAULT_AES_OPTIONS, ...options };

		// 确保密钥长度正确（AES-256需要32字节密钥）
		const key = crypto.createHash('sha256').update(options.key).digest();

		// 如果没有提供IV，则生成一个随机IV
		const iv = options.iv || crypto.randomBytes(16);

		// 创建加密对象
		const cipher = crypto.createCipheriv(
			mergedOptions.algorithm as string,
			key,
			iv
		);

		// 加密数据
		let encrypted = cipher.update(
			data,
			'utf8',
			mergedOptions.outputEncoding as BufferEncoding
		);
		encrypted += cipher.final(
			mergedOptions.outputEncoding as BufferEncoding
		);

		// 将IV和加密数据拼接在一起
		const ivString = iv.toString('hex');
		return `${ivString}:${encrypted}`;
	} catch (error) {
		logError(
			`AES加密失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		throw error;
	}
}

/**
 * 使用AES解密数据
 * @param encryptedData 加密后的数据
 * @param options AES配置选项
 * @returns 解密后的数据
 */
export function decryptWithAes(
	encryptedData: string,
	options: AesOptions
): string {
	try {
		const mergedOptions = { ...DEFAULT_AES_OPTIONS, ...options };

		// 分离IV和加密数据
		const [ivString, encrypted] = encryptedData.split(':');
		if (!ivString || !encrypted) {
			throw new Error('加密数据格式不正确');
		}

		// 将IV从十六进制字符串转换回Buffer
		const iv = Buffer.from(ivString, 'hex');

		// 确保密钥长度正确（AES-256需要32字节密钥）
		const key = crypto.createHash('sha256').update(options.key).digest();

		// 创建解密对象
		const decipher = crypto.createDecipheriv(
			mergedOptions.algorithm as string,
			key,
			iv
		);

		// 解密数据
		let decrypted = decipher.update(
			encrypted,
			mergedOptions.outputEncoding as BufferEncoding,
			'utf8'
		);
		decrypted += decipher.final('utf8');

		return decrypted;
	} catch (error) {
		logError(
			`AES解密失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		throw error;
	}
}

/**
 * 使用JWT和AES双重加密数据
 * @param payload 要加密的数据
 * @param jwtSecret JWT密钥
 * @param aesKey AES密钥
 * @param jwtOptions JWT配置选项
 * @returns 双重加密后的数据
 */
export function encryptWithJwtAndAes(
	payload: any,
	jwtSecret: string,
	aesKey: string,
	jwtOptions: Partial<JwtOptions> = {}
): string {
	try {
		// 先使用JWT加密
		const jwtToken = generateToken(payload, {
			secret: jwtSecret,
			...jwtOptions,
		});

		// 再使用AES加密
		const encryptedData = encryptWithAes(jwtToken, { key: aesKey });

		return encryptedData;
	} catch (error) {
		logError(
			`JWT-AES双重加密失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		throw error;
	}
}

/**
 * 使用AES和JWT双重解密数据
 * @param encryptedData 加密后的数据
 * @param jwtSecret JWT密钥
 * @param aesKey AES密钥
 * @param jwtOptions JWT配置选项
 * @returns 解密后的数据
 */
export function decryptWithAesAndJwt(
	encryptedData: string,
	jwtSecret: string,
	aesKey: string,
	jwtOptions: Partial<JwtOptions> = {}
): any {
	try {
		// 先使用AES解密
		const jwtToken = decryptWithAes(encryptedData, { key: aesKey });

		// 再使用JWT解密
		const payload = verifyToken(jwtToken, {
			secret: jwtSecret,
			...jwtOptions,
		});

		return payload;
	} catch (error) {
		logError(
			`AES-JWT双重解密失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		throw error;
	}
}
