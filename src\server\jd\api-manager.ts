import { log, logError } from '../../utils/logger.js';
import { PositionQuery } from './position-query.js';
import { PositionCreate } from './position-create.js';
import { PromotionByUnionid } from './promotion-by-unionid.js';
import { GoodsQuery } from './goods-query.js';
import { CouponQuery } from './coupon-query.js';

/**
 * 京东API管理器
 * 管理京东联盟所有API实例
 */
export class JdApiManager {
	/**
	 * 推广位查询API
	 */
	public readonly positionQuery: PositionQuery;

	/**
	 * 推广位创建API
	 */
	public readonly positionCreate: PositionCreate;

	/**
	 * 转推广链接API
	 */
	public readonly promotionByUnionid: PromotionByUnionid;

	/**
	 * 商品查询API
	 */
	public readonly goodsQuery: GoodsQuery;

	/**
	 * 优惠券查询API
	 */
	public readonly couponQuery: CouponQuery;

	/**
	 * 构造函数
	 * 初始化所有API实例
	 */
	constructor() {
		log('创建京东API管理器');

		// 初始化推广位查询API
		try {
			this.positionQuery = new PositionQuery();
			log('推广位查询API初始化成功');
		} catch (error) {
			logError(
				`推广位查询API初始化失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}

		// 初始化推广位创建API
		try {
			this.positionCreate = new PositionCreate();
			log('推广位创建API初始化成功');
		} catch (error) {
			logError(
				`推广位创建API初始化失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}

		// 初始化转推广链接API
		try {
			this.promotionByUnionid = new PromotionByUnionid();
			log('转推广链接API初始化成功');
		} catch (error) {
			logError(
				`转推广链接API初始化失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}

		// 初始化商品查询API
		try {
			this.goodsQuery = new GoodsQuery();
			log('商品查询API初始化成功');
		} catch (error) {
			logError(
				`商品查询API初始化失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}

		// 初始化优惠券查询API
		try {
			this.couponQuery = new CouponQuery();
			log('优惠券查询API初始化成功');
		} catch (error) {
			logError(
				`优惠券查询API初始化失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}

		// 后续可添加更多API实例
	}
}
