import { BaseJdTestClient } from './base-test-client.js';
import dotenv from 'dotenv';
import chalk from 'chalk';

// 加载环境变量
dotenv.config();

/**
 * 测试京东转推广链接API
 */
async function testPromotionByUnionid() {
	console.log(chalk.blue('开始测试京东转推广链接API...'));

	// 客户端不需要检查环境变量，这些环境变量是服务端从网络JWT中获取的

	console.log(chalk.yellow('\n正在调用京东转推广链接API...'));

	const client = new BaseJdTestClient();

	try {
		// 使用提供的京东短链接作为materialId
		const materialId = 'https://u.jd.com/aOSIETS'; // 用户提供的京东商品链接

		const response = await client.callJdTool('promotion.byunionid.get', {
			materialId,
			positionId: 3003896132, // 测试推广位ID，可按需修改
			chainType: 3, // 生成长链接和短链接
		});

		console.log(chalk.green('\n京东转推广链接响应:'));
		console.log(response);

		const responseData = response.content?.[0]?.text;
		if (responseData) {
			try {
				const parsed = JSON.parse(responseData);

				if (parsed.code === 200 && parsed.data) {
					console.log(chalk.green('\n转推广链接生成成功!'));
					console.log('生成的推广链接:');
					if (parsed.data.clickURL) {
						console.log(`长链接: ${parsed.data.clickURL}`);
					}
					if (parsed.data.shortURL) {
						console.log(`短链接: ${parsed.data.shortURL}`);
					}
					console.log('\n京东转推广链接API测试成功!');
				} else {
					console.error(chalk.red('\n转推广链接生成失败:'), parsed);
					process.exit(1);
				}
			} catch (e) {
				console.error(chalk.red('\n解析响应数据失败:'), e);
				console.error('原始响应:', responseData);
				process.exit(1);
			}
		} else {
			console.error(chalk.red('\n响应格式错误，找不到有效内容'));
			process.exit(1);
		}
	} catch (error) {
		console.error(chalk.red('\n测试失败:'), error);
		process.exit(1);
	} finally {
		client.close();
	}
}

// 直接执行测试
testPromotionByUnionid().catch(console.error);
