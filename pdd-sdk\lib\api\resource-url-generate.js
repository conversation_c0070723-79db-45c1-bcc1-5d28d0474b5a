/**
 * 拼多多主站频道推广API
 * 接口名称：pdd.ddk.oauth.resource.url.gen
 */

const BaseApi = require('../core/base-api');

class ResourceUrlGenerate extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 生成主站频道推广链接
   * @param {Object} options 生成参数
   * @param {String} options.pid 推广位
   * @param {Boolean} [options.generate_schema_url] 是否返回schema URL
   * @param {Boolean} [options.generate_we_app] 是否生成小程序推广
   * @returns {Promise} Promise对象
   */
  async generate(options = {}) {
    // 必要参数检查
    if (!options.pid) {
      throw new Error('推广位 (pid) 不能为空');
    }
    
    // 构建API参数
    const apiParams = {
      pid: options.pid,
      generate_schema_url: options.generate_schema_url,
      generate_we_app: options.generate_we_app
    };
    
    // 执行请求
    return this.execute('pdd.ddk.oauth.resource.url.gen', apiParams);
  }
}

module.exports = ResourceUrlGenerate; 