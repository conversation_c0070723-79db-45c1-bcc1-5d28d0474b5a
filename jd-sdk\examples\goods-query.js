/**
 * 商品查询示例
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.goods.query
 */
require('dotenv').config();
const { JdClient } = require('../index');

// 检查环境变量
console.log('检查环境变量...');
['appkey', 'secretkey'].forEach(key => {
  console.log(`${key}: ${process.env[key] ? '已设置' : '未设置'}`);
});

console.log('\n环境变量实际值:');
console.log('appkey:', process.env.appkey);

// 创建京东客户端
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main() {
  try {
    // 构建请求参数
    const params = {
      goodsReqDTO: {
        // 查询关键词
        keyword: '手机',
        // 页码
        pageIndex: 1,
        // 每页数量
        pageSize: 10,
        
        // 可选参数：
        // 排序字段(price：单价, 
        // commissionShare：佣金比例, 
        // commission：佣金， 
        // inOrderCount30Days：30天引单量, 
        // inOrderComm30Days：30天支出佣金)
        sortName: 'inOrderCount30Days',
        
        // 排序方式(asc：升序, desc：降序)
        sort: 'desc',
        
        // 是否是优惠券商品，1：有优惠券，0：无优惠券
        isCoupon: 1,
        
        // 是否是自营商品，1：自营商品，0：非自营商品
        isSelf: 1,
        
        // 商品价格下限
        pricefrom: 1000,
        
        // 商品价格上限
        priceto: 3000,
        
        // 佣金比例下限
        commissionShareFrom: 10,
        
        // skuId集合
        // skuIds: [1, 2, 3],
        
        // 品牌ID
        // brandCode: 123456,
        
        // 店铺ID
        // shopId: 123456,
        
        // 商品类型：自定义分类
        // owner: 'g',
        
        // cid1
        // cid1: 1,
        
        // cid2
        // cid2: 2,
        
        // cid3
        // cid3: 3,
        
        // 主商品ID
        // pid: 123456,
        
        // 京喜商品类型
        // jxFlags: [1, 2],
        
        // 支持频道
        // deliveryType: 'mall'
      }
    };

    console.log('\n尝试发送的参数:');
    console.log(JSON.stringify(params, null, 2));

    // 执行请求
    const result = await client.execute('UnionOpenGoodsQueryRequest', params);
    
    console.log('\n完整API响应:');
    console.log(JSON.stringify(result, null, 2));

    // 解析业务响应
    const apiResponse = result.jd_union_open_goods_query_responce;
    if (apiResponse && apiResponse.code === '0') {
      const queryResult = JSON.parse(apiResponse.queryResult);
      
      console.log('\n业务响应:');
      console.log('  状态码:', queryResult.code);
      console.log('  消息:', queryResult.message);
      
      if (queryResult.code === 200 && queryResult.data) {
        console.log('\n查询到的商品:');
        queryResult.data.forEach((item, index) => {
          console.log(`\n商品 ${index + 1}:`);
          console.log('  商品ID:', item.skuId);
          console.log('  商品名称:', item.skuName);
          console.log('  商品价格:', item.priceInfo?.price);
          console.log('  佣金比例:', item.commissionInfo?.commissionShare + '%');
          console.log('  商品链接:', item.materialUrl);
          
          if (item.couponInfo && item.couponInfo.couponList && item.couponInfo.couponList.length > 0) {
            console.log('  优惠券信息:');
            item.couponInfo.couponList.forEach((coupon, cidx) => {
              console.log(`    优惠券 ${cidx + 1}: ${coupon.discount}元 (${coupon.quota}元起)，有效期至${coupon.endTime}`);
            });
          }
        });
      }
    } else {
      console.error('API调用失败:', apiResponse);
    }

  } catch (err) {
    console.error('发生错误:', err);
    console.log('请检查您的.env文件中是否设置了正确的参数。');
    console.log('查看京东联盟API文档: https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.goods.query');
  }
}

// 执行主函数
main(); 