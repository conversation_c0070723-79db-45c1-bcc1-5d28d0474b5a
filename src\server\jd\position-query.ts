import { log, logError } from "../../utils/logger.js";
import { BaseApi } from "./base-api.js";
import { 
  JdPositionQueryParams, 
  JdApiResponse, 
  JdPositionQueryResponseData,
  JdUnionType
} from "./types.js";

/**
 * 京东推广位查询API
 */
export class PositionQuery extends BaseApi {
  /**
   * 构造函数
   */
  constructor() {
    super();
    log('京东推广位查询API初始化完成');
  }

  /**
   * 查询推广位信息
   * @param params 查询参数
   * @returns 查询结果
   */
  async query(params: JdPositionQueryParams): Promise<JdApiResponse<JdPositionQueryResponseData>> {
    try {
      // 准备查询参数
      const queryParams: JdPositionQueryParams = {
        ...this.getCommonParams(),
        unionId: params.unionId || this.unionId,
        key: params.key || this.key,
        unionType: params.unionType,
        pageIndex: params.pageIndex || 1,
        pageSize: params.pageSize || 20
      };
      
      log(`查询京东推广位, 参数:
      unionId: ${queryParams.unionId}
      key: ${queryParams.key ? '已设置(长度:' + queryParams.key.length + ')' : '未设置'}
      unionType: ${queryParams.unionType}
      pageIndex: ${queryParams.pageIndex}
      pageSize: ${queryParams.pageSize}
      `);
      
      // 验证参数
      this.validateParams(queryParams);
      
      // 使用基类方法调用API
      return await this.callApi('jd.union.open.position.query', queryParams);
    } catch (error) {
      logError(`京东推广位查询错误: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
  
  /**
   * 验证参数
   * @param params 查询参数
   */
  private validateParams(params: JdPositionQueryParams): void {
    // 检查必填参数
    if (!params.key) {
      throw new Error('业务参数key是必填的');
    }
    
    if (!params.unionId) {
      throw new Error('业务参数unionId是必填的');
    }
    
    // 验证unionType的值是否有效
    if (params.unionType !== JdUnionType.PRIVATE && params.unionType !== JdUnionType.UNION) {
      throw new Error(`业务参数unionType的值无效，应为${JdUnionType.PRIVATE}(私域推广位)或${JdUnionType.UNION}(联盟后台推广位)`);
    }
    
    // 验证分页参数
    if (params.pageIndex !== undefined && params.pageIndex < 1) {
      throw new Error('业务参数pageIndex必须大于等于1');
    }
    
    if (params.pageSize !== undefined && (params.pageSize < 1 || params.pageSize > 100)) {
      throw new Error('业务参数pageSize必须在1-100之间');
    }
  }
}