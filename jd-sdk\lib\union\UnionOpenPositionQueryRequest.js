/**
 * 推广位查询API
 * 接口文档: https://union.jd.com/openplatform/api/10423
 */

const { BaseApi } = require('../api');

/**
 * 推广位查询请求
 */
class UnionOpenPositionQueryRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.position.query';
  }

  /**
   * 查询推广位
   * @param {Object} positionReq 查询请求参数
   * @param {number} positionReq.unionId 联盟ID
   * @param {string} positionReq.key 授权key，必填参数
   * @param {number} positionReq.unionType 联盟推广位类型，3：私域推广位，4：联盟后台推广位，必填
   * @param {number} [positionReq.pageIndex] 页码，上限100
   * @param {number} [positionReq.pageSize] 每页条数，上限100
   * @returns {Promise<Object>} 推广位查询结果
   */
  async query(positionReq) {
    console.log('UnionOpenPositionQueryRequest.query调用:');
    console.log('- 原始参数:', JSON.stringify({
      ...positionReq,
      key: positionReq && positionReq.key ? `已设置(长度:${positionReq.key.length})` : '未设置'
    }, null, 2));
    
    // 如果传入的是内部包装的对象，提取positionReq
    if (positionReq && positionReq.positionReq) {
      console.log('- 发现嵌套的positionReq对象，进行提取');
      positionReq = positionReq.positionReq;
    }
    
    // 确保positionReq是一个对象
    positionReq = positionReq || {};
    
    // 尝试从各种可能的来源获取key
    if (!positionReq.key) {
      if (process.env.JD_KEY) {
        console.log('- 从环境变量JD_KEY获取key值');
        positionReq.key = process.env.JD_KEY;
      } else if (process.env.key) {
        console.log('- 从环境变量key获取key值');
        positionReq.key = process.env.key;
      }
    }
    
    // 尝试从各种可能的来源获取unionId
    if (!positionReq.unionId) {
      if (process.env.JD_UNION_ID) {
        console.log('- 从环境变量JD_UNION_ID获取unionId值');
        positionReq.unionId = Number(process.env.JD_UNION_ID);
      } else if (process.env.unionId) {
        console.log('- 从环境变量unionId获取unionId值');
        positionReq.unionId = Number(process.env.unionId);
      }
    }
    
    console.log('- 处理后的参数:', JSON.stringify({
      unionId: positionReq.unionId,
      key: positionReq.key ? `已设置(长度:${positionReq.key.length})` : '未设置',
      unionType: positionReq.unionType,
      pageIndex: positionReq.pageIndex,
      pageSize: positionReq.pageSize
    }, null, 2));
    
    // 检查必填参数
    if (!positionReq.key) {
      throw new Error('业务参数key是必填的，请在.env文件中设置key参数');
    }
    
    if (!positionReq.unionId) {
      throw new Error('业务参数unionId是必填的，请在.env文件中设置unionId参数');
    }
    
    if (!positionReq.unionType) {
      throw new Error('业务参数unionType是必填的，应为3或4');
    }
    
    // 验证unionType的值是否有效
    if (positionReq.unionType !== 3 && positionReq.unionType !== 4) {
      throw new Error('业务参数unionType的值无效，应为3(私域推广位)或4(联盟后台推广位)');
    }
    
    return this.execute({ positionReq });
  }
}

module.exports = {
  UnionOpenPositionQueryRequest
}; 