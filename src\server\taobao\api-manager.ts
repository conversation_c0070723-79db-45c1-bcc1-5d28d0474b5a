import { LinkConverter } from './link-converter.js';
import { LinkParser } from './link-parser.js';
import { ActivityConverter } from './activity-converter.js';
import { OrderDetails } from './order-details.js';
import { SpreadRequests } from './spread-requests.js';
import { CreatePromotionZone } from './create-promotion-zone.js';
import { TpwdCreate } from './tpwd-create.js';
import { MaterialSearchUpgrade } from './material-search-upgrade.js';
import { PunishOrder } from './punish-order.js';
import { ItemInfo } from './item-info.js';
import { OptimusPromotion } from './optimus-promotion.js';
import { VegasSendReport } from './vegas-send-report.js';
import { log } from '../../utils/logger.js';

/**
 * 淘宝API管理器
 * 统一管理所有淘宝API处理器实例
 */
export class TaobaoApiManager {
	private _linkConverter: LinkConverter;
	private _linkParser: LinkParser;
	private _activityConverter: ActivityConverter;
	private _orderDetails: OrderDetails;
	private _spreadRequests: SpreadRequests;
	private _createPromotionZone: CreatePromotionZone;
	private _tpwdCreate: TpwdCreate;
	private _materialSearchUpgrade: MaterialSearchUpgrade;
	private _punishOrder: PunishOrder;
	private _itemInfo: ItemInfo;
	private _optimusPromotion: OptimusPromotion;
	private _vegasSendReport: VegasSendReport;

	/**
	 * 构造函数
	 * 初始化所有API处理器
	 */
	constructor() {
		log('初始化淘宝API处理器');
		this._linkConverter = new LinkConverter();
		this._linkParser = new LinkParser();
		this._activityConverter = new ActivityConverter();
		this._orderDetails = new OrderDetails();
		this._spreadRequests = new SpreadRequests();
		this._createPromotionZone = new CreatePromotionZone();
		this._tpwdCreate = new TpwdCreate();
		this._materialSearchUpgrade = new MaterialSearchUpgrade();
		this._punishOrder = new PunishOrder();
		this._itemInfo = new ItemInfo();
		this._optimusPromotion = new OptimusPromotion();
		this._vegasSendReport = new VegasSendReport();
	}

	/**
	 * 获取链接转换器
	 */
	get linkConverter(): LinkConverter {
		return this._linkConverter;
	}

	/**
	 * 获取链接解析器
	 */
	get linkParser(): LinkParser {
		return this._linkParser;
	}

	/**
	 * 获取活动转换器
	 */
	get activityConverter(): ActivityConverter {
		return this._activityConverter;
	}

	/**
	 * 获取订单详情查询
	 */
	get orderDetails(): OrderDetails {
		return this._orderDetails;
	}

	/**
	 * 获取批量链接转换器
	 */
	get spreadRequests(): SpreadRequests {
		return this._spreadRequests;
	}

	/**
	 * 获取创建推广位API
	 */
	get createPromotionZone(): CreatePromotionZone {
		return this._createPromotionZone;
	}

	/**
	 * 获取淘口令生成API
	 */
	get tpwdCreate(): TpwdCreate {
		return this._tpwdCreate;
	}

	/**
	 * 获取物料搜索升级版API
	 */
	get materialSearchUpgrade(): MaterialSearchUpgrade {
		return this._materialSearchUpgrade;
	}

	/**
	 * 获取处罚订单查询API
	 */
	get punishOrder(): PunishOrder {
		return this._punishOrder;
	}

	/**
	 * 获取商品详情查询API
	 */
	get itemInfo(): ItemInfo {
		return this._itemInfo;
	}

	/**
	 * 获取权益物料精选API
	 */
	get optimusPromotion(): OptimusPromotion {
		return this._optimusPromotion;
	}

	/**
	 * 获取红包发放个数查询API
	 */
	get vegasSendReport(): VegasSendReport {
		return this._vegasSendReport;
	}
}
