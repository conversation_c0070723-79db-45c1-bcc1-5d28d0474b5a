import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { TaobaoApiManager } from '../../taobao/api-manager.js';
import { TaobaoParams } from './params.js';

/**
 * 淘宝物料搜索服务类
 * 处理淘宝客物料搜索功能
 */
export class TaobaoMaterialSearchService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 淘宝API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: TaobaoApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册物料搜索工具
	 * https://open.taobao.com/api.htm?docId=64758&docType=2&scopeId=13991
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('searchMaterial');

		this.mcpServer.tool(
			toolName,
			'淘宝客-服务商-物料搜索升级版[taobao.tbk.sc.material.optional.upgrade]',
			TaobaoParams.materialSearch,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);

					// 直接使用传入的参数
					const result =
						await this.apiManager.materialSearchUpgrade.searchMaterial(
							params
						);
					log(`工具 ${toolName} 执行结果: ${JSON.stringify(result)}`);
					return {
						content: [
							{
								type: 'text',
								text: JSON.stringify(result),
							},
						],
					};
				} catch (error) {
					logError(
						`工具 ${toolName} 执行出错: ${
							error instanceof Error
								? error.message
								: String(error)
						}`
					);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${
									error instanceof Error
										? error.message
										: JSON.stringify(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
