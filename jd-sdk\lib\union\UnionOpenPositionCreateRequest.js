/**
 * 推广位创建API
 * 接口文档: https://union.jd.com/openplatform/api/10428
 */

const { BaseApi } = require('../api');

/**
 * 推广位创建请求
 */
class UnionOpenPositionCreateRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.position.create';
  }

  /**
   * 创建推广位
   * @param {Object} positionReq 创建请求参数
   * @param {number} positionReq.unionId 联盟ID
   * @param {string} positionReq.key 授权key，必填参数
   * @param {number} positionReq.unionType 联盟推广位类型 3：私域推广位 4：联盟后台推广位
   * @param {number} positionReq.type 站点类型 3：无线站 4：PC站
   * @param {string|Array<string>} positionReq.spaceNameList 推广位名称
   * @param {number} positionReq.siteId 站点ID，必填，无ID时传入0
   * @param {string} [positionReq.pid] pid格式，{uniontype}_{siteid}_xxx 其中xxx为自定义参数
   * @returns {Promise<Object>} 创建结果
   */
  async create(positionReq) {
    console.log('UnionOpenPositionCreateRequest.create调用:');
    console.log('- 原始参数:', JSON.stringify({
      ...positionReq,
      key: positionReq && positionReq.key ? `已设置(长度:${positionReq.key.length})` : '未设置'
    }, null, 2));
    
    // 如果传入的是内部包装的对象，提取positionReq
    if (positionReq && positionReq.positionReq) {
      console.log('- 发现嵌套的positionReq对象，进行提取');
      positionReq = positionReq.positionReq;
    }
    
    // 确保positionReq是一个对象
    positionReq = positionReq || {};
    
    // 尝试从各种可能的来源获取key
    if (!positionReq.key) {
      if (process.env.JD_KEY) {
        console.log('- 从环境变量JD_KEY获取key值');
        positionReq.key = process.env.JD_KEY;
      } else if (process.env.key) {
        console.log('- 从环境变量key获取key值');
        positionReq.key = process.env.key;
      }
    }
    
    // 尝试从各种可能的来源获取unionId
    if (!positionReq.unionId) {
      if (process.env.JD_UNION_ID) {
        console.log('- 从环境变量JD_UNION_ID获取unionId值');
        positionReq.unionId = Number(process.env.JD_UNION_ID);
      } else if (process.env.unionId) {
        console.log('- 从环境变量unionId获取unionId值');
        positionReq.unionId = Number(process.env.unionId);
      }
    }
    
    console.log('- 处理后的参数:', JSON.stringify({
      unionId: positionReq.unionId,
      key: positionReq.key ? `已设置(长度:${positionReq.key.length})` : '未设置',
      unionType: positionReq.unionType,
      type: positionReq.type,
      spaceNameList: positionReq.spaceNameList,
      siteId: positionReq.siteId
    }, null, 2));
    
    // 检查必填参数
    if (!positionReq.key) {
      throw new Error('业务参数key是必填的，请在.env文件中设置key参数');
    }
    
    if (!positionReq.unionId) {
      throw new Error('业务参数unionId是必填的，请在.env文件中设置unionId参数');
    }
    
    if (!positionReq.unionType) {
      throw new Error('业务参数unionType是必填的，应为3或4');
    }
    
    if (!positionReq.type) {
      throw new Error('业务参数type是必填的，应为3(无线站)或4(PC站)');
    }
    
    if (!positionReq.spaceNameList) {
      throw new Error('业务参数spaceNameList是必填的，应为推广位名称字符串或数组');
    }
    
    if (positionReq.siteId === undefined) {
      throw new Error('业务参数siteId是必填的，无ID时传入0');
    }
    
    // 验证unionType的值是否有效
    if (positionReq.unionType !== 3 && positionReq.unionType !== 4) {
      throw new Error('业务参数unionType的值无效，应为3(私域推广位)或4(联盟后台推广位)');
    }
    
    // 验证type的值是否有效
    if (positionReq.type !== 3 && positionReq.type !== 4) {
      throw new Error('业务参数type的值无效，应为3(无线站)或4(PC站)');
    }
    
    // 处理spaceNameList参数，保持原样(可以是字符串或数组)
    // 注意：接口同时支持字符串和数组形式
    
    return this.execute({ positionReq });
  }
}

module.exports = {
  UnionOpenPositionCreateRequest
}; 