import { BaseJdTestClient } from './base-test-client.js';
import dotenv from 'dotenv';
import chalk from 'chalk';

// 加载环境变量
dotenv.config();

/**
 * 测试京东商品查询API
 */
async function testGoodsQuery() {
	console.log(chalk.blue('开始测试京东商品查询API...'));

	// 客户端不需要检查环境变量，这些环境变量是服务端从网络JWT中获取的

	console.log(chalk.yellow('\n正在调用京东商品查询API...'));

	const client = new BaseJdTestClient();

	// 使用关键词搜索商品
	const keyword = '苹果手机';
	const pageSize = 10;

	const response = await client.callJdTool('goods.query', {
		keyword,
		pageSize,
		sortName: 'price', // 按价格排序
		sort: 'asc', // 升序
		owner: 'g', // 只查自营
	});

	console.log(chalk.green('\n京东商品查询响应:'));
	console.log(response);
}
// 直接执行测试
testGoodsQuery().catch(console.error);
