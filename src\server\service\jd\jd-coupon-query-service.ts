import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { log, logError } from "../../../utils/logger.js";
import { JdApiManager } from "../../jd/api-manager.js";

/**
 * 京东优惠券查询服务类
 * 处理京东联盟优惠券查询功能
 */
export class JdCouponQueryService {
  /**
   * 构造函数
   * @param mcpServer MCP服务器实例
   * @param apiManager 京东API管理器实例
   * @param getFullToolName 获取完整工具名称的函数
   */
  constructor(
    private mcpServer: McpServer,
    private apiManager: JdApiManager,
    private getFullToolName: (name: string) => string
  ) {}

  /**
   * 注册优惠券查询工具
   */
  public registerTool(): void {
    const toolName = this.getFullToolName("coupon.query");
    
    this.mcpServer.tool(
      toolName,
      "查询京东优惠券信息[京东联盟-优惠券查询]",
      {
        couponUrls: z.array(z.string()).min(1).describe("【必填】优惠券链接集合，最多支持100个")
      },
      async ({ couponUrls }) => {
        try {
          log(`执行工具 ${toolName}: ${JSON.stringify({ couponUrls })}`);
          
          // 获取环境变量
          const unionId = process.env.JD_UNION_ID;
          const key = process.env.JD_KEY;
          
          // 检查必要的环境变量
          if (!unionId) {
            throw new Error('环境变量JD_UNION_ID未设置');
          }
          
          if (!key) {
            throw new Error('环境变量JD_KEY未设置');
          }
          
          log(`使用环境变量: JD_UNION_ID=${unionId}, JD_KEY=${key ? '已设置' : '未设置'}`);
          
          try {
            // 准备参数，按照正确格式传递
            const requestParams = {
              couponUrls, // 直接传递数组格式
              unionId: Number(unionId),
              key  // 传递实际的key值，不要替换为"已设置"
            };
            
            // 打印日志时隐藏敏感信息
            log(`执行优惠券查询, 参数: ${JSON.stringify({
              ...requestParams, 
              key: key ? '已设置' : '未设置'
            }, null, 2)}`);
            
            // 执行优惠券查询
            const result = await this.apiManager.couponQuery.queryCoupons(requestParams);
            
            log(`工具 ${toolName} 执行结果: ${JSON.stringify(result, null, 2)}`);
            
            // 结果已经是解析后的JSON对象，直接返回
            return {
              content: [{
                type: "text",
                text: JSON.stringify(result, null, 2)
              }]
            };
          } catch (apiError) {
            logError(`优惠券查询API调用失败: ${apiError instanceof Error ? apiError.message : String(apiError)}`);
            throw apiError;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          logError(`工具 ${toolName} 执行出错: ${errorMessage}`);
          return {
            content: [{
              type: "text",
              text: `工具执行失败: ${errorMessage}`
            }],
            isError: true
          };
        }
      }
    );
  }
}
