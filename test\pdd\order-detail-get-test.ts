import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 获取订单详情测试客户端
 * 用于测试拼多多获取订单详情功能
 */
export class OrderDetailGetTest extends BaseTestClient {
  private orderSn: string;

  /**
   * 构造函数
   * @param orderSn 订单号
   */
  constructor(orderSn: string) {
    super();
    this.orderSn = orderSn;
  }

  /**
   * 执行获取订单详情测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.order.detail.get", {
        order_sn: this.orderSn
      });

      // 打印结果
      if (result.content?.[0]?.text) {
        console.log('获取订单详情测试成功!');
        console.log('查询结果:');
        console.log(result.content[0].text);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const testOrderSn = '250407-497675221411706';
  const test = new OrderDetailGetTest(testOrderSn);
  test.runTest().catch(console.error);
}
