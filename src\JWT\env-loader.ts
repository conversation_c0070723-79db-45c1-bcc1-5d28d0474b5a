/**
 * 环境变量加载器
 * 从网络获取JWT加密的环境变量并解密加载，支持AES二次加密
 */

import axios from 'axios';
import { verifyToken, decryptWithAesAndJwt } from './jwt-utils.js';
import { log, logError, logDebug } from '../utils/logger.js';

/**
 * 环境变量配置
 */
interface EnvConfig {
	/**
	 * 环境变量URL
	 */
	url: string;

	/**
	 * JWT密钥
	 */
	secret: string;

	/**
	 * 可选的AES密钥，如果提供则使用AES进行二次解密
	 */
	aesKey?: string;

	/**
	 * 是否覆盖已存在的环境变量
	 */
	override?: boolean;
}

/**
 * 从网络加载环境变量
 * @param config 环境变量配置
 * @returns 是否成功加载
 */
export async function loadEnvFromNetwork(config: EnvConfig): Promise<boolean> {
		// log(`正在从 ${config.url} 加载环境变量...`);
	
		const response = await axios.get(config.url);

		if (response.status !== 200) {
			logError(`请求失败，状态码: ${response.status}`);
			process.exit(1)
		}

		// log(JSON.stringify(response.data))

		// 处理JSON响应，从 data 字段中提取加密数据
		let encryptedData;
		if (
			typeof response.data === 'object' &&
			response.data !== null &&
			'data' in response.data
		) {
			encryptedData = response.data.data;
		} else {
			encryptedData = response.data;
		}

		// log(encryptedData)

		if (typeof encryptedData !== 'string' || !encryptedData.trim()) {
			logError('获取到的加密数据无效');
			process.exit(1);
		}

		// 解密数据
		let envVars;
		if (config.aesKey) {
			// 如果提供AES密钥，则使用AES-JWT双重解密
			envVars = decryptWithAesAndJwt(
				encryptedData,
				config.secret,
				config.aesKey,
				{
					audience: process.env.JWT_AUDIENCE || 'mcp-server',
					issuer: process.env.JWT_ISSUER || 'mcp-env-encryptor',
				}
			);
		} else {
			// 否则只使用JWT解密
			envVars = verifyToken(encryptedData, {
				secret: config.secret,
				audience: process.env.JWT_AUDIENCE || 'mcp-server',
				issuer: process.env.JWT_ISSUER || 'mcp-env-encryptor',
			});
		}

		if (!envVars || typeof envVars !== 'object') {
			logError('JWT令牌解密后的数据无效');
			process.exit(1);
		}

		// 验证 SERVER_SECRET
		if ('SERVER_SECRET' in envVars) {
			const jwtServerSecret = envVars.SERVER_SECRET;
			const configServerSecret = process.env.SERVER_SECRET;

			if (configServerSecret !== jwtServerSecret) {
				logError('未授权，禁止的访问');
				process.exit(1);
			}

			log('授权验证通过');
		} else {
			logError('缺少授权验证参数，跳过验证');
			process.exit(1);
		}

		// 设置环境变量
		let count = 0;
		for (const [key, value] of Object.entries(envVars)) {
			if (typeof value !== 'string') continue;

			// 如果环境变量已存在且不覆盖，则跳过
			if (process.env[key] && !config.override) {
				logDebug(`环境变量 ${key} 用户已设置`);
				continue;
			}
			// log(`设置环境变量 ${key} = ${value}`);
			process.env[key] = value;
			count++;
		}

		log(`成功从网络加载 ${count} 个环境变量`);
		return true;
	
}

/**
 * 检查必要的环境变量是否已设置
 * @param requiredVars 必要的环境变量名称数组
 * @returns 是否所有必要的环境变量都已设置
 */
export function checkRequiredEnvVars(requiredVars: string[]): boolean {
	const missingVars: string[] = [];

	for (const varName of requiredVars) {
		if (!process.env[varName]) {
			missingVars.push(varName);
		}
	}

	if (missingVars.length > 0) {
		logError(`缺少必要的环境变量: ${missingVars.join(', ')}`);
		return false;
	}

	return true;
}
