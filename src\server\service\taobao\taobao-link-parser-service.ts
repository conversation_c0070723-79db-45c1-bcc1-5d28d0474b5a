import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { TaobaoApiManager } from '../../taobao/api-manager.js';
import { TaobaoParams } from './params.js';

/**
 * 淘宝链接解析服务类
 * 处理淘宝客链接解析功能
 */
export class TaobaoLinkParserService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 淘宝API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: TaobaoApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册链接解析工具
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('parseLink');

		this.mcpServer.tool(
			toolName,
			'解析淘宝客链接或淘口令[淘宝客-服务商-万能解析]',
			TaobaoParams.linkParser,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);

					// 直接使用参数调用API
					const result = await this.apiManager.linkParser.parseLink(
						params
					);
					log(`工具 ${toolName} 执行结果: ${JSON.stringify(result)}`);
					return {
						content: [
							{
								type: 'text',
								text: JSON.stringify(result),
							},
						],
					};
				} catch (error) {
					logError(
						`工具 ${toolName} 执行出错: ${
							error instanceof Error
								? error.message
								: String(error)
						}`
					);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${
									error instanceof Error
										? error.message
										: JSON.stringify(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
