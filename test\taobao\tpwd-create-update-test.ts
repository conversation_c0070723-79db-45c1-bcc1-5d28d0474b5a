import { TpwdCreate } from '../../src/server/taobao/tpwd-create.js';
import { TpwdCreateParams } from '../../src/server/taobao/types.js';
import dotenv from 'dotenv';
import chalk from 'chalk';

// 加载环境变量
dotenv.config();

/**
 * 测试更新后的淘口令创建API
 */
async function testTpwdCreate() {
  console.log(chalk.blue('测试更新后的淘口令创建API...'));
  
  try {
    // 创建实例
    const creator = new TpwdCreate();
    
    // 测试淘口令创建功能
    const params: TpwdCreateParams = {
      url: 'https://detail.tmall.com/item.htm?id=681287356047',
      text: '这是一个测试商品',
      logo: 'https://img.alicdn.com/bao/uploaded/i3/2200724907121/O1CN01BUk7oO22AEPe0ktxB_!!2200724907121.jpg'
    };
    
    console.log(`淘口令创建参数: ${JSON.stringify(params, null, 2)}`);
    const result = await creator.createTpwd(params);
    console.log(`淘口令创建结果: ${JSON.stringify(result, null, 2)}`);
    
    console.log(chalk.green('淘口令创建API测试成功!'));
  } catch (error) {
    console.error(chalk.red('淘口令创建API测试失败:'), error);
    process.exit(1);
  }
}

// 执行测试
testTpwdCreate().catch(console.error);

export { testTpwdCreate }; 