import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log } from '../../../utils/logger.js';
import { BaseService } from '../base-service.js';
import { TaobaoApiManager } from '../../taobao/api-manager.js';

// 导入所有服务类
import { TaobaoMaterialSearchService } from './taobao-material-search-service.js';
import { TaobaoLinkConverterService } from './taobao-link-converter-service.js';
import { TaobaoLinkParserService } from './taobao-link-parser-service.js';
import { TaobaoActivityConverterService } from './taobao-activity-converter-service.js';
import { TaobaoOrderDetailsService } from './taobao-order-details-service.js';
import { TaobaoSpreadRequestsService } from './taobao-spread-requests-service.js';
import { TaobaoPromotionZoneService } from './taobao-promotion-zone-service.js';
import { TaobaoTpwdCreateService } from './taobao-tpwd-create-service.js';
import { TaobaoPunishOrderService } from './taobao-punish-order-service.js';
import { TaobaoItemInfoService } from './taobao-item-info-service.js';
import { TaobaoOptimusPromotionService } from './taobao-optimus-promotion-service.js';
import { TaobaoVegasSendReportService } from './taobao-vegas-send-report-service.js';

/**
 * 淘宝服务类
 * 实现淘宝客-服务商的所有API功能
 */
export class TaobaoService extends BaseService {
	/**
	 * 平台名称
	 */
	protected platformName = 'taobao';

	/**
	 * API管理器实例，使用懒加载模式
	 */
	private apiManager?: TaobaoApiManager;

	/**
	 * 各个服务实例
	 */
	private materialSearchService?: TaobaoMaterialSearchService;
	private linkConverterService?: TaobaoLinkConverterService;
	private linkParserService?: TaobaoLinkParserService;
	private activityConverterService?: TaobaoActivityConverterService;
	private orderDetailsService?: TaobaoOrderDetailsService;
	private spreadRequestsService?: TaobaoSpreadRequestsService;
	private promotionZoneService?: TaobaoPromotionZoneService;
	private tpwdCreateService?: TaobaoTpwdCreateService;
	private punishOrderService?: TaobaoPunishOrderService;
	private itemInfoService?: TaobaoItemInfoService;
	private optimusPromotionService?: TaobaoOptimusPromotionService;
	private vegasSendReportService?: TaobaoVegasSendReportService;

	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 */
	constructor(mcpServer: McpServer) {
		super(mcpServer);
	}

	/**
	 * 初始化API管理器(延迟加载)
	 */
	protected initApiManager(): TaobaoApiManager {
		log('初始化淘宝API管理器');
		return new TaobaoApiManager();
	}

	/**
	 * 获取API管理器实例
	 * 确保API管理器已初始化
	 */
	private getApiManager(): TaobaoApiManager {
		if (!this.apiManager) {
			this.apiManager = this.initApiManager();
		}
		return this.apiManager;
	}

	/**
	 * 注册所有淘宝平台工具
	 */
	protected registerPlatformTools(): void {
		// 初始化各个服务
		this.initializeServices();

		// 注册各个服务的工具
		this.registerServiceTools();
	}

	/**
	 * 初始化所有服务
	 */
	private initializeServices(): void {
		const apiManager = this.getApiManager();
		const getFullToolName = this.getFullToolName.bind(this);

		this.materialSearchService = new TaobaoMaterialSearchService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.linkConverterService = new TaobaoLinkConverterService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.linkParserService = new TaobaoLinkParserService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.activityConverterService = new TaobaoActivityConverterService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.orderDetailsService = new TaobaoOrderDetailsService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.spreadRequestsService = new TaobaoSpreadRequestsService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.promotionZoneService = new TaobaoPromotionZoneService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.tpwdCreateService = new TaobaoTpwdCreateService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.punishOrderService = new TaobaoPunishOrderService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.itemInfoService = new TaobaoItemInfoService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.optimusPromotionService = new TaobaoOptimusPromotionService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);

		this.vegasSendReportService = new TaobaoVegasSendReportService(
			this.mcpServer,
			apiManager,
			getFullToolName
		);
	}

	/**
	 * 注册所有服务的工具
	 */
	private registerServiceTools(): void {
		// 注册物料搜索工具
		this.materialSearchService?.registerTool();

		// 注册链接转换工具
		this.linkConverterService?.registerTool();

		// 注册链接解析工具
		this.linkParserService?.registerTool();

		// 注册活动转换工具
		this.activityConverterService?.registerTool();

		// 注册订单查询工具
		this.orderDetailsService?.registerTool();

		// 注册批量转链工具
		this.spreadRequestsService?.registerTool();

		// 注册创建推广位工具
		this.promotionZoneService?.registerTool();

		// 注册淘口令生成工具
		this.tpwdCreateService?.registerTool();

		// 注册处罚订单查询工具
		this.punishOrderService?.registerTool();

		// 注册商品详情查询工具
		this.itemInfoService?.registerTool();

		// 注册权益物料精选工具
		this.optimusPromotionService?.registerTool();

		// 注册红包发放个数查询工具
		this.vegasSendReportService?.registerTool();
	}
}
