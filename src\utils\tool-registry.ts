import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z, ZodSchema } from "zod";
import { log, logError } from "./logger.js";

/**
 * MCP工具注册器
 * 提供统一的工具注册方法，使直接绑定到服务器
 */
export class ToolRegistry {
  /**
   * 构造函数
   * @param server MCP服务器实例
   */
  constructor(private server: McpServer) {}

  /**
   * 注册工具
   * @param name 工具名称
   * @param description 工具描述
   * @param paramsSchema 参数模式定义
   * @param handler 处理函数
   */
  public registerTool(
    name: string,
    description: string,
    paramsSchema: Record<string, ZodSchema>,
    handler: (params: any) => Promise<any>
  ): void {
    log(`注册工具: ${name}`);
    
    this.server.tool(
      name,
      description,
      paramsSchema,
      async (args, extra) => {
        try {
          log(`执行工具 ${name}: ${JSON.stringify(args)}`);
          const result = await handler(args);
          log(`工具 ${name} 执行结果: ${JSON.stringify(result)}`);
          return result;
        } catch (error) {
          logError(`工具 ${name} 执行出错: ${error instanceof Error ? error.message : String(error)}`);
          return {
            content: [{
              type: "text",
              text: `工具执行失败: ${error instanceof Error ? error.message : JSON.stringify(error)}`
            }],
            isError: true
          };
        }
      }
    );
  }
} 