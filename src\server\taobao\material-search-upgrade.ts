import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getRequiredEnvVar } from '../../utils/env.js';
import { filterEmptyParams } from '../../utils/params-helper.js';

/**
 * 淘宝客-服务商-物料搜索升级版响应
 */
export interface MaterialSearchUpgradeResponse {
	[key: string]: any;
}

/**
 * 淘宝客-服务商-物料搜索升级版参数
 */
export interface MaterialSearchUpgradeParams {
	/**
	 * 广告位ID，必填
	 */
	adzone_id?: string;

	/**
	 * 站点ID，必填
	 */
	site_id?: string;

	/**
	 * 物料ID，必填
	 */
	material_id?: number | string;

	/**
	 * 查询词，非必填
	 */
	q?: string;

	/**
	 * 是否有优惠券，非必填
	 */
	has_coupon?: boolean;

	/**
	 * 排序方式，非必填
	 */
	sort?: string;

	/**
	 * 页码，非必填
	 */
	page_no?: number;

	/**
	 * 页大小，非必填
	 */
	page_size?: number;

	/**
	 * 商品ID，非必填
	 */
	item_id?: string;

	/**
	 * 链接形式：1:PC，2:无线，默认：1，非必填
	 */
	platform?: number;

	/**
	 * 商品筛选-设备类型：1-pc，2-无线，非必填
	 */
	device_type?: string;

	/**
	 * 商品筛选-是否海外商品，非必填
	 */
	is_overseas?: boolean;

	/**
	 * 商品筛选-是否天猫商品，非必填
	 */
	is_tmall?: boolean;

	/**
	 * 商品筛选-是否包邮，非必填
	 */
	include_pay_rate_30?: boolean;

	/**
	 * 商品筛选-是否加入消费者保障，非必填
	 */
	include_good_rate?: boolean;

	/**
	 * 商品筛选-好评率是否高于行业均值，非必填
	 */
	include_rfd_rate?: boolean;

	/**
	 * 商品筛选-退款率是否低于行业均值，非必填
	 */
	need_prepay?: boolean;

	/**
	 * 商品筛选-是否加入消费者保障，非必填
	 */
	need_free_shipment?: boolean;

	/**
	 * 商品筛选-牛皮癣程度，取值：1不限，2无，3轻微，非必填
	 */
	npx_level?: number;

	/**
	 * 商品筛选-KA媒体淘客佣金率上限，如：1234表示12.34%，非必填
	 */
	end_ka_tk_rate?: number;

	/**
	 * 商品筛选-KA媒体淘客佣金率下限，如：1234表示12.34%，非必填
	 */
	start_ka_tk_rate?: number;

	/**
	 * 商品筛选-淘客佣金率上限，如：1234表示12.34%，非必填
	 */
	end_tk_rate?: number;

	/**
	 * 商品筛选-淘客佣金率下限，如：1234表示12.34%，非必填
	 */
	start_tk_rate?: number;

	/**
	 * 商品筛选-折扣价范围上限，单位：元，非必填
	 */
	end_price?: number;

	/**
	 * 商品筛选-折扣价范围下限，单位：元，非必填
	 */
	start_price?: number;

	/**
	 * 商品筛选-30天销量上限，非必填
	 */
	end_sales_count?: number;

	/**
	 * 商品筛选-30天销量下限，非必填
	 */
	start_sales_count?: number;

	/**
	 * 商品筛选-会员运营商品，1-是，0-否，非必填
	 */
	lock_rate_end_time?: number;

	/**
	 * 商品筛选-会员运营商品锁佣时间，非必填
	 */
	lock_rate_start_time?: number;

	/**
	 * 商品筛选-淘宝类目ID，非必填
	 */
	cat?: string;

	/**
	 * 商品筛选-所在地，非必填
	 */
	itemloc?: string;

	/**
	 * 智能匹配-设备号类型：IMEI，或者IDFA，或者UTDID，或者OAID，非必填
	 */
	use_simple_fields?: boolean;

	/**
	 * 智能匹配-设备号加密后的值（MD5加密需32位小写），类型为IMEI，或者IDFA，或者UTDID，或者OAID，非必填
	 */
	device_encrypt?: string;

	/**
	 * 智能匹配-设备号加密类型：MD5，非必填
	 */
	device_type_for_strategy?: string;

	/**
	 * 智能匹配-设备号类型：IMEI，或者IDFA，或者UTDID，或者OAID，非必填
	 */
	device_value?: string;

	/**
	 * 人群ID，仅适用于物料评估，非必填
	 */
	dx_id?: string;

	/**
	 * 是否返回商品视频信息，非必填
	 */
	need_product_video?: boolean;

	/**
	 * 商品筛选-好评率高于等于，非必填
	 */
	start_good_rate?: number;

	/**
	 * 商品筛选-好评率低于等于，非必填
	 */
	end_good_rate?: number;

	/**
	 * 商品筛选-佣金金额上限，单位为分，非必填
	 */
	end_commission?: number;

	/**
	 * 商品筛选-佣金金额下限，单位为分，非必填
	 */
	start_commission?: number;

	/**
	 * 商品筛选-预估到手价范围上限，单位为元，非必填
	 */
	end_price_after_coupon?: number;

	/**
	 * 商品筛选-预估到手价范围下限，单位为元，非必填
	 */
	start_price_after_coupon?: number;

	/**
	 * 商品筛选-优惠券金额上限，单位为元，非必填
	 */
	end_coupon_amount?: number;

	/**
	 * 商品筛选-优惠券金额下限，单位为元，非必填
	 */
	start_coupon_amount?: number;

	/**
	 * 商品筛选-优惠券门槛金额上限，单位为元，非必填
	 */
	end_coupon_start_fee?: number;

	/**
	 * 商品筛选-优惠券门槛金额下限，单位为元，非必填
	 */
	start_coupon_start_fee?: number;

	/**
	 * 商品筛选-商品主图视频：1-无主图视频，0-全部商品，非必填
	 */
	ip_range_filter?: string;

	/**
	 * 商品筛选-商品主图视频：1-无主图视频，0-全部商品，非必填
	 */
	presale_deposit_step?: string;

	/**
	 * 商品筛选-商品主图视频：1-无主图视频，0-全部商品，非必填
	 */
	presale_discount_fee_step?: string;

	/**
	 * 商品筛选-商品主图视频：1-无主图视频，0-全部商品，非必填
	 */
	presale_end_time?: number;

	/**
	 * 商品筛选-商品主图视频：1-无主图视频，0-全部商品，非必填
	 */
	presale_start_time?: number;

	/**
	 * 商品筛选-商品主图视频：1-无主图视频，0-全部商品，非必填
	 */
	presale_tail_end_time?: number;

	/**
	 * 商品筛选-商品主图视频：1-无主图视频，0-全部商品，非必填
	 */
	presale_tail_start_time?: number;

	/**
	 * 商品筛选-商品主图视频：1-无主图视频，0-全部商品，非必填
	 */
	no_product_video?: number;
}

/**
 * 淘宝客-服务商-物料搜索升级版API
 * 提供根据查询条件搜索淘宝客商品的功能
 * https://open.taobao.com/api.htm?docId=64758&docType=2&scopeId=13991
 */
export class MaterialSearchUpgrade extends BaseApi {
	/**
	 * 搜索淘宝客商品
	 * @param params 搜索参数
	 * @returns 搜索响应
	 */
	async searchMaterial(
		params: Omit<MaterialSearchUpgradeParams, 'adzone_id' | 'site_id'>
	): Promise<MaterialSearchUpgradeResponse> {
		try {
			log(
				`执行物料搜索升级版: ${JSON.stringify({
					material_id: params.material_id,
					q: params.q,
					page_no: params.page_no,
					page_size: params.page_size,
				})}`
			);

			// 从环境变量获取广告位ID和站点ID
			const adzone_id = getRequiredEnvVar('TAOBAO_ADZONE_ID');
			const site_id = getRequiredEnvVar('TAOBAO_SITE_ID');

			// 构建API参数
			const apiParams = {
				...filterEmptyParams(params),
				adzone_id,
				site_id,
			};

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.sc.material.optional.upgrade',
				apiParams
			);

			log(`物料搜索升级版结果: ${JSON.stringify(result)}`);

			// 返回响应
			return result;
		} catch (error) {
			log(`物料搜索升级版失败: ${error}`);
			throw error;
		}
	}
}
