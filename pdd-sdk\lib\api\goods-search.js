/**
 * 拼多多多多进宝商品搜索API
 * 接口名称：pdd.ddk.oauth.goods.search
 */

const BaseApi = require('../core/base-api');

class GoodsSearch extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 多多进宝商品搜索
   * @param {Object} options 搜索参数
   * @param {String} [options.keyword] 商品关键词，例如搜索"手机"
   * @param {Array<Number>} [options.activityTags] 活动商品标记数组，例如[4,7]
   * @param {Array<Number>} [options.blockCatPackages] 屏蔽商品类目包
   * @param {Array<Number>} [options.blockCats] 屏蔽商品类目
   * @param {Number} [options.catId] 商品类目ID
   * @param {String} [options.goodsImgType] 商品goodsSign，例如"c9r2omogKbZmu7"
   * @param {Boolean} [options.isBrandGoods] 是否为品牌商品
   * @param {String} [options.listId] 翻页时建议传入前页返回的list_id值
   * @param {Number} [options.merchantType] 店铺类型，1-个人，2-企业，3-旗舰店，4-专卖店，5-专营店，6-普通店
   * @param {Array<Number>} [options.merchantTypeList] 店铺类型数组
   * @param {Number} [options.optId] 商品标签类目ID
   * @param {Number} [options.page] 页码，默认1
   * @param {Number} [options.pageSize] 每页数量，默认100
   * @param {String} [options.pid] 推广位ID
   * @param {Object} [options.rangeList] 价格区间，例如{"range_id":0,"range_from":1,"range_to":1500}
   * @param {Number} [options.rangeId] 区间ID
   * @param {Number} [options.rangeFrom] 区间起始值
   * @param {Number} [options.rangeTo] 区间结束值
   * @param {String} [options.sortType] 排序方式:0-综合排序;1-按佣金比例降序;2-按佣金金额降序;3-按价格升序;4-按价格降序;5-按销量降序;6-按收入比例降序;7-优惠券金额排序升序;8-优惠券金额排序降序
   * @param {Boolean} [options.withCoupon] 是否只返回优惠券的商品
   * @returns {Promise} Promise对象
   */
  async search(options = {}) {
    // 由于拼多多API对参数名称有特殊要求，需要手动处理一些参数名称
    const apiParams = {};
    
    // 关键词
    if (options.keyword) {
      apiParams.keyword = options.keyword;
    }
    
    // 活动标签
    if (options.activityTags) {
      apiParams.activity_tags = JSON.stringify(options.activityTags);
    }
    
    // 屏蔽类目包
    if (options.blockCatPackages) {
      apiParams.block_cat_packages = JSON.stringify(options.blockCatPackages);
    }
    
    // 屏蔽类目
    if (options.blockCats) {
      apiParams.block_cats = JSON.stringify(options.blockCats);
    }
    
    // 类目ID
    if (options.catId) {
      apiParams.cat_id = options.catId;
    }
    
    // 商品图片类型
    if (options.goodsImgType) {
      apiParams.goods_img_type = options.goodsImgType;
    }
    
    // 是否品牌商品
    if (options.isBrandGoods !== undefined) {
      apiParams.is_brand_goods = options.isBrandGoods;
    }
    
    // 翻页ID
    if (options.listId) {
      apiParams.list_id = options.listId;
    }
    
    // 店铺类型
    if (options.merchantType) {
      apiParams.merchant_type = options.merchantType;
    }
    
    // 店铺类型列表
    if (options.merchantTypeList) {
      apiParams.merchant_type_list = JSON.stringify(options.merchantTypeList);
    }
    
    // 商品标签类目ID
    if (options.optId) {
      apiParams.opt_id = options.optId;
    }
    
    // 页码
    if (options.page) {
      apiParams.page = options.page;
    }
    
    // 每页数量
    if (options.pageSize) {
      apiParams.page_size = options.pageSize;
    }
    
    // 推广位ID
    if (options.pid) {
      apiParams.pid = options.pid;
    }
    
    // 价格区间
    if (options.rangeList) {
      apiParams.range_list = JSON.stringify(options.rangeList);
    } else if (options.rangeId !== undefined || options.rangeFrom !== undefined || options.rangeTo !== undefined) {
      const rangeList = [];
      const rangeItem = {};
      
      if (options.rangeId !== undefined) rangeItem.range_id = options.rangeId;
      if (options.rangeFrom !== undefined) rangeItem.range_from = options.rangeFrom;
      if (options.rangeTo !== undefined) rangeItem.range_to = options.rangeTo;
      
      if (Object.keys(rangeItem).length > 0) {
        rangeList.push(rangeItem);
        apiParams.range_list = JSON.stringify(rangeList);
      }
    }
    
    // 排序方式
    if (options.sortType) {
      apiParams.sort_type = options.sortType;
    }
    
    // 是否只返回有优惠券的商品
    if (options.withCoupon !== undefined) {
      apiParams.with_coupon = options.withCoupon;
    }
    
    // 执行请求时不转换参数，因为我们已经手动进行了转换
    return this.execute('pdd.ddk.oauth.goods.search', apiParams, false);
  }
}

module.exports = GoodsSearch; 