import { decryptWithAesAndJwt } from '../src/JWT/jwt-utils.js';
import * as fs from 'fs';

// JWT令牌 - 使用刚才生成的令牌
const token = fs.readFileSync('env.jwt', 'utf8').trim();

// 密钥
const secret = 'url:mcp.sinataoke.cn';

try {
	// 解密JWT令牌
	const decoded = decryptWithAesAndJwt(token, secret, 'SINATAOKECN', {
		issuer: 'mcp-env',
		audience: 'taoke-mcp-server',
	});

	console.log('解密成功!');
	console.log('解密结果:');
	console.log(decoded);
} catch (error) {
	console.error(
		'解密失败:',
		error instanceof Error ? error.message : String(error)
	);
}
