/**
 * 服务器模块通用类型定义
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";

/**
 * 服务接口
 * 所有平台服务类必须实现此接口
 */
export interface Service {
  /**
   * 注册服务的工具方法
   */
  register(): void;
}

/**
 * 服务配置接口
 */
export interface ServiceConfig {
  /**
   * 服务是否启用
   */
  enabled: boolean;
  
  /**
   * 服务配置项
   */
  [key: string]: any;
}

/**
 * 服务管理器配置接口
 */
export interface ServerManagerConfig {
  /**
   * 服务器名称
   */
  name?: string;
  
  /**
   * 服务器版本
   */
  version?: string;
  
  /**
   * 各服务配置
   */
  services?: Record<string, ServiceConfig>;
}

/**
 * 服务器状态枚举
 */
export enum ServerStatus {
  /**
   * 已停止
   */
  STOPPED = 'stopped',
  
  /**
   * 正在启动
   */
  STARTING = 'starting',
  
  /**
   * 运行中
   */
  RUNNING = 'running',
  
  /**
   * 正在停止
   */
  STOPPING = 'stopping',
  
  /**
   * 出错
   */
  ERROR = 'error'
}

/**
 * 服务创建工厂接口
 */
export interface ServiceFactory {
  /**
   * 创建服务实例
   * @param mcpServer MCP服务器实例
   * @param config 服务配置
   */
  createService(mcpServer: McpServer, config?: ServiceConfig): Service;
} 