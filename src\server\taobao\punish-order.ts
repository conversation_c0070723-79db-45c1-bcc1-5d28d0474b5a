import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';

/**
 * 淘宝客-服务商-处罚订单查询API
 * 根据接口文档：https://open.taobao.com/api.htm?docId=41942&docType=2&scopeId=15738
 * 提供查询淘宝客推广订单的处罚订单信息
 */
export class PunishOrder extends BaseApi {
	/**
	 * 查询处罚订单
	 * @param af_order_option 查询条件，具体请参考接口文档
	 * @returns 原始API响应
	 */
	public async getPunishOrders(af_order_option: {
		relation_id?: number; // 渠道关系ID
		tb_trade_id?: number; // 子订单号
		page_size?: number; // pagesize
		page_no?: number; // 页码
		span?: number; // 查询时间跨度，单位是天
		start_time?: string; // 查询开始时间，以taoke订单创建时间开始
		adzone_id?: number; // pid中的第三段
		site_id?: number; // pid中的第二段
	}): Promise<any> {
		try {
			log(`执行处罚订单查询: ${JSON.stringify(af_order_option)}`);

			// 直接传递对象，让基类先过滤空参数
			// 构建API参数
			const apiParams: Record<string, any> = {
				af_order_option,
			};

			log(`构建的API参数: ${JSON.stringify(apiParams)}`);

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.sc.punish.order.get',
				apiParams
			);

			log(`处罚订单查询结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`处罚订单查询失败: ${error}`);
			throw error;
		}
	}
}
