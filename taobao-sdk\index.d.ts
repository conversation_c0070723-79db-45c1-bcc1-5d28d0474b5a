/**
 * 淘宝开放平台SDK类型定义
 * 
 * 支持ESM和CommonJS双模式导入
 * 详细API参数请参考淘宝开放平台文档：https://open.taobao.com/api.htm
 */

/**
 * 客户端配置选项
 */
export interface ClientOptions {
  /** 应用Key */
  appkey: string;
  /** 应用Secret */
  appsecret: string;
  /** REST API URL，默认为 https://eco.taobao.com/router/rest */
  REST_URL?: string;
  /** 其他可选配置 */
  [key: string]: any;
}

/**
 * API调用参数
 */
export interface ApiParams {
  /** 用户授权会话 */
  session?: string;
  /** 返回格式，默认json */
  format?: string;
  /** API版本号 */
  v?: string;
  /** 签名方法 */
  sign_method?: string;
  /** 其他API参数 */
  [key: string]: any;
}

/**
 * API响应结果
 */
export interface ApiResponse {
  [key: string]: any;
}

/**
 * API回调函数
 */
export type ApiCallback = (error: Error | null, response: ApiResponse) => void;

/**
 * 淘宝开放平台标准客户端
 */
export class TopClient {
  /**
   * 创建淘宝开放平台客户端实例
   * @param options 客户端配置选项
   */
  constructor(options: ClientOptions);

  /**
   * 执行API调用
   * @param apiname API名称，如 'taobao.tbk.item.convert'
   * @param params API调用参数
   * @param callback 回调函数
   */
  execute(apiname: string, params: ApiParams, callback: ApiCallback): void;

  /**
   * 带HTTP头的API调用
   * @param apiname API名称
   * @param params API参数
   * @param httpHeaders HTTP头信息
   * @param callback 回调函数
   */
  executeWithHeader(apiname: string, params: ApiParams, httpHeaders: any[], callback: ApiCallback): void;

  /**
   * GET方式调用API
   * @param apiname API名称
   * @param params API参数
   * @param callback 回调函数
   */
  get(apiname: string, params: ApiParams, callback: ApiCallback): void;
}

/**
 * 兼容原始ApiClient别名
 */
export const ApiClient: typeof TopClient;

/**
 * TMC消息客户端
 */
export class TmcClient {
  constructor(options: ClientOptions);
  // TMC客户端方法
  [key: string]: any;
}

/**
 * 钉钉客户端
 */
export class DingTalkClient {
  constructor(options: ClientOptions);
  // 钉钉客户端方法
  [key: string]: any;
}

/**
 * 兼容CommonJS模块系统导出
 */
export as namespace TaobaoSDK;

/**
 * 默认导出
 */
export default {
  TopClient,
  ApiClient,
  TmcClient,
  DingTalkClient
}; 