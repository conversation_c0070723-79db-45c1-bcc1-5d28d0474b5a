import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log } from '../../../utils/logger.js';
import { BaseService } from '../base-service.js';
import { PddApiManager } from '../../pdd/api-manager.js';

// 导入所有服务类
import { PddGoodsDetailService } from './pdd-goods-detail-service.js';
import { PddCmsPromUrlService } from './pdd-cms-prom-url-service.js';
import { PddGoodsPidGenerateService } from './pdd-goods-pid-generate-service.js';
import { PddGoodsPidQueryService } from './pdd-goods-pid-query-service.js';
import { PddGoodsPromUrlGenerateService } from './pdd-goods-prom-url-generate-service.js';
import { PddGoodsRecommendGetService } from './pdd-goods-recommend-get-service.js';
import { PddGoodsSearchService } from './pdd-goods-search-service.js';
import { PddGoodsZsUnitUrlGenService } from './pdd-goods-zs-unit-url-gen-service.js';
import { PddMemberAuthorityQueryService } from './pdd-member-authority-query-service.js';
import { PddOrderDetailGetService } from './pdd-order-detail-get-service.js';
import { PddOrderListIncrementGetService } from './pdd-order-list-increment-get-service.js';
import { PddResourceUrlGenService } from './pdd-resource-url-gen-service.js';

/**
 * 拼多多服务类
 * 实现多多进宝的所有API功能
 */
export class PddService extends BaseService {
    /**
     * 平台名称
     */
    protected platformName = 'pdd';

    /**
     * API管理器实例，使用懒加载模式
     */
    private apiManager?: PddApiManager;

    /**
     * 各个服务实例
     */
    private goodsDetailService?: PddGoodsDetailService;
    private cmsPromUrlService?: PddCmsPromUrlService;
    private goodsPidGenerateService?: PddGoodsPidGenerateService;
    private goodsPidQueryService?: PddGoodsPidQueryService;
    private goodsPromUrlGenerateService?: PddGoodsPromUrlGenerateService;
    private goodsRecommendGetService?: PddGoodsRecommendGetService;
    private goodsSearchService?: PddGoodsSearchService;
    private goodsZsUnitUrlGenService?: PddGoodsZsUnitUrlGenService;
    private memberAuthorityQueryService?: PddMemberAuthorityQueryService;
    private orderDetailGetService?: PddOrderDetailGetService;
    private orderListIncrementGetService?: PddOrderListIncrementGetService;
    private resourceUrlGenService?: PddResourceUrlGenService;

    /**
     * 构造函数
     * @param mcpServer MCP服务器实例
     */
    constructor(mcpServer: McpServer) {
        super(mcpServer);
    }

    /**
     * 初始化API管理器(延迟加载)
     */
    protected initApiManager(): PddApiManager {
        log('初始化拼多多API管理器');
        return new PddApiManager();
    }

    /**
     * 获取API管理器实例
     * 确保API管理器已初始化
     */
    private getApiManager(): PddApiManager {
        if (!this.apiManager) {
            this.apiManager = this.initApiManager();
        }
        return this.apiManager;
    }

    /**
     * 注册所有拼多多平台工具
     */
    protected registerPlatformTools(): void {
        // 初始化各个服务
        this.initializeServices();
        
        // 注册各个服务的工具
        this.registerServiceTools();
    }

    /**
     * 初始化所有服务
     */
    private initializeServices(): void {
        const apiManager = this.getApiManager();
        const getFullToolName = this.getFullToolName.bind(this);

        this.goodsDetailService = new PddGoodsDetailService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.cmsPromUrlService = new PddCmsPromUrlService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.goodsPidGenerateService = new PddGoodsPidGenerateService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.goodsPidQueryService = new PddGoodsPidQueryService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.goodsPromUrlGenerateService = new PddGoodsPromUrlGenerateService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.goodsRecommendGetService = new PddGoodsRecommendGetService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.goodsSearchService = new PddGoodsSearchService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.goodsZsUnitUrlGenService = new PddGoodsZsUnitUrlGenService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.memberAuthorityQueryService = new PddMemberAuthorityQueryService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.orderDetailGetService = new PddOrderDetailGetService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.orderListIncrementGetService = new PddOrderListIncrementGetService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );

        this.resourceUrlGenService = new PddResourceUrlGenService(
            this.mcpServer,
            apiManager,
            getFullToolName
        );
    }

    /**
     * 注册所有服务的工具
     */
    private registerServiceTools(): void {
        // 注册商品详情查询工具
        this.goodsDetailService?.registerTool();

        // 注册商城推广链接生成工具
        this.cmsPromUrlService?.registerTool();

        // 注册推广位生成工具
        this.goodsPidGenerateService?.registerTool();

        // 注册推广位查询工具
        this.goodsPidQueryService?.registerTool();

        // 注册商品推广链接生成工具
        this.goodsPromUrlGenerateService?.registerTool();

        // 注册商品推荐查询工具
        this.goodsRecommendGetService?.registerTool();

        // 注册商品搜索工具
        this.goodsSearchService?.registerTool();

        // 注册多多进宝转链接口工具
        this.goodsZsUnitUrlGenService?.registerTool();

        // 注册查询是否绑定备案工具
        this.memberAuthorityQueryService?.registerTool();

        // 注册获取订单详情工具
        this.orderDetailGetService?.registerTool();

        // 注册按照更新时间段增量同步推广订单信息工具
        this.orderListIncrementGetService?.registerTool();

        // 注册拼多多主站频道推广工具
        this.resourceUrlGenService?.registerTool();
    }
}
