/**
 * 测试环境变量JWT-AES双重加密
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { encryptEnvFile } from '../src/JWT/env-encryptor.js';

// 加载环境变量
dotenv.config();

// 主函数
async function main() {
  console.log('====== 测试环境变量JWT-AES双重加密 ======');
  
  // 获取环境变量
  const envFilePath = path.resolve(process.cwd(), '.env');
  const jwtSecret = process.env.ENV_SECRET || 'url:mcp.sinataoke.cn';
  const aesKey = process.env.ENV_AES_KEY || 'liuliangzhengsinataoke';
  const expiresIn = '30d';
  
  console.log('环境变量文件路径:', envFilePath);
  console.log('JWT Secret:', jwtSecret);
  console.log('AES Key:', aesKey);
  console.log('过期时间:', expiresIn);
  
  try {
    // 1. 只使用JWT加密
    console.log('\n1. 测试只使用JWT加密:');
    const jwtToken = await encryptEnvFile(envFilePath, jwtSecret, expiresIn);
    console.log('JWT加密结果:');
    console.log(jwtToken);
    
    // 保存JWT令牌到文件
    const jwtOutputFile = path.resolve(process.cwd(), 'env.jwt');
    fs.writeFileSync(jwtOutputFile, jwtToken);
    console.log(`JWT令牌已保存到文件: ${jwtOutputFile}`);
    
    // 2. 使用JWT-AES双重加密
    console.log('\n2. 测试使用JWT-AES双重加密:');
    const encryptedData = await encryptEnvFile(envFilePath, jwtSecret, expiresIn, aesKey);
    console.log('JWT-AES双重加密结果:');
    console.log(encryptedData);
    
    // 保存加密数据到文件
    const aesOutputFile = path.resolve(process.cwd(), 'env.jwt.aes');
    fs.writeFileSync(aesOutputFile, encryptedData);
    console.log(`JWT-AES双重加密数据已保存到文件: ${aesOutputFile}`);
    
  } catch (error) {
    console.error('环境变量加密失败:', error instanceof Error ? error.message : String(error));
  }
}

// 执行主函数
main().catch(error => {
  console.error('未处理的错误:', error);
  process.exit(1);
});
