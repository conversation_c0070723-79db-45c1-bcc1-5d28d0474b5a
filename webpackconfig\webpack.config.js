import path from 'path';
import { fileURLToPath } from 'url';
import nodeExternals from 'webpack-node-externals';
import TerserPlugin from 'terser-webpack-plugin';
import { ShebangPlugin } from '../scripts/webpack-shebang-plugin.js';
// 不再使用 CopyPlugin

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default {
  mode: 'production',

  entry: {
    index: path.resolve(__dirname, '../src/index.ts'),
    cli: path.resolve(__dirname, '../src/cli.ts')
  },

  output: {
    path: path.resolve(__dirname, '../out/dist'),
    filename: '[name].js',
    clean: {
      keep: /(\.npmignore|package\.json|README\.md)$/i, // 保留这些文件
    },
    // 使用 CommonJS 格式
    library: {
      type: 'commonjs2'
    },
    // 不启用 ESM 输出
    module: false,
    // 环境不为 module
    environment: {
      module: false
    },
    // 使用 CommonJS 格式的 chunk
    chunkFormat: 'commonjs',
    // 添加 .js 扩展名
    chunkFilename: '[name].js'
  },

  experiments: {
    // 不启用 output.module
    outputModule: false
  },

  resolve: {
    extensions: ['.ts', '.js', '.json'],
    alias: {
      '@': path.resolve(__dirname, '../src'),
      // 添加 async 模块的别名
      'async/forEach': path.resolve(__dirname, '../node_modules/async/forEach.js'),
      'async/series': path.resolve(__dirname, '../node_modules/async/series.js'),
      // 确保正确解析 async 模块
      'async': path.resolve(__dirname, '../node_modules/async'),
      // 确保正确解析 logform 模块
      'logform/json': path.resolve(__dirname, '../node_modules/logform/json.js'),
      // 确保正确解析 @colors/colors 模块
      '@colors/colors/safe': path.resolve(__dirname, '../node_modules/@colors/colors/safe.js')
    },
    extensionAlias: {
      '.js': ['.ts', '.js']
    },
    // 添加 mainFields 配置，确保正确解析 ESM 模块
    mainFields: ['module', 'main', 'browser'],
    // 为 ws 模块的原生依赖提供 fallback
    fallback: {
      // 对于原生模块，使用空对象
      '../build/Release/bufferutil': false,
      '../build/default/bufferutil': false,
      '../build/Release/validation': false,
      '../build/default/validation': false
    }
  },

  target: 'node',

  // 添加 Node.js 兼容性
  node: {
    __dirname: false,
    __filename: false,
    global: true
  },

  // 使用 nodeExternals 排除所有 node_modules 中的依赖
  externals: [nodeExternals({
    modulesDir: path.resolve(__dirname, '../node_modules')
  })],

  module: {
    rules: [
      {
        test: /\.ts$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true
            }
          }
        ],
        exclude: /node_modules/
      }
    ]
  },

  plugins: [
    // 不需要复制文件，因为 out 目录中的文件已经存在，并且会被 clean.keep 选项保留

    // 添加 shebang 行到 CLI 文件
    new ShebangPlugin({
      shebang: '#!/usr/bin/env node',
      files: ['cli.js', 'index.js']
    })
  ],

  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          ecma: 2020, // 支持 ES2020 语法
          compress: {
            drop_console: false,
          },
          mangle: false, // 禁用变量名混淆，避免出错
          keep_classnames: true, // 保留类名
          keep_fnames: true, // 保留函数名
        },
        // 排除可能导致问题的文件
        exclude: [/node_modules/, /jd-sdk/, /taobao-sdk/, /pdd-sdk/, /pdd-sdk-new/],
      }),
    ],
  },

  devtool: 'source-map',

  // 忽略特定警告
  ignoreWarnings: [
    // 忽略与 ws 模块相关的原生模块警告
    /Failed to parse source map/,
    /Can't resolve '..\build\Release\bufferutil'/,
    /Can't resolve '..\build\default\bufferutil'/,
    /Can't resolve '..\build\Release\validation'/,
    /Can't resolve '..\build\default\validation'/,
    // 忽略动态 require 警告
    /Critical dependency: the request of a dependency is an expression/
  ]
};
