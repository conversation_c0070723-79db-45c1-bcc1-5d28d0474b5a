/**
 * 拼多多API公共类型定义文件
 * 集中管理拼多多相关API的类型定义
 */

/**
 * 拼多多API响应接口
 */
export interface PddApiResponse<T> {
	[key: string]: T;
}

/**
 * 调用MCP工具的响应格式
 */
export interface McpToolResponse {
	content: MsgContent[];
	isError?: boolean;
}

/**
 * MCP工具响应内容
 */
export interface MsgContent {
	type: string;
	text: string;
}

/**
 * 商品搜索参数
 */
export interface GoodsSearchParams {
	/**
	 * 关键词，与opt_id字段选填一个或全部填写
	 */
	keyword?: string;

	/**
	 * 活动商品标记数组，例如：[4,7]，4-秒杀，7-百亿补贴，10851-千万补贴，11879-千万神券，10913-招商礼金商品，31-品牌黑标，10564-精选爆品-官方直推爆款，10584-精选爆品-团长推荐，24-品牌高佣，其他的值请忽略
	 */
	activity_tags?: number[];

	/**
	 * 屏蔽商品类目包：1-拼多多小程序屏蔽的类目&关键词;2-虚拟类目;3-医疗器械;4-处方药;5-非处方药
	 */
	block_cat_packages?: number[];

	/**
	 * 自定义屏蔽一级/二级/三级类目ID，自定义数量不超过20个;使用pdd.goods.cats.get接口获取cat_id
	 */
	block_cats?: number[];

	/**
	 * 商品类目ID，使用pdd.goods.cats.get接口获取
	 */
	cat_id?: number;

	/**
	 * 自定义参数，为链接打上自定义标签；自定义参数最长限制64个字节；格式为：  {"uid":"11111","sid":"22222"} ，其中 uid 用户唯一标识，可自行加密后传入，每个用户仅且对应一个标识，必填； sid 上下文信息标识，例如sessionId等，非必填。该json字符串中也可以加入其他自定义的key。（如果使用GET请求，请使用URLEncode处理参数）
	 */
	custom_parameters?: string;

	/**
	 * 是否使用工具商专属推广计划，默认为false
	 */
	force_auth_duo_id?: boolean;

	/**
	 * 商品主图类型：1-场景图，2-白底图，默认为0
	 */
	goods_img_type?: number;

	/**
	 * 商品goodsSign列表，例如：["c9r2omogKFFAc7WBwvbZU1ikIb16_J3CTa8HNN"]，支持通过goodsSign查询商品。goodsSign是加密后的goodsId, goodsId已下线，请使用goodsSign来替代。使用说明：https://jinbao.pinduoduo.com/qa-system?questionId=252
	 */
	goods_sign_list?: string[];

	/**
	 * 是否为品牌商品
	 */
	is_brand_goods?: boolean;

	/**
	 * 翻页时建议填写前页返回的list_id值
	 */
	list_id?: string;

	/**
	 * 店铺类型，1-个人，2-企业，3-旗舰店，4-专卖店，5-专营店，6-普通店（未传为全部）
	 */
	merchant_type?: number;

	/**
	 * 店铺类型数组，例如：[1,2]
	 */
	merchant_type_list?: number[];

	/**
	 * 商品标签类目ID，与keyword字段选填一个或全部填写，使用pdd.goods.opt.get获取
	 */
	opt_id?: number;

	/**
	 * 默认值1，商品分页数
	 */
	page?: number;

	/**
	 * 默认100，每页商品数量
	 */
	page_size?: number;

	/**
	 * 筛选范围列表 样例：[{"range_id":0,"range_from":1,"range_to":1500},{"range_id":1,"range_from":1,"range_to":1500}]
	 */
	range_list?: {
		range_id: number;
		range_from: number;
		range_to: number;
	}[];

	/**
	 * 风控参数
	 */
	risk_params?: Record<string, string>;

	/**
	 * 排序方式:0-综合排序;1-按佣金比率升序;2-按佣金比例降序;3-按价格升序;4-按价格降序;5-按销量升序;6-按销量降序;7-优惠券金额排序升序;8-优惠券金额排序降序;9-券后价升序排序;10-券后价降序排序;11-按照加入多多进宝时间升序;12-按照加入多多进宝时间降序;13-按佣金金额升序排序;14-按佣金金额降序排序;15-店铺描述评分升序;16-店铺描述评分降序;17-店铺物流评分升序;18-店铺物流评分降序;19-店铺服务评分升序;20-店铺服务评分降序;27-描述评分击败同类店铺百分比升序，28-描述评分击败同类店铺百分比降序，29-物流评分击败同类店铺百分比升序，30-物流评分击败同类店铺百分比降序，31-服务评分击败同类店铺百分比升序，32-服务评分击败同类店铺百分比降序
	 */
	sort_type?: number;

	/**
	 * 是否使用个性化推荐，true表示使用，false表示不使用，默认true。
	 */
	use_customized?: boolean;

	/**
	 * 是否只返回优惠券的商品，false返回所有商品，true只返回有优惠券的商品
	 */
	with_coupon?: boolean;
}

/**
 * 商品详情查询参数
 */
export interface GoodsDetailParams {
	/**
	 * 自定义参数，为链接打上自定义标签；自定义参数最长限制64个字节；格式为：  {"uid":"11111","sid":"22222"} ，其中 uid 用户唯一标识，可自行加密后传入，每个用户仅且对应一个标识，必填； sid 上下文信息标识，例如sessionId等，非必填。该json字符串中也可以加入其他自定义的key。（如果使用GET请求，请使用URLEncode处理参数）
	 */
	custom_parameters?: string;

	/**
	 * 商品主图类型：1-场景图，2-白底图，默认为0
	 */
	goods_img_type?: number;

	/**
	 * 商品goodsSign，支持通过goodsSign查询商品。goodsSign是加密后的goodsId, goodsId已下线，请使用goodsSign来替代。使用说明：https://jinbao.pinduoduo.com/qa-system?questionId=252
	 */
	goods_sign?: string;

	/**
	 * 是否获取sku信息，默认false不返回。（特殊渠道权限，需额外申请）
	 */
	need_sku_info?: boolean;

	/**
	 * 风控参数
	 */
	risk_params?: Record<string, string>;

	/**
	 * 搜索id，建议填写，提高收益。来自pdd.ddk.goods.recommend.get、pdd.ddk.goods.search、pdd.ddk.top.goods.list.query等接口
	 */
	search_id?: string;

	/**
	 * 招商多多客 ID，非必填
	 */
	zs_duo_id?: number;
}

/**
 * 商品推荐查询参数
 */
export interface GoodsRecommendGetParams {
	/**
	 * 活动商品标记数组，例：[4,7]，4-秒杀，7-百亿补贴，10851-千万补贴，11879-千万神券，10913-招商礼金商品，31-品牌黑标，10564-精选爆品-官方直推爆款，10584-精选爆品-团长推荐，24-品牌高佣，其他的值请忽略
	 */
	activity_tags?: number[];

	/**
	 * 猜你喜欢场景的商品类目，20100-百货，20200-母婴，20300-食品，20400-女装，20500-电器，20600-鞋包，20700-内衣，20800-美妆，20900-男装，21000-水果，21100-家纺，21200-文具,21300-运动,21400-虚拟,21500-汽车,21600-家装,21700-家具,21800-医药;
	 */
	cat_id?: number;

	/**
	 * 进宝频道推广商品: 1-今日销量榜,3-相似商品推荐,4-猜你喜欢(和进宝网站精选一致),5-实时热销榜,6-实时收益榜。默认值5
	 */
	channel_type?: number;

	/**
	 * 自定义参数，为链接打上自定义标签；自定义参数最长限制64个字节；格式为： {"uid":"11111","sid":"22222"} ，其中 uid 为用户唯一标识，可自行加密后传入，每个用户仅且对应一个标识，必填； sid 为上下文信息标识，例如sessionId等，非必填。该json字符串中也可以加入其他自定义的key。
	 */
	custom_parameters?: string;

	/**
	 * 是否使用工具商专属推广计划，默认为false
	 */
	force_auth_duo_id?: boolean;

	/**
	 * 商品主图类型：1-场景图，2-白底图，默认为0
	 */
	goods_img_type?: number;

	/**
	 * 商品goodsSign列表，相似商品推荐场景时必传，仅取数组的第一位，例如：["c9r2omogKFFAc7WBwvbZU1ikIb16_J3CTa8HNN"]。goodsSign是加密后的goodsId, goodsId已下线，请使用goodsSign来替代。使用说明：https://jinbao.pinduoduo.com/qa-system?questionId=252
	 */
	goods_sign_list?: string[];

	/**
	 * 一页请求数量；默认值 ： 20
	 */
	limit?: number;

	/**
	 * 翻页时建议填写前页返回的list_id值
	 */
	list_id?: string;

	/**
	 * 从多少位置开始请求；默认值 ： 0
	 */
	offset?: number;

	/**
	 * 风控参数
	 */
	risk_params?: Record<string, string>;
}

/**
 * 商品推广链接生成参数
 */
export interface GoodsPromUrlGenerateParams {
	/**
	 * 多多礼金ID
	 */
	cash_gift_id?: number;

	/**
	 * 自定义礼金标题，用于向用户展示渠道专属福利，不超过12个字
	 */
	cash_gift_name?: string;

	/**
	 * 是否使用多多客专属推广计划
	 */
	force_duo_id?: boolean;

	/**
	 * 是否生成带授权的单品链接。如果未授权，则会走授权流程
	 */
	generate_authority_url?: boolean;

	/**
	 * 是否生成店铺收藏券推广链接
	 */
	generate_mall_collect_coupon?: boolean;

	/**
	 * 是否生成qq小程序
	 */
	generate_qq_app?: boolean;

	/**
	 * 是否返回 schema URL
	 */
	generate_schema_url?: boolean;

	/**
	 * 是否生成短链接，true-是，false-否
	 */
	generate_short_url?: boolean;

	/**
	 * 是否生成拼多多福利券微信小程序推广信息
	 */
	generate_we_app?: boolean;

	/**
	 * 是否生成小程序schema长链
	 */
	generate_we_app_long_link?: boolean;

	/**
	 * 商品goodsSign列表，例如：["c9r2omogKFFAc7WBwvbZU1ikIb16_J3CTa8HNN"]，支持批量生链。goodsSign是加密后的goodsId, goodsId已下线，请使用goodsSign来替代。使用说明：https://jinbao.pinduoduo.com/qa-system?questionId=252
	 */
	goods_sign_list?: string[];

	/**
	 * 素材ID，可以通过商品详情接口获取商品素材信息
	 */
	material_id?: string;

	/**
	 * true--生成多人团推广链接 false--生成单人团推广链接（默认false）1、单人团推广链接：用户访问单人团推广链接，可直接购买商品无需拼团。2、多人团推广链接：用户访问双人团推广链接开团，若用户分享给他人参团，则开团者和参团者的佣金均结算给推手
	 */
	multi_group?: boolean;

	/**
	 * 推广位ID列表，例如：["60005_612"]
	 */
	p_id_list?: string[];

	/**
	 * 搜索id，建议填写，提高收益。来自pdd.ddk.goods.recommend.get、pdd.ddk.goods.search、pdd.ddk.top.goods.list.query等接口
	 */
	search_id?: string;

	/**
	 * 特殊参数
	 */
	special_params?: Record<string, string>;

	/**
	 * 生成商品链接类型 0-默认 1-百补相似品列表
	 */
	url_type?: number;
}

/**
 * 多多进宝转链接口参数
 */
export interface GoodsZsUnitUrlGenParams {
	/**
	 * 需转链的链接，支持拼多多商品链接、进宝长链/短链（即为pdd.ddk.goods.promotion.url.generate接口生成的长短链）
	 */
	source_url: string;

	/**
	 * 自定义参数，为链接打上自定义标签；自定义参数最长限制64个字节；格式为：  {"uid":"11111","sid":"22222"} ，其中 uid 用户唯一标识，可自行加密后传入，每个用户仅且对应一个标识，必填； sid 上下文信息标识，例如sessionId等，非必填。该json字符串中也可以加入其他自定义的key。（如果使用GET请求，请使用URLEncode处理参数）
	 */
	custom_parameters?: string;

	/**
	 * 是否返回 schema URL
	 */
	generate_schema_url?: boolean;

	/**
	 * 是否生成微信LongLink
	 */
	generate_we_app_long_link?: boolean;

	/**
	 * 是否生成微信shortlink链接，仅支持单品，单个渠道每天生成的shortLink数量有限，请合理生成shortLink链接
	 */
	generate_short_link?: boolean;
}

/**
 * 生成商城推广链接参数
 */
export interface CmsPromUrlGenerateParams {
	/**
	 * 0, "1.9包邮"；1, "今日爆款"； 2, "品牌清仓"； 4,"PC端专属商城(已下线,会生成默认商城)"
	 */
	channel_type?: number;

	/**
	 * 自定义参数，为链接打上自定义标签；自定义参数最长限制64个字节；格式为：  {"uid":"11111","sid":"22222"} ，其中 uid 用户唯一标识，可自行加密后传入，每个用户仅且对应一个标识，必填； sid 上下文信息标识，例如sessionId等，非必填。该json字符串中也可以加入其他自定义的key
	 */
	custom_parameters?: string;

	/**
	 * 是否生成手机跳转链接。true-是，false-否，默认false
	 */
	generate_mobile?: boolean;

	/**
	 * 是否返回 schema URL
	 */
	generate_schema_url?: boolean;

	/**
	 * 是否生成短链接，true-是，false-否
	 */
	generate_short_url?: boolean;

	/**
	 * 是否生成拼多多福利券微信小程序推广信息
	 */
	generate_we_app?: boolean;

	/**
	 * 搜索关键词
	 */
	keyword?: string;

	/**
	 * 单人团多人团标志。true-多人团，false-单人团 默认false
	 */
	multi_group?: boolean;
}

/**
 * 推广位生成参数
 */
export interface GoodsPidGenerateParams {
	/**
	 * 要生成的推广位数量，默认为10，范围为：1~100
	 */
	number: number;

	/**
	 * 推广位名称，例如["名称1","名称2"]
	 */
	p_id_name_list?: string[];

	/**
	 * 媒体id
	 */
	media_id?: number;
}

/**
 * 推广位查询参数
 */
export interface GoodsPidQueryParams {
	/**
	 * 返回的页数
	 */
	page?: number;

	/**
	 * 返回的每页推广位数量
	 */
	page_size?: number;

	/**
	 * 推广位列表，例如：["60005_612"]
	 */
	pid_list?: string[];
}

/**
 * 获取订单详情参数
 */
export interface OrderDetailGetParams {
	/**
	 * 订单号
	 */
	order_sn: string;

	/**
	 * 订单类型：1-推广订单；2-直播间订单
	 */
	query_order_type?: number;
}

/**
 * 按照更新时间段增量同步推广订单信息参数
 */
export interface OrderListIncrementGetParams {
	/**
	 * 最近24小时内多多进宝商品订单更新时间--查询时间结束。note：此时间为时间戳，指格林威治时间 1970 年01 月 01 日 00 时 00 分 00 秒(北京时间 1970 年 01 月 01 日 08 时 00 分 00 秒)起至现在的总秒数
	 */
	end_update_time: number;

	/**
	 * 第几页，从1到10000，默认1，注：使用最后更新时间范围增量同步时，必须采用倒序的分页方式（从最后一页往回取）才能避免漏单问题。
	 */
	page?: number;

	/**
	 * 返回的每页结果订单数，默认为100，范围为10到100，建议使用40~50，可以提高成功率，减少超时数量。
	 */
	page_size?: number;

	/**
	 * 最近24小时内多多进宝商品订单更新时间--查询时间开始。note：此时间为时间戳，指格林威治时间 1970 年01 月 01 日 00 时 00 分 00 秒(北京时间 1970 年 01 月 01 日 08 时 00 分 00 秒)起至现在的总秒数
	 */
	start_update_time: number;
}

/**
 * 拼多多主站频道推广接口参数
 */
export interface ResourceUrlGenParams {
	/**
	 * 自定义参数，为链接打上自定义标签；自定义参数最长限制64个字节；格式为： {"uid":"11111","sid":"22222"} ，其中 uid 用户唯一标识，可自行加密后传入，每个用户仅且对应一个标识，必填； sid 上下文信息标识，例如sessionId等，非必填。该json字符串中也可以加入其他自定义的key
	 */
	custom_parameters?: string;

	/**
	 * 是否返回 schema URL
	 */
	generate_schema_url?: boolean;

	/**
	 * 是否生成拼多多福利券微信小程序推广信息
	 */
	generate_we_app?: boolean;

	/**
	 * 频道来源：4-限时秒杀,39997-充值中心, 39998-活动转链，39996-百亿补贴，39999-电器城，40000-领券中心，50005-火车票
	 */
	resource_type?: number;

	/**
	 * 原链接
	 */
	url?: string;
}

/**
 * 查询是否绑定备案参数
 */
export interface MemberAuthorityQueryParams {
	/**
	 * 推广位id，如果不传则使用环境变量中的PID
	 */
	pid?: string;

	/**
	 * 自定义参数，为链接打上自定义标签；自定义参数最长限制64个字节；格式为： {"uid":"11111","sid":"22222"} ，其中 uid 用户唯一标识，可自行加密后传入，每个用户仅且对应一个标识，必填； sid 上下文信息标识，例如sessionId等，非必填。该json字符串中也可以加入其他自定义的key
	 */
	custom_parameters?: string;
}
