import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 商品详情查询测试客户端
 * 用于测试拼多多商品详情查询功能
 */
export class GoodsDetailTest extends BaseTestClient {
  private goodsSign: string;

  /**
   * 构造函数
   * @param goodsSign 要测试的商品sign
   */
  constructor(goodsSign: string) {
    super();
    this.goodsSign = goodsSign;
  }

  /**
   * 执行商品详情查询测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.goods.detail", {
        goods_sign: this.goodsSign
      });

      // 验证响应
      if (result.content?.[0]?.text) {
        console.log('商品详情查询测试成功!');
        console.log(`查询结果: ${result.content[0].text}`);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const testGoodsSign = 'E9j2AdzphfZiwIxFwvTel54m3Q953ZdY_JQcAb6DCCX';
  const test = new GoodsDetailTest(testGoodsSign);
  test.runTest().catch(console.error);
} 