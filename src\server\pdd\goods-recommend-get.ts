import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { GoodsRecommendGetParams } from './types.js';

/**
 * 商品推荐查询 API
 */
export class GoodsRecommendGet extends BaseApi {
	/**
	 * 查询推荐商品
	 * @param params 查询参数
	 * @returns 查询结果
	 */
	async query(params: GoodsRecommendGetParams): Promise<any> {
		try {
			// 调用API
			return this.callApi('pdd.ddk.oauth.goods.recommend.get', params);
		} catch (error) {
			log(`查询推荐商品失败: ${error}`);
			throw error;
		}
	}
}
