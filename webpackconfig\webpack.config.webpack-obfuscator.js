import path from 'path';
import { fileURLToPath } from 'url';
import nodeExternals from 'webpack-node-externals';
import TerserPlugin from 'terser-webpack-plugin';
import { ShebangPlugin } from '../scripts/webpack-shebang-plugin.js';
import WebpackObfuscator from 'webpack-obfuscator';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default {
  mode: 'production',

  entry: {
    index: path.resolve(__dirname, '../src/index.ts'),
    cli: path.resolve(__dirname, '../src/cli.ts')
  },

  output: {
    path: path.resolve(__dirname, '../out/dist'),
    filename: '[name].js',
    clean: {
      keep: /(\.npmignore|package\.json|README\.md)$/i, // 保留这些文件
    },
    // 使用 CommonJS 格式
    library: {
      type: 'commonjs2'
    },
    // 不启用 ESM 输出
    module: false,
    // 环境不为 module
    environment: {
      module: false
    },
    // 使用 CommonJS 格式的 chunk
    chunkFormat: 'commonjs',
    // 添加 .js 扩展名
    chunkFilename: '[name].js'
  },

  experiments: {
    // 不启用 output.module
    outputModule: false
  },

  resolve: {
    extensions: ['.ts', '.js', '.json','.mjs'],
    alias: {
      '@': path.resolve(__dirname, '../src'),
      // 添加 async 模块的别名
      'async/forEach': path.resolve(__dirname, '../node_modules/async/forEach.js'),
      'async/series': path.resolve(__dirname, '../node_modules/async/series.js'),
      // 确保正确解析 async 模块
      'async': path.resolve(__dirname, '../node_modules/async'),
      // 确保正确解析 logform 模块
      'logform/json': path.resolve(__dirname, '../node_modules/logform/json.js'),
      // 确保正确解析 @colors/colors 模块
      '@colors/colors/safe': path.resolve(__dirname, '../node_modules/@colors/colors/safe.js'),
    },
    extensionAlias: {
      '.js': ['.ts', '.js']
    },
    // 添加 mainFields 配置，确保正确解析 ESM 模块
    mainFields: ['module', 'main'],
    // 为 ws 模块的原生依赖提供 fallback
    fallback: {
      // 对于原生模块，使用空对象
      '../build/Release/bufferutil': false,
      '../build/default/bufferutil': false,
      '../build/Release/validation': false,
      '../build/default/validation': false
    }
  },

  target: 'node',

  // 添加 Node.js 兼容性
  node: {
    __dirname: false,
    __filename: false,
    global: true
  },

  // 使用 nodeExternals 排除所有 node_modules 中的依赖
  externals: [nodeExternals({
    modulesDir: path.resolve(__dirname, '../node_modules'),
  })],

  module: {
    rules: [
      {
        test: /\.ts$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              transpileOnly: true
            }
          }
        ],
        exclude: /node_modules/
      }
    ]
  },

  plugins: [
    // 添加 shebang 行到 CLI 文件
    new ShebangPlugin({
      shebang: '#!/usr/bin/env node',
      files: ['cli.js', 'index.js']
    }),

    // 添加 webpack-obfuscator 插件
    new WebpackObfuscator({
      // 基本混淆选项
      compact: true,                    // 压缩代码
      controlFlowFlattening: true,      // 控制流扁平化
      controlFlowFlatteningThreshold: 0.5, // 控制流扁平化阈值
      deadCodeInjection: true,          // 注入死代码
      deadCodeInjectionThreshold: 0.3,  // 死代码注入阈值
      debugProtection: false,           // 调试保护（可能会导致无限循环）
      disableConsoleOutput: true,       // 禁用控制台输出
      identifierNamesGenerator: 'hexadecimal', // 标识符名称生成器
      log: false,                       // 日志
      renameGlobals: false,             // 重命名全局变量
      rotateStringArray: true,          // 旋转字符串数组
      selfDefending: false,             // 自我保护（可能会导致问题）
      stringArray: true,                // 字符串数组
      stringArrayEncoding: ['base64'],  // 字符串数组编码，使用 base64 处理所有字符
      stringArrayThreshold: 1,          // 字符串数组阈值，设为1确保所有字符串都被处理
      transformObjectKeys: true,        // 转换对象键
      unicodeEscapeSequence: true       // 启用 Unicode 转义序列
    }, ["^SERVER_SECRET$", "^ENV_URL$", "^ENV_SECRET$"])
  ],

  optimization: {
    minimize: true,
    minimizer: [
      new TerserPlugin({
        terserOptions: {
          ecma: 2020, // 支持 ES2020 语法
          compress: {
            drop_console: true, // 移除控制台输出
            pure_funcs: ['console.log', 'console.info', 'console.debug', 'console.warn'], // 移除特定函数调用
          },
          mangle: true, // 启用变量名混淆
          keep_classnames: false, // 不保留类名
          keep_fnames: false, // 不保留函数名
          output: {
            comments: false, // 移除注释
            ascii_only: true, // 使用 ASCII 字符
          }
        },
        // 排除可能导致问题的文件
        exclude: [/node_modules/, /jd-sdk/, /taobao-sdk/, /pdd-sdk/, /pdd-sdk-new/],
      }),
    ],
  },

  devtool: false, // 禁用 source map 以增强混淆效果

  // 忽略特定警告
  ignoreWarnings: [
    // 忽略与 ws 模块相关的原生模块警告
    /Failed to parse source map/,
    /Can't resolve '..\build\Release\bufferutil'/,
    /Can't resolve '..\build\default\bufferutil'/,
    /Can't resolve '..\build\Release\validation'/,
    /Can't resolve '..\build\default\validation'/,
    // 忽略动态 require 警告
    /Critical dependency: the request of a dependency is an expression/
  ]
};
