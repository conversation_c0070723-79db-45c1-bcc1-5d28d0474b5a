/**
 * JWT-AES双重加密解密测试
 */

import { 
  generateToken,
  encryptWithAes, 
  decryptWithAes,
  encryptWithJwtAndAes,
  decryptWithAesAndJwt
} from '../src/JWT/jwt-utils.js';
import * as dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

// 测试数据
const testData = {
  TEST_VAR1: 'test-value-1',
  TEST_VAR2: 'test-value-2',
  TEST_SECRET: 'test-secret-key',
  SERVER_SECRET: 'server-secret-key'
};

// 测试密钥
const jwtSecret = process.env.ENV_SECRET || 'url:mcp.sinataoke.cn';
const aesKey = process.env.ENV_AES_KEY || 'liuliangzhengsinataoke';

console.log('===== JWT-AES双重加密解密测试 =====');
console.log('测试数据:', testData);

// 测试JWT加密
console.log('\n1. 测试JWT加密');
const jwtToken = generateToken(testData, {
  secret: jwtSecret,
  expiresIn: '1h'
});
console.log('JWT加密结果:', jwtToken);

// 测试AES加密和解密
console.log('\n2. 测试AES加密');
const encryptedWithAes = encryptWithAes(jwtToken, { key: aesKey });
console.log('AES加密结果:', encryptedWithAes);

console.log('\n3. 测试AES解密');
const decryptedWithAes = decryptWithAes(encryptedWithAes, { key: aesKey });
console.log('AES解密结果:', decryptedWithAes);

// 验证AES解密结果
if (decryptedWithAes === jwtToken) {
  console.log('\n✅ AES测试通过: 解密后的数据与JWT令牌一致');
} else {
  console.error('\n❌ AES测试失败: 解密后的数据与JWT令牌不一致');
}

// 测试JWT-AES双重加密和解密
console.log('\n4. 测试JWT-AES双重加密');
const encryptedWithJwtAndAes = encryptWithJwtAndAes(
  testData,
  jwtSecret,
  aesKey,
  { expiresIn: '1h' }
);
console.log('JWT-AES双重加密结果:', encryptedWithJwtAndAes);

console.log('\n5. 测试AES-JWT双重解密');
const decryptedWithAesAndJwt = decryptWithAesAndJwt(
  encryptedWithJwtAndAes,
  jwtSecret,
  aesKey
);
console.log('AES-JWT双重解密结果:', decryptedWithAesAndJwt);

// 验证JWT-AES双重解密结果
let allMatch = true;
for (const key in testData) {
  if (testData[key] !== decryptedWithAesAndJwt[key]) {
    console.error(`❌ 键 ${key} 不匹配: 期望 ${testData[key]}, 实际 ${decryptedWithAesAndJwt[key]}`);
    allMatch = false;
  }
}

if (allMatch) {
  console.log('\n✅ JWT-AES双重加密测试通过: 解密后的数据与原始数据一致');
} else {
  console.error('\n❌ JWT-AES双重加密测试失败: 解密后的数据与原始数据不一致');
}
