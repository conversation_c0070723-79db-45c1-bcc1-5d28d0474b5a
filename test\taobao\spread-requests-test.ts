import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 批量转短链测试客户端
 * 用于测试淘宝客长链批量转短链功能
 */
export class SpreadRequestsTest extends BaseTestClient {
	private urls: string[];

	/**
	 * 构造函数
	 * @param urls 要测试的URL列表
	 */
	constructor(urls: string[]) {
		super();
		this.urls = urls;
	}

	/**
	 * 执行批量转短链测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			console.log(
				'发送批量转短链请求参数:',
				JSON.stringify(this.urls, null, 2)
			);

			// 将URL列表转换为符合API要求的格式
			const requests = this.urls.map((url) => ({ url }));

			// 调用工具并获取结果
			const result = await this.callTool('taobao.spreadRequests', {
				requests,
			});

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('批量转短链测试成功!');
				console.log(`转换结果: ${result.content[0].text}`);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 如果直接运行此文件,则执行测试
if (currentFilePath === entryPointPath) {
	const testUrls = [
		'https://uland.taobao.com/coupon/edetail?e=VAcEd8hlY7%2BlhHvvyUNXZfh8CuWt5YH5OVuOuRD5gLJMmdsrkidbOWBzzpT26idJjb8PVLJrrfVmkK8m1Lf96N5gsRQs4KDlRbFElPuBvC8Xp2tgktmllZhvQ7Yzbl%2FRRSHvQe2jOLZ9pbNCYX0I%2BPP%2BWUTgK%2F%2B0P%2F%2F3OBTopyRMparU6DU7oYEiF329nymuF3YqiM2e1TzM%2FqkOhugh%2FmTjVvFK4MTonmVukAs%2BtPgdoJZM0jgT24kkYlT884OIA%2B3XzUtm8jCRrX0U47HLHBHANxzzGL7QF3YqiM2e1Ty5OllfkA7FZ77dcTJ5IcEvcXnGlPtu3FCjGwG5%2FUm0S6J7%2BkHL3AEW&traceId=2127fe1f17435637452087841e46eb&&union_lens=lensId:TAPI@1743563745@210497fa_11f7_195f47f680f_37f0@01&activityId=756354c0288446d895a36a51240f51ca',
	];

	console.log('启动批量转短链测试');
	const test = new SpreadRequestsTest(testUrls);
	test.runTest().catch(console.error);
}
