/**
 * 拼多多开放平台SDK客户端
 */

const GoodsDetail = require('./api/goods-detail');
const GoodsPidGenerate = require('./api/goods-pid-generate');
const GoodsPidQuery = require('./api/goods-pid-query');
const GoodsPromUrlGenerate = require('./api/goods-prom-url-generate');
const GoodsSearch = require('./api/goods-search');
const GoodsZsUnitUrlGenerate = require('./api/goods-zs-unit-url-generate');
const MemberAuthorityQuery = require('./api/member-authority-query');
const OrderDetail = require('./api/order-detail');
const ResourceUrlGenerate = require('./api/resource-url-generate');

class PddClient {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   * @param {String} config.clientId 应用ID
   * @param {String} config.clientSecret 应用密钥
   * @param {String} [config.accessToken] 访问令牌
   * @param {Boolean} [config.debug] 是否启用调试模式
   */
  constructor(config) {
    if (!config.clientId || !config.clientSecret) {
      throw new Error('参数错误: clientId和clientSecret不能为空');
    }

    this.config = config;
    
    // 初始化各API实例
    this._initApis();
  }

  /**
   * 初始化各API实例
   * @private
   */
  _initApis() {
    // 商品详情查询API
    this.goodsDetail = new GoodsDetail(this.config);
    
    // 推广位生成API
    this.goodsPidGenerate = new GoodsPidGenerate(this.config);
    
    // 推广位查询API
    this.goodsPidQuery = new GoodsPidQuery(this.config);
    
    // 商品推广链接生成API
    this.goodsPromUrlGenerate = new GoodsPromUrlGenerate(this.config);
    
    // 商品搜索API
    this.goodsSearch = new GoodsSearch(this.config);
    
    // 多多进宝转链API
    this.goodsZsUnitUrlGenerate = new GoodsZsUnitUrlGenerate(this.config);
    
    // 会员备案查询API
    this.memberAuthorityQuery = new MemberAuthorityQuery(this.config);
    
    // 订单详情查询API
    this.orderDetail = new OrderDetail(this.config);
    
    // 主站频道推广API
    this.resourceUrlGenerate = new ResourceUrlGenerate(this.config);
  }
}

module.exports = PddClient; 