import { PromotionByUnionid } from './jd/promotion-by-unionid.js';
import { GoodsQuery } from './jd/goods-query.js';
import { CouponQuery } from './jd/coupon-query.js';

// 初始化京东API实例
const jdPromotionApi = new PromotionByUnionid();
const jdGoodsApi = new GoodsQuery();
const jdCouponApi = new CouponQuery();

// 注册京东相关工具
export const jdTools = [
  // 京东商品查询API
  {
    name: 'jd.goods.query',
    description: '查询京东商品信息，支持按关键词、SKU ID、类目等方式搜索',
    parameters: {
      type: 'object',
      properties: {
        keyword: {
          type: 'string',
          description: '搜索关键词，支持多个关键词，用英文逗号分隔'
        },
        skuIds: {
          type: 'string',
          description: '京东商品ID，多个用英文逗号分隔，最多100个'
        },
        cid1: {
          type: 'number',
          description: '一级类目ID'
        },
        cid2: {
          type: 'number',
          description: '二级类目ID'
        },
        cid3: {
          type: 'number',
          description: '三级类目ID'
        },
        owner: {
          type: 'string',
          description: '商品类型，g:自营，p:POP'
        },
        pageIndex: {
          type: 'number',
          description: '页码，默认1'
        },
        pageSize: {
          type: 'number',
          description: '每页数量，默认20，上限50'
        },
        sortName: {
          type: 'string',
          description: '排序字段(price:单价,commissionShare:佣金比例,commission:佣金,inOrderCount30Days:30天引单量)'
        },
        sort: {
          type: 'string',
          description: '排序方式，asc:升序，desc:降序，默认降序'
        }
      }
    },
    async handler(params: any) {
      console.log('调用京东商品查询API');
      return await jdGoodsApi.queryGoods(params);
    }
  },
  
  // 京东优惠券查询API
  {
    name: 'jd.coupon.query',
    description: '根据优惠券链接查询京东优惠券的详细信息',
    parameters: {
      type: 'object',
      properties: {
        couponUrls: {
          type: 'array',
          items: {
            type: 'string'
          },
          description: '优惠券链接集合，最多支持100个'
        }
      },
      required: ['couponUrls']
    },
    async handler(params: any) {
      console.log('调用京东优惠券查询API');
      return await jdCouponApi.queryCoupons(params);
    }
  },
]; 