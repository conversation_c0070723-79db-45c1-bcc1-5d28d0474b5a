import fs from 'fs';
import { verifyToken } from './src/JWT/jwt-utils.js';

// 读取JWT令牌
const token = fs.readFileSync('test-env.jwt', 'utf8').trim();

// 密钥
const secret = 'test-secret-key';

try {
  // 解密JWT令牌
  const decoded = verifyToken(token, {
    secret,
    audience: 'mcp-server',
    issuer: 'mcp-env-encryptor'
  });
  
  console.log('解密成功!');
  console.log('解密结果:');
  console.log(JSON.stringify(decoded, null, 2));
} catch (error) {
  console.error('解密失败:', error instanceof Error ? error.message : String(error));
}
