import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getEnvVar } from '../../utils/env.js';
import {
	LinkParseParams,
	TaobaoApiResponse,
	LinkParseResponseData,
} from './types.js';

/**
 * 淘宝链接解析API
 * 提供链接解析功能
 */
export class LinkParser extends BaseApi {
	/**
	 * 解析淘宝链接或淘口令
	 * @param params 解析参数
	 * @returns 解析响应
	 */
	async parseLink(
		params: LinkParseParams
	): Promise<TaobaoApiResponse<LinkParseResponseData>> {
		try {
			log(`执行链接解析: ${JSON.stringify(params)}`);

			// 获取广告位ID，优先使用参数adzone_id，如果没有则使用环境变量
			const adzone_id = params.adzone_id || getEnvVar('TAOBAO_ADZONE_ID');

			// 直接使用参数，基类会处理数组参数和空参过滤
			const apiParams = {
				...params,
				adzone_id,
			};

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.sc.general.link.parse',
				apiParams
			);

			log(`链接解析结果: ${JSON.stringify(result)}`);

			// 返回响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`链接解析失败: ${error}`);
			throw error;
		}
	}
}
