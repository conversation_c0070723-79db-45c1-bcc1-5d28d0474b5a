import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getRequiredEnvVar } from '../../utils/env.js';
import { ResourceUrlGenParams } from './types.js';

/**
 * 拼多多主站频道推广接口
 */
export class ResourceUrlGen extends BaseApi {
	/**
	 * 生成拼多多主站频道推广链接
	 * @param params 请求参数
	 * @returns 推广链接结果
	 */
	async generateUrl(
		params: Omit<ResourceUrlGenParams, 'resource_type'>
	): Promise<any> {
		try {
			// 从环境变量获取 PID（必填参数）
			const pid = getRequiredEnvVar('PDD_PID');

			// 合并参数
			const fullParams: Record<string, any> = {
				...params,
				pid,
			};

			log(
				`生成拼多多主站频道推广链接参数: ${JSON.stringify(fullParams)}`
			);

			// 调用API
			return this.callApi('pdd.ddk.oauth.resource.url.gen', fullParams);
		} catch (error) {
			log(`生成拼多多主站频道推广链接失败: ${error}`);
			throw error;
		}
	}
}
