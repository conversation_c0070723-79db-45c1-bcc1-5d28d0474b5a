/**
 * 拼多多订单详情查询API测试
 */

// 导入依赖
const { PddClient } = require('../index');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 加载环境变量（从项目根目录的.env文件）
const envPath = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log(`已加载环境变量文件: ${envPath}`);
} else {
  console.warn(`警告: 环境变量文件不存在: ${envPath}`);
}

// 检查必要的环境变量是否存在
const requiredEnvVars = [
  'PDD_CLIENT_ID', 
  'PDD_CLIENT_SECRET', 
  'PDD_SESSION_TOKEN'
];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error(`缺少必要的环境变量: ${missingEnvVars.join(', ')}`);
  console.error('请确保.env文件中包含这些变量');
  process.exit(1);
}

// 初始化客户端
const client = new PddClient({
  clientId: process.env.PDD_CLIENT_ID,
  clientSecret: process.env.PDD_CLIENT_SECRET,
  accessToken: process.env.PDD_SESSION_TOKEN,
  debug: true // 启用调试模式
});

// 测试订单详情查询
async function testOrderDetail() {
  console.log('======= 拼多多订单详情查询测试 =======');
  console.log('客户端配置:');
  console.log('- clientId:', process.env.PDD_CLIENT_ID);
  console.log('- accessToken:', maskToken(process.env.PDD_SESSION_TOKEN));
  console.log('\n开始查询订单详情...');
  
  // 构建请求参数
  const options = {
    order_sn: '250407-497675221411706'
  };
  
  console.log('请求参数:', JSON.stringify(options, null, 2));
  
  try {
    // 执行请求
    const result = await client.orderDetail.get(options);
    console.log('\n查询结果:', JSON.stringify(result, null, 2));
  } catch (error) {
    console.error('\n查询失败:', error.message);
    if (error.response) {
      console.error('错误详情:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 辅助函数：掩码显示令牌
function maskToken(token) {
  if (!token) return 'undefined';
  return token.substr(0, 6) + '****' + token.substr(-6);
}

// 执行测试
testOrderDetail(); 