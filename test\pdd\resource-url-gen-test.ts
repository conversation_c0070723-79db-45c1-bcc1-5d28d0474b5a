import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 拼多多主站频道推广测试客户端
 * 用于测试拼多多主站频道推广功能
 */
export class ResourceUrlGenTest extends BaseTestClient {
  /**
   * 执行拼多多主站频道推广测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.resource.url.gen", {
        resource_type: 39996, // 百亿补贴
        generate_schema_url: true,
        generate_we_app: true
      });

      // 打印结果
      if (result.content?.[0]?.text) {
        console.log('拼多多主站频道推广测试成功!');
        console.log('推广链接:');
        console.log(result.content[0].text);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const test = new ResourceUrlGenTest();
  test.runTest().catch(console.error);
}
