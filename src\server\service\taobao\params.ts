import { z } from 'zod';

/**
 * 淘宝API参数定义
 * 集中管理所有淘宝API的参数定义
 */
export const TaobaoParams = {
	/**
	 * 淘宝物料搜索参数
	 * 基于淘宝开放平台API文档：https://open.taobao.com/api.htm?docId=64758&docType=2&scopeId=13991
	 */
	materialSearch: {
		start_dsr: z
			.number()
			.optional()
			.describe(
				'【非必填】商品筛选-店铺dsr评分。筛选大于等于当前设置的店铺dsr评分的商品0-50000之间，示例值：10'
			),
		page_size: z
			.number()
			.optional()
			.describe('【非必填】页大小，默认20，1~100，示例值：20'),
		page_no: z
			.number()
			.optional()
			.describe('【非必填】第几页，默认：１，示例值：1'),
		end_tk_rate: z
			.number()
			.optional()
			.describe(
				'【非必填】商品筛选-淘客收入比率上限(商品佣金比率+补贴比率)。如：1234表示12.34%，示例值：1234'
			),
		start_tk_rate: z
			.number()
			.optional()
			.describe(
				'【非必填】商品筛选-淘客收入比率下限(商品佣金比率+补贴比率)。如：1234表示12.34%，示例值：1234'
			),
		end_price: z
			.number()
			.optional()
			.describe(
				'【非必填】商品筛选-预估到手价范围上限。单位：元，示例值：10'
			),
		start_price: z
			.number()
			.optional()
			.describe(
				'【非必填】商品筛选-预估到手价范围下限。单位：元，示例值：10'
			),
		is_overseas: z
			.boolean()
			.optional()
			.describe(
				'【非必填】商品筛选-是否海外商品。true表示属于海外商品，false或不设置表示不限，示例值：false'
			),
		is_tmall: z
			.boolean()
			.optional()
			.describe(
				'【非必填】商品筛选-是否天猫商品。true表示属于天猫商品，false或不设置表示不限，示例值：false'
			),
		sort: z
			.string()
			.optional()
			.describe(
				'【非必填】排序_des（降序），排序_asc（升序），销量（total_sales），淘客收入比率（tk_rate），营销计划佣金（tk_mkt_rate）， 累计推广量（tk_total_sales），总支出佣金（tk_total_commi），预估到价格（final_promotion_price），匹配分（match），示例值：tk_rate_des'
			),
		itemloc: z
			.string()
			.optional()
			.describe('【非必填】商品筛选-所在地，示例值：杭州'),
		cat: z
			.string()
			.optional()
			.describe(
				'【非必填】商品筛选-后台类目ID。用,分割，最大10个，示例值：16,18'
			),
		q: z
			.string()
			.optional()
			.describe('【非必填】商品筛选-查询词，示例值：女装'),
		material_id: z
			.number()
			.optional()
			.describe(
				'【非必填】物料id，不传时默认物料material_id=80309，示例值：80309'
			),
		has_coupon: z
			.boolean()
			.optional()
			.describe(
				'【非必填】优惠券筛选-是否有优惠券。true表示该商品有优惠券，false或不设置表示不限，示例值：false'
			),
		ip: z
			.string()
			.optional()
			.describe(
				'【非必填】ip参数影响邮费获取，如果不传或者传入不准确，邮费无法精准提供，示例值：*********'
			),
		npx_level: z
			.number()
			.optional()
			.describe(
				'【非必填】商品筛选-牛皮癣程度。取值：1不限，2无，3轻微，示例值：2'
			),
		include_rfd_rate: z
			.boolean()
			.optional()
			.describe(
				'【非必填】商品筛选-退款率是否低于行业均值。True表示大于等于，false或不设置表示不限，示例值：true'
			),
		include_good_rate: z
			.boolean()
			.optional()
			.describe(
				'【非必填】商品筛选-好评率是否高于行业均值。True表示大于等于，false或不设置表示不限，示例值：true'
			),
		include_pay_rate_30: z
			.boolean()
			.optional()
			.describe(
				'【非必填】商品筛选-成交转化是否高于行业均值。True表示大于等于，false或不设置表示不限，示例值：true'
			),
		need_prepay: z
			.boolean()
			.optional()
			.describe(
				'【非必填】商品筛选-是否加入消费者保障。true表示加入，false或不设置表示不限，示例值：true'
			),
		need_free_shipment: z
			.boolean()
			.optional()
			.describe(
				'【非必填】商品筛选-是否包邮。true表示包邮，false或不设置表示不限，示例值：true'
			),
		device_value: z
			.string()
			.optional()
			.describe(
				'【非必填】智能匹配-设备号加密后的值（MD5加密需32位小写），示例值：xxx'
			),
		device_encrypt: z
			.string()
			.optional()
			.describe('【非必填】智能匹配-设备号加密类型：MD5，示例值：MD5'),
		device_type: z
			.string()
			.optional()
			.describe(
				'【非必填】智能匹配-设备号类型：IMEI，或者IDFA，或者UTDID，或者OAID，示例值：IMEI'
			),
		relation_id: z
			.string()
			.optional()
			.describe(
				'【非必填】渠道关系ID，仅适用于渠道推广场景，示例值：3243'
			),
		special_id: z
			.string()
			.optional()
			.describe(
				'【非必填】会员运营ID，仅适用于会员运营场景，示例值：2323'
			),
		get_topn_rate: z
			.number()
			.optional()
			.describe(
				'【非必填】是否获取前N件佣金信息，0否，1是，其他值否，示例值：0'
			),
		biz_scene_id: z
			.string()
			.optional()
			.describe(
				'【非必填】1-动态ID转链场景，2-消费者比价场景（不填默认为1），示例值：1'
			),
		promotion_type: z
			.string()
			.optional()
			.describe(
				'【非必填】1-自购省，2-推广赚（代理模式专属ID，代理模式必填，非代理模式不用填写该字段），示例值：2'
			),
		mgc_start_time: z
			.string()
			.optional()
			.describe(
				'【非必填】线报内容筛选—内容生产开始时间，13毫秒时间戳，示例值：1695281620000'
			),
		mgc_end_time: z
			.string()
			.optional()
			.describe(
				'【非必填】线报内容筛选—内容生产截止时间，13毫秒时间戳，示例值：1695281620000'
			),
		mgc_status: z
			.string()
			.optional()
			.describe(
				'【非必填】线报状态筛选，0-全部 1-过期 2-实时生效 3-未来生效 不传默认过滤有效，示例值：0'
			),
		ucrowd_id: z
			.number()
			.optional()
			.describe(
				'【非必填】人群ID，仅适用于物料评估场景material_id=41377，示例值：1'
			),
	},

	/**
	 * 淘宝链接转换参数
	 * 基于淘宝开放平台API文档：https://open.taobao.com/api.htm?docId=64756&docType=2&scopeId=13991
	 */
	linkConverter: {
		biz_scene_id: z
			.string()
			.optional()
			.describe(
				'【非必填】1-动态ID转链场景，2-消费者比价场景，4-，示例值：1'
			),
		promotion_type: z
			.string()
			.optional()
			.describe(
				'【非必填】1-自购省，2-推广赚（代理模式专属ID，代理模式必填，其它模式不用填写本字段），示例值：2'
			),
		material_list: z
			.string()
			.optional()
			.describe(
				'【非必填】物料列表，可以为url或淘口令,多个时使用英文逗号拼接传入（邀约制权限有申请门槛，权限分为①联盟推广链接转链；②天猫/淘宝复制链接转链；如有相关需求请在权限申请时同步说明），示例值：https://s.click.taobao.com/pQBDhNu,https://s.click.taobao.com/pQBDhNu'
			),
		relation_id: z
			.string()
			.optional()
			.describe(
				'【非必填】渠道管理ID（如是主站选品推广场景，必须入参该字段，且bizSceneId字段需入参2-消费者比价场景，否则二次转链失败），示例值：123456'
			),
		seller_id_list: z
			.string()
			.optional()
			.describe(
				'【非必填】卖家ID列表,多个时使用英文逗号拼接传入，示例值：123456'
			),
		item_id_list: z
			.string()
			.optional()
			.describe(
				'【非必填】商品ID列表,多个时使用英文逗号拼接传入，示例值：qYtxrMJOC8tmtM-Qq0Z65Sbbq5DqZ9'
			),
		page_id_list: z
			.string()
			.optional()
			.describe(
				'【非必填】会场ID列表,多个时使用英文逗号拼接传入，示例值：123456'
			),
		target_item: z
			.object({
				item_id_list: z
					.array(z.string())
					.optional()
					.describe(
						'【非必填】页面内定坑商品ID，用于素材-坑位还原，示例值：xxx'
					),
			})
			.optional()
			.describe('【非必填】会场页面内定坑商品'),
		item_dto: z
			.array(
				z.object({
					item_id: z
						.string()
						.optional()
						.describe(
							'【非必填】商品ID，示例值：i87a9ja90d8-09qrjcoa7qwl'
						),
					sku_id: z
						.number()
						.optional()
						.describe(
							'【非必填】入参商品id下的skuid，传入时会透传至转链结果url中，示例值：123'
						),
					is_target_coupon: z
						.number()
						.optional()
						.describe(
							'【非必填】是否指定券，1-指定 0-不指定 默认为0（邀约制权限，仅面向KA自用型工具服务商），示例值：1'
						),
					coupon_id: z
						.string()
						.optional()
						.describe(
							'【非必填】优惠券id（邀约制权限，仅面向KA自用型工具服务商），示例值：9urytzd6k4'
						),
					external_id: z
						.string()
						.optional()
						.describe(
							'【非必填】淘宝客外部用户标记，如自身系统账户ID；微信ID等，示例值：123'
						),
					dx: z
						.string()
						.optional()
						.describe(
							'【非必填】1表示商品转通用计划链接，其他值或不传表示转最优佣金率（含营销计划）链接，示例值：1'
						),
					manage_pub_id: z
						.number()
						.optional()
						.describe('【非必填】商品库账号ID，示例值：123456'),
				})
			)
			.optional()
			.describe(
				'【非必填】商品转链，包含字段：item_id, sku_id, is_target_coupon, coupon_id, external_id, dx, manage_pub_id'
			),
		page_dto: z
			.array(
				z.object({
					page_id: z
						.string()
						.optional()
						.describe('【非必填】会场ID，示例值：20356789765'),
					target_item_list: z
						.array(z.string())
						.optional()
						.describe(
							'【非必填】页面内定坑商品ID，用于素材-坑位还原，示例值：xxx'
						),
				})
			)
			.optional()
			.describe(
				'【非必填】会场页面转链，包含字段：page_id, target_item_list'
			),
		shop_dto: z
			.array(
				z.object({
					shop_id: z
						.string()
						.optional()
						.describe('【非必填】店铺ID，示例值：123456'),
				})
			)
			.optional()
			.describe('【非必填】店铺转链，包含字段：shop_id'),
		material_dto: z
			.array(
				z.object({
					material_url: z
						.string()
						.optional()
						.describe(
							'【非必填】物料链接，可以为url或淘口令,多个时使用英文逗号拼接传入（邀约制权限有申请门槛，权限分为①联盟推广链接转链；②天猫/淘宝复制链接转链；如有相关需求请在权限申请时同步说明），示例值：s.click.taobao.com'
						),
					is_target_coupon: z
						.number()
						.optional()
						.describe(
							'【非必填】是否指定券，1-指定 0-不指定 默认为0（邀约制权限，仅面向KA自用型工具服务商），示例值：1'
						),
					coupon_id: z
						.string()
						.optional()
						.describe(
							'【非必填】优惠券id（邀约制权限，仅面向KA自用型工具服务商），示例值：9urytzd6k4'
						),
				})
			)
			.optional()
			.describe(
				'【非必填】链接/口令转链，包含字段：material_url, is_target_coupon, coupon_id'
			),
		special_id: z
			.string()
			.optional()
			.describe('【非必填】会员运营id，示例值：123'),
		uvid: z
			.string()
			.optional()
			.describe('【非必填】加密用户标识，示例值：adjso7389rbnasd'),
		qmtid: z
			.string()
			.optional()
			.describe('【非必填】启明系统任务ID，示例值：123'),
	},

	/**
	 * 淘宝链接解析参数
	 * 基于淘宝开放平台API文档：taobao.tbk.sc.general.link.parse
	 */
	linkParser: {
		relation_id: z
			.string()
			.optional()
			.describe(
				'【非必填】渠道管理ID（如是主站选品推广场景，必须入参该字段，且bizSceneId字段需入参2-消费者比价场景，否则二次转链失败），示例值：123456'
			),
		material_dto: z
			.array(
				z.object({
					material_url: z
						.string()
						.optional()
						.describe(
							'【非必填】物料链接，可以为url或淘口令，示例值：s.click.taobao.com'
						),
				})
			)
			.optional()
			.describe('【非必填】链接/口令转链'),
		fields: z
			.string()
			.optional()
			.describe(
				'【非必填】有origin_pid使用场景需入参，示例值：origin_pid'
			),
	},

	/**
	 * 淘宝客-公用-淘宝客商品详情查询(简版)参数
	 * 基于淘宝开放平台API文档：taobao.tbk.item.info.get
	 */
	itemInfo: {
		num_iids: z
			.string()
			.describe('【必填】商品ID串，用,分割，最大40个，示例值：123,456'),
		platform: z
			.number()
			.optional()
			.describe(
				'【非必填】链接形式：1：PC，2：无线，默认：１，示例值：1'
			),
		ip: z
			.string()
			.optional()
			.describe(
				'【非必填】ip地址，影响邮费获取，如果不传或者传入不准确，邮费无法精准提供，示例值：***********'
			),
		biz_scene_id: z
			.string()
			.optional()
			.describe(
				'【非必填】1-动态ID转链场景，2-消费者比价场景，3-商品库导购场景（不填默认为1），示例值：1'
			),
		promotion_type: z
			.string()
			.optional()
			.describe(
				'【非必填】1-自购省，2-推广赚（代理模式专属ID，代理模式必填，非代理模式不用填写该字段），示例值：2'
			),
		relation_id: z
			.string()
			.optional()
			.describe('【非必填】渠道关系ID，示例值：1'),
		manage_item_pub_id: z
			.number()
			.optional()
			.describe(
				'【非必填】商品库服务账户(场景id3权限对应的memberid），示例值：1'
			),
	},

	/**
	 * 淘宝客-服务商-官方活动转链参数
	 * 基于淘宝开放平台API文档：taobao.tbk.sc.activity.info.get
	 */
	activityConverter: {
		activity_material_id: z
			.string()
			.describe(
				'【必填】官方活动会场ID，从淘宝客后台“我要推广-活动推广”中获取，示例值：123'
			),
		relation_id: z
			.number()
			.optional()
			.describe('【非必填】渠道关系id，示例值：123'),
		union_id: z
			.string()
			.optional()
			.describe(
				'【非必填】自定义输入串，英文和数字组成，长度不能大于12个字符，区分不同的推广渠道，示例值：demo'
			),
	},

	/**
	 * 淘宝客-服务商-所有订单查询参数
	 * 基于淘宝开放平台API文档：taobao.tbk.sc.order.details.get
	 */
	orderDetails: {
		query_type: z
			.number()
			.optional()
			.describe(
				'【非必填】查询时间类型，1：按照订单淘客创建时间查询，2:按照订单淘客付款时间查询，3:按照订单淘客结算时间查询，4:按照订单更新时间；，示例值：1'
			),
		position_index: z
			.string()
			.optional()
			.describe(
				'【非必填】位点，除第一页之外，都需要传递；前端原样返回。，示例值：2222_334666'
			),
		page_size: z
			.number()
			.optional()
			.describe('【非必填】页大小，默认20，1~100，示例值：20'),
		member_type: z
			.number()
			.optional()
			.describe(
				'【非必填】推广者角色类型,2:二方，3:三方，不传，表示所有角色，示例值：2'
			),
		tk_status: z
			.number()
			.optional()
			.describe(
				'【非必填】淘客订单状态，12-付款，13-关闭，14-确认收货，3-结算成功;不传，表示所有状态，示例值：12'
			),
		end_time: z
			.string()
			.describe(
				'【必填】订单查询结束时间，订单开始时间至订单结束时间，中间时间段日常要求不超过3个小时，但如618、双11、年货节等大促期间预估时间段不可超过20分钟，超过会提示错误，调用时请务必注意时间段的选择，以保证亲能正常调用！，示例值：2019-04-23 12:28:22'
			),
		start_time: z
			.string()
			.describe('【必填】订单查询开始时间，示例值：2019-04-05 12:18:22'),
		jump_type: z
			.number()
			.optional()
			.describe(
				'【非必填】跳转类型，当向前或者向后翻页必须提供,-1: 向前翻页,1：向后翻页，示例值：1'
			),
		page_no: z
			.number()
			.optional()
			.describe('【非必填】第几页，默认1，1~100，示例值：1'),
		order_scene: z
			.number()
			.optional()
			.describe(
				'【非必填】筛选订单类型，1:所有订单，2:渠道订单，3:会员运营订单，默认为1，示例值：1'
			),
		member_group_id: z
			.number()
			.optional()
			.describe('【非必填】member组ID，示例值：100'),
	},

	/**
	 * 淘宝客-公用-长链转短链参数
	 * 基于淘宝开放平台API文档：taobao.tbk.spread.get
	 */
	spreadRequests: {
		requests: z
			.array(
				z.object({
					url: z
						.string()
						.describe(
							'【必填】原始url, 只支持uland.taobao.com，s.click.taobao.com， ai.taobao.com，temai.taobao.com的域名转换，否则判错，示例值：http://temai.taobao.com'
						),
				})
			)
			.describe('【必填】请求列表，内部包含多个url'),
	},

	/**
	 * 淘宝客-服务商-创建推广者位参数
	 * 基于淘宝开放平台API文档：taobao.tbk.sc.adzone.create
	 */
	promotionZone: {
		adzone_name: z
			.string()
			.max(64)
			.describe('【必填】广告位名称，最大长度64字符，示例值：广告位'),
	},

	/**
	 * 淘宝客-公用-淘口令生成参数
	 * 基于淘宝开放平台API文档：taobao.tbk.tpwd.create
	 */
	tpwdCreate: {
		url: z
			.string()
			.describe(
				'【必填】联盟官方渠道获取的淘客推广链接，请注意，不要随意篡改官方生成的链接，否则可能无法生成淘口令，示例值：https://s.click.taobao.com/YI3Uopu'
			),
		text: z
			.string()
			.optional()
			.describe(
				'【非必填】兼容旧版本api参数，无实际作用，示例值：nomeaningValue'
			),
		logo: z
			.string()
			.optional()
			.describe(
				'【非必填】兼容旧版本api参数，无实际作用，示例值：nomeaningValue'
			),
		ext: z
			.string()
			.optional()
			.describe(
				'【非必填】兼容旧版本api参数，无实际作用，示例值：nomeaningValue'
			),
		user_id: z
			.string()
			.optional()
			.describe(
				'【非必填】兼容旧版本api参数，无实际作用，示例值：nomeaningValue'
			),
	},

	/**
	 * 淘宝客-服务商-处罚订单查询参数
	 * 基于淘宝开放平台API文档：taobao.tbk.sc.punish.order.get
	 */
	punishOrder: {
		af_order_option: z
			.object({
				relation_id: z
					.number()
					.optional()
					.describe('【非必填】渠道关系id，示例值：2222'),
				tb_trade_id: z
					.number()
					.optional()
					.describe('【非必填】子订单号，示例值：258897956183171983'),
				page_size: z
					.number()
					.optional()
					.describe('【非必填】pagesize，示例值：1'),
				page_no: z
					.number()
					.optional()
					.describe('【非必填】pageNo，示例值：10'),
				span: z
					.number()
					.optional()
					.describe(
						'【非必填】查询时间跨度，不超过30天，单位是天，示例值：10'
					),
				start_time: z
					.string()
					.optional()
					.describe(
						'【非必填】查询开始时间，以taoke订单创建时间开始，示例值：2018-11-11 00:01:01'
					),
				adzone_id: z
					.number()
					.optional()
					.describe('【非必填】pid中的第三段，adzoneId，示例值：123'),
				site_id: z
					.number()
					.optional()
					.describe('【非必填】pid中的第二段，siteId，示例值：123'),
			})
			.describe('【必填】入参的对象'),
	},

	/**
	 * 淘宝客-服务商-权益物料精选参数
	 * 基于淘宝开放平台API文档：taobao.tbk.sc.optimus.promotion
	 */
	optimusPromotion: {
		page_size: z
			.number()
			.optional()
			.describe('【非必填】页大小，每次请求限到10以内，示例值：10'),
		page_num: z
			.number()
			.optional()
			.describe('【非必填】第几页，默认：1，示例值：1'),
		promotion_id: z
			.number()
			.describe(
				'【必填】官方提供的权益物料Id。有价券-37104、大额店铺券-37116、天猫店铺券-62191、券券补-61809 更多权益物料id敬请期待！，示例值：123'
			),
	},

	/**
	 * 淘宝客-服务商-查询红包发放个数参数
	 * 基于淘宝开放平台API文档：taobao.tbk.sc.vegas.send.report
	 */
	vegasSendReport: {
		biz_date: z.string().describe('【必填】统计日期，示例值：20191201'),
		relation_id: z
			.number()
			.optional()
			.describe('【非必填】渠道关系id，示例值：111'),
		activity_id: z
			.number()
			.optional()
			.describe('【非必填】已下线，后续不需要填写，示例值：1'),
		page_no: z.number().optional().describe('【非必填】页码，示例值：1'),
		page_size: z
			.number()
			.optional()
			.describe('【非必填】每页大小，示例值：10'),
		rpt_dim: z
			.string()
			.optional()
			.describe(
				'【非必填】查询维度，不填写默认是pid维度，示例值：pid/relation'
			),
		activity_category: z
			.number()
			.optional()
			.describe(
				'【非必填】查询红包类型，1-超级红包，2-福利购，3-签到红包，4-福利直降，5-幸运赢免单，不传时默认查询超级红包数据，示例值：1'
			),
	},

	// 可以继续添加其他API的参数定义...
};
