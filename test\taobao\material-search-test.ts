import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 物料搜索测试客户端
 * 用于测试淘宝客物料搜索功能
 */
export class MaterialSearchTest extends BaseTestClient {
	private optionalParams: Record<string, any>;

	/**
	 * 构造函数
	 * @param optionalParams 可选参数
	 */
	constructor(optionalParams: Record<string, any> = {}) {
		super();
		this.optionalParams = optionalParams;
	}

	/**
	 * 执行物料搜索测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			// 构建参数对象
			const params = {
				...this.optionalParams,
			};

			console.log(
				'发送物料搜索请求参数:',
				JSON.stringify(params, null, 2)
			);

			// 调用工具并获取结果
			const result = await this.callTool('taobao.searchMaterial', params);

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('物料搜索测试成功!');
				try {
					const data = JSON.parse(result.content[0].text);
					console.log(
						`找到商品数量: ${
							data?.result_list?.map_data?.length || 0
						}`
					);
					console.log('搜索结果示例:');
					if (data?.result_list?.map_data?.length > 0) {
						const sample = data.result_list.map_data[0];
						console.log(JSON.stringify(sample, null, 2));
					}
				} catch (e) {
					console.log(`原始结果: ${result.content[0].text}`);
				}
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 如果直接运行此文件,则执行测试
if (currentFilePath === entryPointPath) {
	// 可选参数示例
	const optionalParams = {
		q: '男装 工装裤', // 搜索关键词
		page_no: 1, // 第1页
		page_size: 1,
	};

	console.log('启动物料搜索测试');
	const test = new MaterialSearchTest(optionalParams);
	test.runTest().catch(console.error);
}
