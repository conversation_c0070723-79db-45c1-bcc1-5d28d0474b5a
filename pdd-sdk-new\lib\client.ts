import axios, { AxiosInstance } from 'axios';
import crypto from 'crypto';

/**
 * 拼多多开放平台客户端配置
 */
export interface PddClientConfig {
  /**
   * 应用的 client_id
   */
  clientId: string;
  
  /**
   * 应用的密钥
   */
  clientSecret: string;
  
  /**
   * 访问令牌
   */
  accessToken?: string;

  /**
   * 请求地址
   */
  serverUrl?: string;
  
  /**
   * 是否开启调试模式
   */
  debug?: boolean;
}

interface CommonParams {
  client_id: string;
  timestamp: string;
  data_type: string;
  version: string;
  access_token?: string;
  sign?: string;
  [key: string]: any;
}

/**
 * 拼多多开放平台客户端
 */
export class PddClient {
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly accessToken?: string;
  private readonly serverUrl: string;
  private readonly debug: boolean;
  private readonly axios: AxiosInstance;

  /**
   * 构造函数
   * @param config 客户端配置
   */
  constructor(config: PddClientConfig) {
    this.clientId = config.clientId;
    this.clientSecret = config.clientSecret;
    this.accessToken = config.accessToken;
    this.serverUrl = config.serverUrl || 'https://gw-api.pinduoduo.com/api/router';
    this.debug = config.debug || false;

    this.axios = axios.create({
      baseURL: this.serverUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });
  }

  /**
   * 生成签名
   * @param params 请求参数
   * @returns 签名字符串
   */
  private generateSign(params: Record<string, any>): string {
    const sortedKeys = Object.keys(params).sort();
    let signStr = this.clientSecret;
    
    for (const key of sortedKeys) {
      const value = params[key];
      if (key !== 'sign' && value !== undefined && value !== null && value !== '') {
        signStr += key + value;
      }
    }
    signStr += this.clientSecret;

    return crypto.createHash('md5')
      .update(signStr)
      .digest('hex')
      .toUpperCase();
  }

  /**
   * 执行接口请求
   * @param params 业务参数
   * @returns 接口响应
   */
  public async execute<T = any>(params: Record<string, any>): Promise<T> {
    const commonParams: CommonParams = {
      client_id: this.clientId,
      timestamp: Math.floor(Date.now() / 1000).toString(),
      data_type: 'JSON',
      version: 'V1'
    };

    if (this.accessToken) {
      commonParams.access_token = this.accessToken;
    }

    const requestParams = {
      ...commonParams,
      ...params
    };

    requestParams.sign = this.generateSign(requestParams);

    try {
      const response = await this.axios.post('', new URLSearchParams(requestParams));

      if (response.data.error_response) {
        throw new Error(JSON.stringify(response.data.error_response));
      }

      return response.data as T;
    } catch (error) {
      throw error;
    }
  }
} 