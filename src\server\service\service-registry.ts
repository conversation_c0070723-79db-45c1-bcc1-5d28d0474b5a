import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../utils/logger.js';
import { BaseService } from './base-service.js';
import { TaobaoService } from './taobao/taobao-service.js';
import { JdService } from './jd/jd-service.js';
import { PddService } from './pdd/pdd-service.js';

/**
 * 服务注册表
 * 管理并统一注册所有平台服务
 */
export class ServiceRegistry {
	/**
	 * 已注册的服务列表
	 */
	private services: BaseService[] = [];

	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 */
	constructor(private mcpServer: McpServer) {
		this.initServices();
	}

	/**
	 * 初始化所有服务
	 */
	private initServices(): void {
		// 注册淘宝服务
		this.services.push(new TaobaoService(this.mcpServer));

		// 注册京东服务
		this.services.push(new JdService(this.mcpServer));

		// 注册拼多多服务
		this.services.push(new PddService(this.mcpServer));

		log(`已初始化 ${this.services.length} 个平台服务`);
	}

	/**
	 * 注册所有服务
	 */
	public registerAllServices(): void {
		log('开始注册所有平台服务...');

		try {
			for (const service of this.services) {
				service.registerTools();
			}

			log('所有平台服务注册完成');
		} catch (error) {
			logError(
				`注册服务失败: ${
					error instanceof Error ? error.message : String(error)
				}`
			);
			throw error;
		}
	}
}
