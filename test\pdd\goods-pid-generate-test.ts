import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 推广位生成测试客户端
 * 用于测试拼多多推广位生成功能
 */
export class GoodsPidGenerateTest extends BaseTestClient {
  private number: number;

  /**
   * 构造函数
   * @param number 要生成的推广位数量
   */
  constructor(number: number) {
    super();
    this.number = number;
  }

  /**
   * 执行推广位生成测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.goods.pid.generate", {
        number: this.number
      });

      // 验证响应
      if (result.content?.[0]?.text) {
        console.log('推广位生成测试成功!');
        console.log(`生成结果: ${result.content[0].text}`);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const testNumber = 1;
  const test = new GoodsPidGenerateTest(testNumber);
  test.runTest().catch(console.error);
} 