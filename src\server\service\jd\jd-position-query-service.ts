import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { log, logError } from "../../../utils/logger.js";
import { JdApiManager } from "../../jd/api-manager.js";
import { JdUnionType } from "../../jd/types.js";

/**
 * 京东推广位查询服务类
 * 处理京东联盟推广位查询功能
 */
export class JdPositionQueryService {
  /**
   * 构造函数
   * @param mcpServer MCP服务器实例
   * @param apiManager 京东API管理器实例
   * @param getFullToolName 获取完整工具名称的函数
   */
  constructor(
    private mcpServer: McpServer,
    private apiManager: JdApiManager,
    private getFullToolName: (name: string) => string
  ) {}

  /**
   * 注册推广位查询工具
   */
  public registerTool(): void {
    const toolName = this.getFullToolName("positionQuery");
    
    this.mcpServer.tool(
      toolName,
      "查询京东推广位信息[京东联盟-推广位查询]",
      {
        unionType: z.nativeEnum(JdUnionType).describe("【必填】联盟推广位类型，3：私域推广位，4：联盟后台推广位"),
        pageIndex: z.number().int().min(1).optional().describe("【非必填】页码，默认1"),
        pageSize: z.number().int().optional().describe("【非必填】每页条数，默认20，上限100")
      },
      async ({ unionType, pageIndex, pageSize }) => {
        try {
          log(`执行工具 ${toolName}: ${JSON.stringify({ unionType, pageIndex, pageSize })}`);
          
          // 获取环境变量
          const unionId = process.env.JD_UNION_ID;
          const key = process.env.JD_KEY;
          
          // 检查必要的环境变量
          if (!unionId) {
            throw new Error('环境变量JD_UNION_ID未设置');
          }
          
          if (!key) {
            throw new Error('环境变量JD_KEY未设置');
          }
          
          log(`使用环境变量: JD_UNION_ID=${unionId}, JD_KEY=${key ? '已设置' : '未设置'}`);
          
          try {
            // 执行推广位查询，使用正确的参数格式
            const result = await this.apiManager.positionQuery.query({
              unionId: Number(unionId),
              key,
              unionType,
              pageIndex: pageIndex || 1,
              pageSize: pageSize || 20
            });
            
            log(`工具 ${toolName} 执行结果: ${JSON.stringify(result, null, 2)}`);
            
            // 结果已经是解析后的JSON对象，直接返回
            return {
              content: [{
                type: "text",
                text: JSON.stringify(result, null, 2)
              }]
            };
          } catch (apiError) {
            logError(`推广位查询API调用失败: ${apiError instanceof Error ? apiError.message : String(apiError)}`);
            throw apiError;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          logError(`工具 ${toolName} 执行出错: ${errorMessage}`);
          return {
            content: [{
              type: "text",
              text: `工具执行失败: ${errorMessage}`
            }],
            isError: true
          };
        }
      }
    );
  }
}
