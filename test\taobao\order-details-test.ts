import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 订单查询测试客户端
 * 用于测试淘宝客订单查询功能
 */
export class OrderDetailsTest extends BaseTestClient {
	private queryParams: {
		start_time: string;
		end_time: string;
		query_type?: number;
		page_no?: number;
		page_size?: number;
		tk_status?: number;
		member_type?: number;
		jump_type?: number;
		position_index?: string;
		order_scene?: number;
	};

	/**
	 * 构造函数
	 * @param queryParams 订单查询参数
	 */
	constructor(queryParams: {
		start_time: string;
		end_time: string;
		query_type?: number;
		page_no?: number;
		page_size?: number;
		tk_status?: number;
		member_type?: number;
		jump_type?: number;
		position_index?: string;
		order_scene?: number;
	}) {
		super();
		this.queryParams = queryParams;
	}

	/**
	 * 执行订单查询测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			console.log(
				'发送订单查询请求参数:',
				JSON.stringify(this.queryParams, null, 2)
			);

			// 调用工具并获取结果
			const result = await this.callTool(
				'taobao.getOrderDetails',
				this.queryParams
			);

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('订单查询测试成功!');
				console.log(`查询结果: ${result.content[0].text}`);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 如果直接运行此文件,则执行测试
if (currentFilePath === entryPointPath) {
	const queryParams = {
		start_time: '2025-04-01 13:21:00',
		end_time: '2025-04-01 15:21:59',
		query_type: 1,
		page_no: 1,
		page_size: 20,
		order_scene: 1,
	};

	console.log('启动订单查询测试');
	const test = new OrderDetailsTest(queryParams);
	test.runTest().catch(console.error);
}
