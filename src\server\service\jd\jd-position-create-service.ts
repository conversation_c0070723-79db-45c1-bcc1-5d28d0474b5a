import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { log, logError } from "../../../utils/logger.js";
import { JdApiManager } from "../../jd/api-manager.js";
import { JdUnionType, JdSiteType } from "../../jd/types.js";

/**
 * 京东推广位创建服务类
 * 处理京东联盟推广位创建功能
 */
export class JdPositionCreateService {
  /**
   * 构造函数
   * @param mcpServer MCP服务器实例
   * @param apiManager 京东API管理器实例
   * @param getFullToolName 获取完整工具名称的函数
   */
  constructor(
    private mcpServer: McpServer,
    private apiManager: JdApiManager,
    private getFullToolName: (name: string) => string
  ) {}

  /**
   * 注册推广位创建工具
   */
  public registerTool(): void {
    const toolName = this.getFullToolName("positionCreate");

    this.mcpServer.tool(
      toolName,
      "创建京东推广位[京东联盟-推广位创建]",
      {
        unionType: z.nativeEnum(JdUnionType).describe("【必填】联盟推广位类型，3：私域推广位，4：联盟后台推广位"),
        type: z.nativeEnum(JdSiteType).describe("【必填】站点类型，3：无线站，4：PC站"),
        spaceNameList: z.array(z.string()).min(1).describe("【必填】推广位名称列表，最多创建50个"),
        siteId: z.number().int().min(0).optional().describe("【非必填】站点ID，无ID时使用环境变量JD_SITE_ID，未设置则为0")
      },
      async ({ unionType, type, spaceNameList, siteId }) => {
        try {
          log(`执行工具 ${toolName}: ${JSON.stringify({ unionType, type, spaceNameList, siteId })}`);

          // 获取环境变量
          const unionId = process.env.JD_UNION_ID;
          const key = process.env.JD_KEY;

          // 检查必要的环境变量
          if (!unionId) {
            logError('环境变量JD_UNION_ID未设置');
            process.exit(1);
          }

          if (!key) {
            logError('环境变量JD_KEY未设置');
            process.exit(1);
          }

          log(`使用环境变量: JD_UNION_ID=${unionId}, JD_KEY=${key ? '已设置' : '未设置'}`);

          try {
            // 执行推广位创建
            const result = await this.apiManager.positionCreate.create({
              unionId: Number(unionId),
              key,
              unionType,
              type,
              spaceNameList,
              siteId: siteId !== undefined ? siteId : undefined
            });

            log(`工具 ${toolName} 执行结果: ${JSON.stringify(result, null, 2)}`);

            // 结果已经是解析后的JSON对象，直接返回
            return {
              content: [{
                type: "text",
                text: JSON.stringify(result, null, 2)
              }]
            };
          } catch (apiError) {
            logError(`推广位创建API调用失败: ${apiError instanceof Error ? apiError.message : String(apiError)}`);
            throw apiError;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          logError(`工具 ${toolName} 执行出错: ${errorMessage}`);
          return {
            content: [{
              type: "text",
              text: `工具执行失败: ${errorMessage}`
            }],
            isError: true
          };
        }
      }
    );
  }
}
