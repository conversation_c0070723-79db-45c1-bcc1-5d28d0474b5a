import { log, logDebug } from '../../utils/logger.js';
import { PddClient } from '@liuliang520500/pdd-sdk-new';
import { getEnvVar } from '../../utils/env.js';
import { filterEmptyParams } from '../../utils/params-helper.js';

/**
 * 拼多多 API 基类
 */
export abstract class BaseApi {
	protected client: PddClient;

	constructor() {
		// 从环境变量获取配置
		const clientId = getEnvVar('PDD_CLIENT_ID');
		const clientSecret = getEnvVar('PDD_CLIENT_SECRET');
		const accessToken = getEnvVar('PDD_SESSION_TOKEN');

		// 初始化客户端
		this.client = new PddClient({
			clientId,
			clientSecret,
			accessToken,
		});
	}

	/**
	 * 调用 API
	 * @param type API 类型
	 * @param params 业务参数
	 * @returns API 响应结果
	 */
	protected async callApi<T = any>(
		type: string,
		params: Record<string, any> = {}
	): Promise<T> {
		try {
			// 过滤空值参数
			const filteredParams = filterEmptyParams(params);

			// 处理数组和对象参数，将它们转换为JSON字符串
			const processedParams: Record<string, any> = {};
			for (const [key, value] of Object.entries(filteredParams)) {
				if (Array.isArray(value)) {
					// 如果是数组，转换为JSON字符串
					processedParams[key] = JSON.stringify(value);
				} else if (typeof value === 'object' && value !== null) {
					// 如果是对象，转换为JSON字符串
					processedParams[key] = JSON.stringify(value);
				} else {
					// 其他类型直接保留
					processedParams[key] = value;
				}
			}

			logDebug(
				`调用拼多多API ${type}, 参数: ${JSON.stringify(
					processedParams
				)}`
			);

			const response = await this.client.execute<T>({
				type,
				...processedParams,
			});

			logDebug(`API响应: ${JSON.stringify(response)}`);
			return this.handleApiResponse<T>(response);
		} catch (error) {
			log(`拼多多API调用失败: ${error}`);
			throw error;
		}
	}

	/**
	 * 处理API响应
	 * @param response API响应
	 * @returns 处理后的响应
	 */
	protected handleApiResponse<T>(response: any): T {
		// 检查是否有错误响应
		if (response.error_response) {
			const error = new Error(
				`拼多多API错误: ${
					response.error_response.error_msg || 'Unknown error'
				}`
			);
			(error as any).response = response;
			throw error;
		}

		// 返回响应数据
		return response;
	}
}
