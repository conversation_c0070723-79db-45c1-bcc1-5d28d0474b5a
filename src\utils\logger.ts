import winston from 'winston';
import path from 'path';
import fs from 'fs';
import os from 'os';

/**
 * 日志类型枚举
 */
export enum LogLevel {
	INFO = 'INFO',
	ERROR = 'ERROR',
	DEBUG = 'DEBUG',
	WARN = 'WARN',
}

/**
 * 日志配置接口
 */
export interface LoggerConfig {
	/** 日志目录 */
	logDir: string;
	/** 是否启用控制台输出 */
	enableConsole: boolean;
	/** 最低日志级别 */
	minLevel: LogLevel;
}

/**
 * 获取启动参数中的日志目录
 * 如果没有指定，则返回用户的临时文件夹中的 mcp-logs 目录
 */
function getLogDirFromArgs(): string {
	const args = process.argv.slice(2);
	// 如果有参数且不是以'-'开头，则认为是日志目录
	const logDirArg = args.find(
		(arg) => !arg.startsWith('-') && !arg.startsWith('--')
	);

	if (logDirArg) {
		// 如果是绝对路径，直接返回
		if (path.isAbsolute(logDirArg)) {
			return logDirArg;
		}
		// 如果是相对路径，转换为相对于当前工作目录的绝对路径
		return path.resolve(process.cwd(), logDirArg);
	}

	// 如果没有指定日志目录，则使用用户的临时文件夹
	return path.join(os.tmpdir(), 'mcp-logs');
}

/**
 * 默认日志配置
 */
const DEFAULT_CONFIG: LoggerConfig = {
	logDir: getLogDirFromArgs(), // 如果没有指定日志目录，则不记录日志
	enableConsole: true,
	minLevel: LogLevel.INFO,
};

// 当前日志配置
let currentConfig: LoggerConfig = { ...DEFAULT_CONFIG };

// 创建日志目录
const logDir = currentConfig.logDir;

// 确保日志目录存在
if (logDir) {
	if (!fs.existsSync(logDir)) {
		fs.mkdirSync(logDir, { recursive: true });
	}
}

// 获取当前时间戳，精确到分钟
const getLogFileName = () => {
	const now = new Date();
	return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(
		2,
		'0'
	)}-${String(now.getDate()).padStart(2, '0')}_${String(
		now.getHours()
	).padStart(2, '0')}-${String(now.getMinutes()).padStart(2, '0')}.log`;
};

// 确保日志目录存在
const ensureLogDir = () => {
	const absoluteLogDir = path.resolve(currentConfig.logDir);
	if (!fs.existsSync(absoluteLogDir)) {
		fs.mkdirSync(absoluteLogDir, { recursive: true });
	}
	return absoluteLogDir;
};

// 创建日志记录器
const createLogger = () => {
	const transports: winston.transport[] = [];

	// 添加文件传输器
	const absoluteLogDir = ensureLogDir();
	const logFile = path.join(absoluteLogDir, getLogFileName());

	transports.push(
		new winston.transports.File({
			filename: logFile,
			format: winston.format.combine(
				winston.format.timestamp(),
				winston.format.printf(({ timestamp, level, message }) => {
					return `[${timestamp}] ${level.toUpperCase()}: ${message}`;
				})
			),
		})
	);

	// 添加控制台传输器
	if (currentConfig.enableConsole) {
		transports.push(
			new winston.transports.Console({
				format: winston.format.combine(
					winston.format.timestamp(),
					winston.format.printf(({ timestamp, level, message }) => {
						return `[${timestamp}] ${level.toUpperCase()}: ${message}`;
					})
				),
			})
		);
	}

	return winston.createLogger({
		level: 'debug',
		transports,
	});
};

// 创建全局日志记录器
const globalLogger = createLogger();

/**
 * 初始化日志系统
 * @param config 日志配置
 */
export function initLogger(config?: Partial<LoggerConfig>): void {
	// 合并配置
	currentConfig = { ...DEFAULT_CONFIG, ...config };

	// 输出初始化日志
	globalLogger.info('============================================');
	globalLogger.info('淘宝链接转换 MCP 服务日志系统初始化');
	globalLogger.info(`运行环境: Node.js ${process.version}`);
	globalLogger.info(`日志目录: ${path.resolve(currentConfig.logDir)}`);
	globalLogger.info(`当前日志文件: ${getLogFileName()}`);

	globalLogger.info('============================================');

	// 设置全局异常处理
	process.on('uncaughtException', (err) => {
		globalLogger.error(`未捕获的异常: ${err.message}`);
		globalLogger.error(`错误堆栈: ${err.stack}`);
	});

	process.on('unhandledRejection', (reason) => {
		globalLogger.error(`未处理的Promise拒绝: ${reason}`);
	});
}

// 导出日志方法
export const log = (message: string) => globalLogger.info(message);
export const logError = (message: string) => globalLogger.error(message);
export const logDebug = (message: string) => globalLogger.debug(message);
export const logWarn = (message: string) => globalLogger.warn(message);
export const closeLogger = () => {}; // 添加缺失的closeLogger方法
