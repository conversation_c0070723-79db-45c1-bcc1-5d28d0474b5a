import { BaseApi } from './base-api.js';
import { getRequiredEnvVar } from '../../utils/env.js';
import { log } from '../../utils/logger.js';
import { CmsPromUrlGenerateParams } from './types.js';

/**
 * 生成商城推广链接 API
 */
export class CmsPromUrlGenerate extends BaseApi {
	/**
	 * 生成商城推广链接
	 * @param params 生成参数
	 * @returns 生成结果
	 */
	async generate(params: CmsPromUrlGenerateParams): Promise<any> {
		try {
			// 从环境变量获取推广位ID
			const pid = getRequiredEnvVar('PDD_PID');
			log(`使用环境变量中的PID: ${pid}`);

			// 调用API
			return this.callApi('pdd.ddk.oauth.cms.prom.url.generate', {
				...params,
				p_id_list: [pid],
			});
		} catch (error) {
			log(`生成商城推广链接失败: ${error}`);
			throw error;
		}
	}
}
