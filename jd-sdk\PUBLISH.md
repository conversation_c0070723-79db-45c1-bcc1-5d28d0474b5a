# 发布指南

本文档将指导您如何将 jd-sdk 发布到 NPM。

## 准备工作

在发布之前，请确保：

1. 您已经拥有一个 NPM 账号
2. 您的 `package.json` 文件中的信息是正确的，特别是：
   - `name`：包名称
   - `version`：包版本
   - `description`：包描述
   - `keywords`：关键词
   - `author`：作者信息
   - `license`：许可证
   - `repository`：代码仓库信息

## 发布步骤

### 1. 登录 NPM

```bash
npm login
```

登录过程中需要输入用户名、密码和邮箱。如果配置了双因素认证，还需要输入认证码。

### 2. 测试打包

在发布前，最好先测试打包一下，检查包含了哪些文件：

```bash
npm pack
```

这将在当前目录下创建一个 `.tgz` 文件，但不会真正发布到 NPM。

检查生成的 `.tgz` 文件内容是否符合预期：
- 确认包含所有需要的源代码文件
- 确认没有包含不必要的文件（如 `node_modules`、`.env` 等）

### 3. 发布到 NPM

确认一切无误后，执行：

```bash
npm publish
```

如果要发布到特定的 NPM 注册表，可以使用：

```bash
npm publish --registry=https://registry.npmjs.org
```

### 4. 发布到私有注册表

如果您想发布到私有注册表（如公司内部的 NPM 镜像），可以使用：

```bash
npm publish --registry=https://your-private-registry.com
```

### 5. 版本管理

在后续更新时，需要先更新 `package.json` 中的版本号，推荐使用 npm 自带的版本管理命令：

```bash
# 增加补丁版本 1.0.0 -> 1.0.1
npm version patch

# 增加次要版本 1.0.0 -> 1.1.0
npm version minor

# 增加主要版本 1.0.0 -> 2.0.0
npm version major
```

然后再次发布：

```bash
npm publish
```

## 常见问题

### 1. 发布时提示 403 错误

可能原因：
- 包名已被占用
- 没有登录或登录过期
- 没有发布权限
- 私有包但没有付费账户

解决方案：
- 修改包名，确保唯一性
- 重新登录 `npm login`
- 确认账户权限
- 使用 `npm publish --access=public` 发布公共包

### 2. 发布时提示 409 错误

可能原因：
- 尝试发布的版本已经存在

解决方案：
- 更新 `package.json` 中的版本号
- 使用 `npm version` 命令自动更新版本号

### 3. 中国大陆用户提示网络问题

可能原因：
- 网络连接问题

解决方案：
- 使用淘宝镜像：`npm publish --registry=https://registry.npmmirror.com`
- 使用代理服务器
- 使用 VPN

## 发布后的检查

发布成功后，您可以检查包是否成功发布：

```bash
npm view jd-sdk
```

也可以在浏览器中访问：

```
https://www.npmjs.com/package/jd-sdk
```

## 使用说明

在其他项目中安装您发布的包：

```bash
npm install jd-sdk --save
``` 