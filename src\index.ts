#!/usr/bin/env node

import dotenv from 'dotenv';
import { log, initLogger, logError } from './utils/logger.js';
import { McpServerManager } from './server/server-manager.js';
import { loadEnvFromNetwork, checkRequiredEnvVars } from './JWT/env-loader.js';
import { initSecretConfig } from './JWT/secret-config.js';




// 初始化日志工具
initLogger();

// 加载本地环境变量
dotenv.config();

// 记录启动信息
log('====== MCP 服务启动 ======');


// 创建服务器管理器实例
const serverManager = new McpServerManager();

/**
 * 从网络加载环境变量
 */
async function loadEnvVarsFromNetwork(): Promise<boolean> {
	// 如果没有设置环境变量 URL，则跳过
	const envUrl = process.env.ENV_URL?.trim();
	const envSecret = process.env.ENV_SECRET?.trim();
	const aesKey = process.env.SERVER_SECRET?.trim();
	if (!envUrl) {
		logError('缺少必要的环境变量，ENV_URL');
		return false;
	}

	// log(`${envUrl}`)
	// log(`${envSecret}`)
	// log(`${aesKey}`)
	try {
		// 从网络加载环境变量
		const success = await loadEnvFromNetwork({
			url: envUrl,
			secret: envSecret,
			aesKey: aesKey,
			override: process.env.ENV_OVERRIDE === 'true',
		});

		return success;
	} catch (error) {
		logError(
			`从网络加载环境变量失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		return false;
	}
}

/**
 * 启动服务器
 */
async function startServer(): Promise<void> {
	log('====== 启动服务器 ======');
	log('正在加载环境变量...');

	// 先初始化 Secret 配置，获取解密参数
	// log('====== 初始化 Secret 配置 ======');
	try {
		// 获取并设置 Secret 配置
		await initSecretConfig();
		// log('授权校验成功成功');
	} catch (error) {
		logError(
			`Secret 配置初始化失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		process.exit(1);
	}

	// 然后从网络加载环境变量，使用初始化后的解密参数
	log('====== 从网络加载环境变量 ======');
	const result = await loadEnvVarsFromNetwork();
	if (!result) {
		logError('环境变量加载失败，将使用本地环境变量');
		// 注意：这里不直接抛出错误，而是继续使用本地环境变量
	}

	log('正在检查环境变量...');

	// 检查必要的环境变量 - 从 .env.example 中提取所有环境变量
	const requiredEnvVars = [
		// 淘宝客API配置
		'TAOBAO_SITE_ID',
		'TAOBAO_ADZONE_ID',
		'TAOBAO_SESSION',

		// 京东联盟API配置
		'JD_KEY',
		'JD_UNION_ID',
		'JD_SITE_ID',

		// 拼多多API配置
		'PDD_PID',
		'PDD_SESSION_TOKEN',

		// 服务器密钥
		'SERVER_SECRET',
	];

	// 使用环境变量加载器检查必要的环境变量
	if (!checkRequiredEnvVars(requiredEnvVars)) {
		logError('缺少必要的环境变量，无法启动服务器');
		process.exit(1);
	}

	log('环境变量检查通过');

	// 处理淘宝 PID，如果存在则提取出 site_id 和 adzone_id
	if (process.env.TAOBAO_PID) {
		log('检测到 TAOBAO_PID 环境变量，正在提取 SITE_ID 和 ADZONE_ID...');
		const pidParts = process.env.TAOBAO_PID.split('_');
		if (pidParts.length === 4) {
			// 从 PID 中提取 SITE_ID 和 ADZONE_ID
			// 格式为 mm_113455612_357550044_115969600318
			// 其中 357550044 是 SITE_ID，115969600318 是 ADZONE_ID
			process.env.TAOBAO_SITE_ID = pidParts[2];
			process.env.TAOBAO_ADZONE_ID = pidParts[3];
			log(
				`从 TAOBAO_PID 中提取到 TAOBAO_SITE_ID: ${process.env.TAOBAO_SITE_ID}`
			);
			log(
				`从 TAOBAO_PID 中提取到 TAOBAO_ADZONE_ID: ${process.env.TAOBAO_ADZONE_ID}`
			);
		} else {
			logError(
				`TAOBAO_PID 格式不正确: ${process.env.TAOBAO_PID}，应为 mm_xxx_xxx_xxx 格式`
			);
		}
	}

	// 处理京东 PID，如果存在则提取出 UNION_ID 和 SITE_ID
	if (process.env.JD_PID) {
		log('检测到 JD_PID 环境变量，正在提取 UNION_ID 和 SITE_ID...');
		const jdPidParts = process.env.JD_PID.split('_');
		if (jdPidParts.length === 3) {
			// 从 PID 中提取 UNION_ID 和 SITE_ID
			// 格式为 1000793546_4100048109_3002986287
			// 其中 1000793546 是 UNION_ID，4100048109 是 SITE_ID
			process.env.JD_UNION_ID = jdPidParts[0];
			process.env.JD_SITE_ID = jdPidParts[1];
			log(`从 JD_PID 中提取到 JD_UNION_ID: ${process.env.JD_UNION_ID}`);
			log(`从 JD_PID 中提取到 JD_SITE_ID: ${process.env.JD_SITE_ID}`);
		} else {
			logError(
				`JD_PID 格式不正确: ${process.env.JD_PID}，应为 xxx_xxx_xxx 格式`
			);
		}
	}

	try {
		// 启动服务器管理器
		await serverManager.start();
		log('服务器启动成功');
	} catch (error) {
		logError(
			`服务器启动失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		process.exit(1);
	}
}

/**
 * 处理进程退出信号
 */
async function handleExit() {
	log('收到退出信号，正在优雅关闭服务器...');
	try {
		await serverManager.stop();
		log('服务器已关闭');
		process.exit(0);
	} catch (error) {
		logError(
			`服务器关闭出错: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		process.exit(1);
	}
}

// 注册信号处理程序
process.on('SIGINT', handleExit);
process.on('SIGTERM', handleExit);

// 直接启动服务器
// startServer().catch((error) => {
// 	logError(
// 		`服务器启动失败: ${
// 			error instanceof Error ? error.message : String(error)
// 		}`
// 	);
// 	process.exit(1);
// });

export { startServer };
