import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { TaobaoApiManager } from '../../taobao/api-manager.js';
import { TaobaoParams } from './params.js';

/**
 * 淘宝处罚订单服务类
 * 处理淘宝客处罚订单查询功能
 */
export class TaobaoPunishOrderService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 淘宝API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: TaobaoApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册处罚订单查询工具
	 * https://open.taobao.com/api.htm?docId=41942&docType=2&scopeId=15738
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('getPunishOrder');

		this.mcpServer.tool(
			toolName,
			'处罚订单查询[淘宝客-服务商-处罚订单查询]',
			TaobaoParams.punishOrder,
			async ({ af_order_option }) => {
				try {
					log(
						`执行工具 ${toolName}: ${JSON.stringify({
							af_order_option,
						})}`
					);
					const result =
						await this.apiManager.punishOrder.getPunishOrders(
							af_order_option
						);
					log(`工具 ${toolName} 执行结果: ${JSON.stringify(result)}`);
					return {
						content: [
							{
								type: 'text',
								text: JSON.stringify(result),
							},
						],
					};
				} catch (error) {
					logError(
						`工具 ${toolName} 执行出错: ${
							error instanceof Error
								? error.message
								: String(error)
						}`
					);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${
									error instanceof Error
										? error.message
										: JSON.stringify(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
