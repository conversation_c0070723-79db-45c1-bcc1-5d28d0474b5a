import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { TaobaoApiManager } from '../../taobao/api-manager.js';
import { TaobaoParams } from './params.js';

/**
 * 淘宝客-服务商-查询红包发放个数服务
 */
export class TaobaoVegasSendReportService {
	private mcpServer: McpServer;
	private apiManager: TaobaoApiManager;
	private getFullToolName: (name: string) => string;

	constructor(
		mcpServer: McpServer,
		apiManager: TaobaoApiManager,
		getFullToolName: (name: string) => string
	) {
		this.mcpServer = mcpServer;
		this.apiManager = apiManager;
		this.getFullToolName = getFullToolName;
	}

	/**
	 * 注册工具
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('vegas-send-report');

		this.mcpServer.tool(
			toolName,
			'查询淘宝红包发放个数[淘宝客-服务商-查询红包发放个数]',
			TaobaoParams.vegasSendReport,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);
					const result =
						await this.apiManager.vegasSendReport.getVegasSendReport(
							params
						);
					log(`工具 ${toolName} 执行结果: ${JSON.stringify(result)}`);
					return {
						content: [
							{
								type: 'text',
								text: JSON.stringify(result),
							},
						],
					};
				} catch (error) {
					logError(
						`工具 ${toolName} 执行出错: ${
							error instanceof Error
								? error.message
								: String(error)
						}`
					);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${
									error instanceof Error
										? error.message
										: JSON.stringify(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
