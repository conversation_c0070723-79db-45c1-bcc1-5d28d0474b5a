/**
 * 推广位查询示例
 */
require('dotenv').config();
const { JdClient } = require('../index');

// 检查必要的环境变量
if (!process.env.JD_APP_KEY) {
  console.error('环境变量错误: 未设置JD_APP_KEY');
  process.exit(1);
}

if (!process.env.JD_APP_SECRET) {
  console.error('环境变量错误: 未设置JD_APP_SECRET');
  process.exit(1);
}

if (!process.env.JD_KEY) {
  console.error('环境变量错误: 未设置JD_KEY (这是访问API的必要参数)');
  process.exit(1);
}

if (!process.env.JD_UNION_ID) {
  console.error('环境变量错误: 未设置JD_UNION_ID');
  process.exit(1);
}

// 打印环境变量信息用于调试
console.log('环境变量信息:');
console.log('JD_APP_KEY:', process.env.JD_APP_KEY ? '已设置' : '未设置');
console.log('JD_APP_SECRET:', process.env.JD_APP_SECRET ? '已设置' : '未设置');
console.log('JD_KEY:', process.env.JD_KEY ? '已设置' : '未设置');
console.log('JD_UNION_ID:', process.env.JD_UNION_ID ? '已设置' : '未设置');
console.log('JD_SITE_ID:', process.env.JD_SITE_ID ? process.env.JD_SITE_ID : '未设置');

// 创建京东客户端实例
const client = new JdClient({
  appKey: process.env.JD_APP_KEY,
  secretKey: process.env.JD_APP_SECRET,
});

// 主函数
async function main() {
  try {
    console.log('开始查询京东推广位...');
    
    // 私域推广位查询
    const privatePositionResult = await client.execute('jd.union.open.position.query', {
      unionId: process.env.JD_UNION_ID,
      key: process.env.JD_KEY, // 显式传入key参数
      unionType: 3, // 私域推广位
      pageIndex: 1,
      pageSize: 10,
    });
    
    console.log('私域推广位查询结果:');
    logPositionResult(privatePositionResult);
    
    // 联盟后台推广位查询
    const unionPositionResult = await client.execute('jd.union.open.position.query', {
      unionId: process.env.JD_UNION_ID,
      key: process.env.JD_KEY, // 显式传入key参数
      unionType: 4, // 联盟后台推广位
      pageIndex: 1,
      pageSize: 10,
    });
    
    console.log('\n联盟后台推广位查询结果:');
    logPositionResult(unionPositionResult);
    
  } catch (error) {
    console.error('推广位查询出错:', error);
    console.error('请检查您的.env文件中的参数是否正确，特别是JD_KEY和JD_UNION_ID');
    console.error('API文档: https://union.jd.com/openplatform/api/10423');
  }
}

/**
 * 打印推广位查询结果
 * @param {Object} apiResponse API响应对象
 */
function logPositionResult(apiResponse) {
  try {
    // 解析返回结果
    if (apiResponse && apiResponse.jd_union_open_position_query_responce) {
      const response = apiResponse.jd_union_open_position_query_responce;
      
      if (response.queryResult) {
        const result = JSON.parse(response.queryResult);
        
        if (result.code === 200) {
          if (result.data && result.data.result && result.data.result.length > 0) {
            console.log(`共找到${result.data.result.length}个推广位，总数: ${result.data.total || '未知'}:`);
            result.data.result.forEach((position, index) => {
              console.log(`#${index + 1} ID: ${position.id}, 名称: ${position.spaceName}, 站点ID: ${position.siteId}, PID: ${position.pid || '无'}`);
            });
          } else {
            console.log('未找到推广位数据');
          }
        } else {
          console.error(`查询失败: ${result.message} (代码: ${result.code})`);
        }
      } else {
        console.error('返回结果格式错误: 缺少queryResult字段');
      }
    } else {
      console.error('API调用失败: 无响应或响应格式错误');
      if (apiResponse) {
        console.log('原始响应:', JSON.stringify(apiResponse, null, 2));
      }
    }
  } catch (error) {
    console.error('解析响应结果时出错:', error);
    console.error('原始响应:', JSON.stringify(apiResponse, null, 2));
  }
}

// 执行主函数
main();