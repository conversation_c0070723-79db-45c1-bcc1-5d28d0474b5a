import dotenv from "dotenv";
import { fileURLToPath } from "url";
import { dirname } from "path";
import { TopClient } from "../taobao-sdk/index.js";

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 加载环境变量
dotenv.config();

// 确保环境变量存在并转换为所需类型
function getRequiredEnvVar (name) {
  const value = process.env[name];
  if (!value)
  {
    throw new Error(`环境变量 ${name} 未设置`);
  }
  return value;
}

const TAOBAO_APP_KEY = getRequiredEnvVar('TAOBAO_APP_KEY');
const TAOBAO_APP_SECRET = getRequiredEnvVar('TAOBAO_APP_SECRET');
const TAOBAO_SESSION = getRequiredEnvVar('TAOBAO_SESSION');
const TAOBAO_ADZONE_ID = getRequiredEnvVar('TAOBAO_ADZONE_ID');

/**
 * 测试链接解析API
 * 根据接口文档：https://open.taobao.com/api.htm?docId=70535&docType=2&scopeId=31093
 */
async function testLinkParse () {
  console.log('测试: 链接解析 taobao.tbk.sc.general.link.parse');

  // 初始化淘宝客户端
  const client = new TopClient({
    appkey: TAOBAO_APP_KEY,
    appsecret: TAOBAO_APP_SECRET,
    REST_URL: 'http://gw.api.taobao.com/router/rest'
  });

  console.log('淘宝客户端已初始化，APP_KEY:', TAOBAO_APP_KEY.substring(0, 4) + '****');

  // 使用提供的淘宝推广链接
  const testUrl = "https://s.click.taobao.com/9Czb82s";

  // 根据API文档，准备必要参数
  const materialDto = JSON.stringify({
    material_url: testUrl
  });

  const apiParams = {
    material_dto: materialDto,  // 直接传递 JSON 字符串
    adzone_id: TAOBAO_ADZONE_ID,
    session: TAOBAO_SESSION,

    // 淘宝API通用参数
    format: 'json',
    v: '2.0',
    sign_method: 'md5',
    timestamp: new Date().toISOString().replace(/T/, ' ').replace(/\..+/, '')
  };

  console.log('API参数准备完成:', JSON.stringify(apiParams, null, 2));

  // 使用Promise包装回调方式调用
  return new Promise((resolve, reject) => {
    client.execute('taobao.tbk.sc.general.link.parse', apiParams, function (error, response) {
      if (error)
      {
        console.error('API调用失败:', error);
        reject(error);
        return;
      }

      console.log('API调用成功!');
      console.log('API返回响应:', JSON.stringify(response, null, 2));
      resolve(response);
    });
  });
}

console.log('=== 开始测试淘宝SDK ===');
console.log('测试时间:', new Date().toISOString());

// 运行测试
async function runTests () {
  try
  {
    // 测试链接解析API
    console.log('\n=== 测试链接解析API ===\n');
    await testLinkParse();

    console.log('\n=== 测试完成 ===');
  } catch (err)
  {
    console.error('\n=== 测试失败! ===');
    console.error(err);
  }
}

runTests(); 