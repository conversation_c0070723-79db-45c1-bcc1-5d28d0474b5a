/**
 * 联盟商品查询API
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.goods.query
 */
const { BaseApi } = require('../api');

/**
 * 联盟商品查询API请求类
 * 查询京东商品及优惠券信息，返回的结果可调用转链接口生成单品或二合一推广链接
 */
class UnionOpenGoodsQueryRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.goods.query';
  }

  /**
   * 执行商品查询请求
   * @param {Object} params 请求参数
   * @param {Object} params.goodsReqDTO 商品查询请求对象
   * @returns {Promise<Object>} 请求结果
   */
  async query(params = {}) {
    if (!params.goodsReqDTO) {
      throw new Error('goodsReqDTO不能为空');
    }

    // 验证并清理参数
    params.goodsReqDTO = this.cleanParams(params.goodsReqDTO);

    const apiParams = {
      goodsReqDTO: params.goodsReqDTO
    };

    return this.execute(apiParams);
  }

  /**
   * 清理参数，移除空值
   * @param {Object} params 原始参数
   * @returns {Object} 清理后的参数
   */
  cleanParams(params) {
    const cleanedParams = {};
    
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        cleanedParams[key] = params[key];
      }
    }
    
    return cleanedParams;
  }
}

module.exports = {
  UnionOpenGoodsQueryRequest
}; 