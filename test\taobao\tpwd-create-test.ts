import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 淘口令生成测试客户端
 * 用于测试淘宝客淘口令生成功能
 */
export class TpwdCreateTest extends BaseTestClient {
	private url: string;
	private text?: string;
	private logo?: string;
	private ext?: string;
	private userId?: string;

	/**
	 * 构造函数
	 * @param url 要转为淘口令的链接
	 * @param text 口令弹框内容
	 * @param logo 口令弹框logo
	 * @param ext 扩展字段
	 * @param userId 生成口令的淘宝用户ID
	 */
	constructor(
		url: string,
		text?: string,
		logo?: string,
		ext?: string,
		userId?: string
	) {
		super();
		this.url = url;
		this.text = text;
		this.logo = logo;
		this.ext = ext;
		this.userId = userId;
	}

	/**
	 * 执行淘口令生成测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			// 构建参数对象
			const params: any = { url: this.url };
			if (this.text) params.text = this.text;
			if (this.logo) params.logo = this.logo;
			if (this.ext) params.ext = this.ext;
			if (this.userId) params.user_id = this.userId;

			console.log(
				'发送淘口令生成请求参数:',
				JSON.stringify(params, null, 2)
			);

			// 调用工具并获取结果
			const result = await this.callTool('taobao.createTpwd', params);

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('淘口令生成测试成功!');
				console.log(`生成结果: ${result.content[0].text}`);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 如果直接运行此文件,则执行测试
if (currentFilePath === entryPointPath) {
	// 测试链接
	const testUrl = 'https://s.click.taobao.com/ycHWW1s';
	const testText = '测试淘口令';

	console.log('启动淘口令生成测试');
	const test = new TpwdCreateTest(testUrl, testText);
	test.runTest().catch(console.error);
}
