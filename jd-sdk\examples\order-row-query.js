/**
 * 京东联盟订单行查询示例
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.order.row.query
 */
require('dotenv').config();
const { JdClient } = require('../index');

// 检查环境变量
console.log('检查环境变量...');
['appkey', 'secretkey', 'unionId', 'key'].forEach(key => {
  console.log(`${key}: ${process.env[key] ? '已设置' : '未设置'}`);
});

console.log('\n环境变量实际值:');
console.log('appkey:', process.env.appkey);
console.log('unionId:', process.env.unionId);

// 创建京东客户端
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main() {
  try {
    // 构建请求参数
    const params = {
      orderReq: {
        pageIndex: 1,         // 页码
        pageSize: 20,         // 每页数量
        type: 1,              // 订单时间查询类型 1：下单时间，2：完成时间（购买用户确认收货时间），3：更新时间
        startTime: getDateStr(-7),  // 开始时间 yyyy-MM-dd HH:mm:ss
        endTime: getDateStr(0),     // 结束时间 yyyy-MM-dd HH:mm:ss
        fields: 'goodsInfo',  // 请求的字段列表，可选值包括：goodsInfo(商品信息)、siteInfo(网站信息)等
        key: process.env.key  // PID中的授权key，推客管理-我的工具-我的API
      }
    };

    console.log('\n尝试发送的参数:');
    console.log(JSON.stringify(params, null, 2));

    // 执行请求
    const result = await client.execute('UnionOpenOrderRowQueryRequest', params);
    
    console.log('\n完整API响应:');
    console.log(JSON.stringify(result, null, 2));

    // 解析业务响应
    const apiResponse = result.jd_union_open_order_row_query_responce;
    if (apiResponse && apiResponse.code === '0') {
      const queryResult = JSON.parse(apiResponse.queryResult);
      
      console.log('\n业务响应:');
      console.log('  状态码:', queryResult.code);
      console.log('  消息:', queryResult.message);
      
      if (queryResult.code === 200 && queryResult.data) {
        console.log('\n查询到的订单行:');
        console.log('  总数:', queryResult.totalCount || '未知');
        
        queryResult.data.forEach((orderRow, index) => {
          console.log(`\n订单行 ${index + 1}:`);
          console.log('  订单号:', orderRow.orderId);
          console.log('  商品ID:', orderRow.skuId);
          console.log('  商品名称:', orderRow.skuName);
          console.log('  订单状态:', getOrderStatusDesc(orderRow.validCode));
          console.log('  订单价格:', orderRow.price);
          console.log('  实际支付金额:', orderRow.actualFee);
          console.log('  佣金:', orderRow.estimateFee);
          console.log('  下单时间:', formatDate(orderRow.orderTime));
          
          if (orderRow.goodsInfo) {
            console.log('  商品信息:');
            console.log('    类目名称:', orderRow.goodsInfo.categoryName);
            console.log('    类目ID:', orderRow.goodsInfo.categoryId);
            if (orderRow.goodsInfo.imageUrl) {
              console.log('    图片URL:', orderRow.goodsInfo.imageUrl);
            }
          }
          
          if (orderRow.siteInfo) {
            console.log('  站点信息:');
            console.log('    站点ID:', orderRow.siteInfo.siteId);
            console.log('    站点名称:', orderRow.siteInfo.siteName);
          }
        });
      }
    } else {
      console.error('API调用失败:', apiResponse);
    }

  } catch (err) {
    console.error('发生错误:', err);
    console.log('请检查您的.env文件中是否设置了正确的参数。');
    console.log('查看京东联盟API文档: https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.order.row.query');
  }
}

// 获取订单状态描述
function getOrderStatusDesc(validCode) {
  const STATUS = {
    2: '无效-拆单',
    3: '无效-取消',
    4: '无效-京东帮帮主订单',
    5: '无效-账号异常',
    6: '无效-赠品类目不返佣',
    7: '无效-校园订单',
    8: '无效-企业订单',
    9: '无效-团购订单',
    10: '无效-开增值税专用发票订单',
    11: '无效-乡村推广员订单',
    12: '无效-自己推广自己下单',
    13: '无效-违规订单',
    14: '无效-来源与备案网址不符',
    15: '待付款',
    16: '已付款',
    17: '已完成',
    18: '已结算',
    19: '无效-佣金比例为0',
    20: '无效-退款',
    21: '无效-订单被拆分',
    22: '无效-非法结算',
    23: '无效-非法推广',
    24: '无效-订单来源与备案网址不符',
    25: '无效-置换订单',
    26: '无效-未使用正确推广链接',
    27: '无效-验单不通过',
    28: '无效-返厂',
    29: '无效-拒收',
    30: '无效-订单表示已拆分',
    31: '无效-赠品类目不返佣',
    32: '无效-京东账号异常',
    33: '无效-订单商品因退款导致总价不满限额',
    34: '无效-同一用户不可返佣',
    35: '无效-同一单位用户不可返佣',
    36: '无效-学生订单',
    37: '无效-违规订单',
    38: '无效-促销订单无效',
    39: '无效-会员订单不返佣',
    40: '无效-爬虫过滤',
    41: '无效-黑名单过滤',
    42: '无效-秒杀活动'
  };
  return STATUS[validCode] || `未知状态(${validCode})`;
}

// 获取当前日期字符串（偏移天数）
function getDateStr(days) {
  const date = new Date();
  date.setDate(date.getDate() + days);
  
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day} 00:00:00`;
}

// 格式化日期
function formatDate(timestamp) {
  if (!timestamp) return '未知';
  const date = new Date(timestamp);
  return date.toLocaleString();
}

// 执行主函数
main(); 