{"name": "@liuliang520500/mymcp", "version": "1.0.2", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "node dist/index.js", "test:taobao:link-convert": "tsx test/taobao/link-converter-test.ts", "test:taobao:link-parse": "tsx test/taobao/link-parser-test.ts", "test:taobao:activity-convert": "tsx test/taobao/activity-converter-test.ts", "test:taobao:order-details": "tsx test/taobao/order-details-test.ts", "test:taobao:tools-list": "tsx test/taobao/tools-list-test.ts", "test:taobao:spread-requests": "tsx test/taobao/spread-requests-test.ts", "test:taobao:create-promotion-zone": "tsx test/taobao/create-promotion-zone-test.ts", "test:taobao:tpwd-create": "tsx test/taobao/tpwd-create-test.ts", "test:taobao:material-search": "tsx test/taobao/material-search-test.ts", "test:taobao:punish-order": "tsx test/taobao/punish-order-test.ts", "test:taobao:item-info": "tsx test/taobao/item-info-test.ts", "test:taobao:optimus-promotion": "tsx test/taobao/optimus-promotion-test.ts", "test:jd:position-query": "tsx test/jd/position-query-test.ts", "test:jd:position-create": "tsx test/jd/position-create-test.ts", "test:jd:promotion-by-unionid": "tsx test/jd/promotion-by-unionid-test.ts", "test:jd:goods-query": "tsx test/jd/goods-query-test.ts", "test:jd:coupon-query": "tsx test/jd/coupon-query-test.ts", "test:pdd:goods-detail": "tsx test/pdd/goods-detail-test.ts", "test:pdd:cms-prom-url": "tsx test/pdd/cms-prom-url-generate-test.ts", "test:pdd:pid-generate": "tsx test/pdd/goods-pid-generate-test.ts", "test:pdd:pid-query": "tsx test/pdd/goods-pid-query-test.ts", "test:pdd:prom-url": "tsx test/pdd/goods-prom-url-generate-test.ts", "test:pdd:recommend": "tsx test/pdd/goods-recommend-test.ts", "test:pdd:search": "tsx test/pdd/goods-search-test.ts", "test:pdd:zs-unit-url": "tsx test/pdd/goods-zs-unit-url-generate-test.ts", "test:pdd:member-authority": "tsx test/pdd/member-authority-query-test.ts", "test:pdd:order-detail": "tsx test/pdd/order-detail-get-test.ts", "test:pdd:order-list": "tsx test/pdd/order-list-increment-get-test.ts", "test:pdd:resource-url": "tsx test/pdd/resource-url-gen-test.ts", "clean": "rimraf dist logs", "prebuild": "npm run clean", "build:webpack": "rimraf out/dist logs && webpack --config webpackconfig/webpack.config.js", "build:webpack:obfuscator": "rimraf out/dist logs && webpack --config webpackconfig/webpack.config.webpack-obfuscator.js", "prepublishOnly": "npm run build:webpack", "publish": "cd out && npm publish", "publish:patch": "cd out && npm version patch && npm publish", "publish:obfuscated": "npm run build:webpack:obfuscator && cd out && npm publish", "test:jwt-aes": "tsx scripts/test-jwt-aes.ts", "test:env-encrypt-aes": "tsx scripts/test-env-encrypt-aes.ts", "test:env-load-aes": "tsx scripts/test-env-load-aes.ts", "test:taobao:link-convert-update": "tsx test/taobao/link-converter-update-test.ts", "test:taobao:link-parse-update": "tsx test/taobao/link-parser-update-test.ts", "test:taobao:tpwd-create-update": "tsx test/taobao/tpwd-create-update-test.ts", "test:taobao:material-search-update": "tsx test/taobao/material-search-update-test.ts"}, "keywords": [], "author": "", "license": "ISC", "description": "MCP电商服务", "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@liuliang520500/jd-sdk": "^1.0.5", "@liuliang520500/pdd-sdk-new": "^1.0.0", "@liuliang520500/taobao-topclient": "^1.0.0", "@modelcontextprotocol/sdk": "^1.8.0", "@types/winston": "^2.4.4", "axios": "^1.6.7", "chalk": "^5.4.1", "dotenv": "^16.4.7", "form-data": "^4.0.0", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "mime": "^3.0.0", "node-fetch": "^3.3.2", "request": "^2.88.2", "urlencode": "^1.1.0", "winston": "^3.11.0", "ws": "^8.16.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.11.19", "copy-webpack-plugin": "^11.0.0", "javascript-obfuscator": "^4.1.1", "rimraf": "^5.0.10", "ts-loader": "^9.5.0", "ts-node": "^10.9.2", "tsx": "^4.7.1", "typescript": "^5.3.3", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0", "webpack-obfuscator": "^3.5.1"}, "bin": {"taoke-mcp": "dist/cli.js"}, "files": ["dist", "README.md"]}