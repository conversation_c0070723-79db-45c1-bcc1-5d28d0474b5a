const axios = require('axios');
const querystring = require('querystring');

/**
 * HTTP请求客户端
 */
function HttpClient() {
    this.timeout = 30000; // 默认30秒超时
}

/**
 * 执行HTTP GET请求
 * @param {String} url 请求URL
 * @param {Object} [options] 请求选项
 * @return {HttpRequest} 请求对象
 */
HttpClient.prototype.get = function(requestUrl, options) {
    options = options || {};
    options.method = 'GET';
    return this.request(requestUrl, options);
};

/**
 * 执行HTTP POST请求
 * @param {String} url 请求URL
 * @param {Object} [options] 请求选项
 * @return {HttpRequest} 请求对象
 */
HttpClient.prototype.post = function(requestUrl, options) {
    options = options || {};
    options.method = 'POST';
    return this.request(requestUrl, options);
};

/**
 * 执行HTTP请求
 * @param {String} requestUrl 请求URL
 * @param {Object} options 请求选项
 * @return {HttpRequest} 请求对象
 */
HttpClient.prototype.request = function(requestUrl, options) {
    return new HttpRequest(requestUrl, options, this.timeout);
};

/**
 * HTTP请求对象
 * @param {String} requestUrl 请求URL
 * @param {Object} options 请求选项
 * @param {Number} timeout 超时时间（毫秒）
 * @constructor
 */
function HttpRequest(requestUrl, options, timeout) {
    this.url = requestUrl;
    this.options = options || {};
    this.timeout = timeout || 30000;
    this.headers = {};
    this.data = null;
    this.params = {};
}

/**
 * 设置请求头
 * @param {String} name 头部名称
 * @param {String} value 头部值
 * @return {HttpRequest} 当前请求对象，用于链式调用
 */
HttpRequest.prototype.header = function(name, value) {
    this.headers[name] = value;
    return this;
};

/**
 * 设置请求参数
 * @param {String} name 参数名称
 * @param {String} value 参数值
 * @return {HttpRequest} 当前请求对象，用于链式调用
 */
HttpRequest.prototype.field = function(name, value) {
    this.params[name] = value;
    return this;
};

/**
 * 设置请求体数据
 * @param {Object|String} data 请求体数据
 * @return {HttpRequest} 当前请求对象，用于链式调用
 */
HttpRequest.prototype.send = function(data) {
    this.data = data;
    return this;
};

/**
 * 发送请求并接收响应
 * @param {Function} callback 回调函数，接收响应对象
 */
HttpRequest.prototype.end = function(callback) {
    const method = this.options.method || 'GET';
    const isGet = method.toUpperCase() === 'GET';
    
    // 构建axios请求配置
    const config = {
        method: method,
        url: this.url,
        headers: this.headers,
        timeout: this.timeout,
    };
    
    // 处理GET请求参数
    if (isGet && Object.keys(this.params).length > 0) {
        config.params = this.params;
    }
    
    // 处理POST请求体
    if (!isGet) {
        if (this.data) {
            config.data = this.data;
        } else if (Object.keys(this.params).length > 0) {
            // 如果是POST请求且Content-Type是application/x-www-form-urlencoded，使用querystring转换
            if (this.headers['Content-Type'] === 'application/x-www-form-urlencoded') {
                config.data = querystring.stringify(this.params);
            } else {
                config.data = this.params;
            }
        }
    }
    
    // 发送axios请求
    axios(config)
        .then(response => {
            callback({
                statusCode: response.status,
                headers: response.headers,
                body: response.data
            });
        })
        .catch(error => {
            // 处理错误响应
            if (error.response) {
                // 服务器响应了错误状态码
                callback({
                    statusCode: error.response.status,
                    headers: error.response.headers,
                    body: error.response.data
                });
            } else if (error.request) {
                // 请求已发送但没有收到响应
                callback({
                    statusCode: 0,
                    headers: {},
                    body: null,
                    error: new Error('No response received')
                });
            } else {
                // 请求设置过程中发生错误
                callback({
                    statusCode: 0,
                    headers: {},
                    body: null,
                    error: error
                });
            }
        });
};

// 创建默认HTTP客户端实例
const client = new HttpClient();

// 导出GET和POST方法
exports.get = function(url, options) {
    return client.get(url, options);
};

exports.post = function(url, options) {
    return client.post(url, options);
}; 