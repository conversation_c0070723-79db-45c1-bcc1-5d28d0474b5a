/**
 * 淘宝API公共类型定义文件
 * 集中管理淘宝相关API的类型定义
 */

/**
 * 消息内容类型
 */
export type MsgContent =
	| { type: 'text'; text: string; [key: string]: unknown }
	| { type: 'image'; data: string; mimeType: string; [key: string]: unknown }
	| {
			type: 'resource';
			resource:
				| {
						text: string;
						uri: string;
						mimeType?: string;
						[key: string]: unknown;
				  }
				| {
						uri: string;
						blob: string;
						mimeType?: string;
						[key: string]: unknown;
				  };
			[key: string]: unknown;
	  };

/**
 * 基础API响应接口
 */
export interface TaobaoApiBaseResponse {
	/**
	 * 响应码 200表示成功
	 */
	code: number;

	/**
	 * 响应消息
	 */
	message: string;

	/**
	 * 请求ID
	 */
	request_id?: string;
}

/**
 * 通用API响应接口
 */
export interface TaobaoApiResponse<T = any> extends TaobaoApiBaseResponse {
	/**
	 * 响应数据
	 */
	data?: T;
}

/**
 * 淘客商品素材搜索参数
 */
export interface MaterialSearchParams {
	/**
	 * 搜索关键词
	 */
	keyword?: string;

	/**
	 * 物料id: 即物料的填充位置
	 * 详细说明：https://market.m.taobao.com/app/qn/toutiao-new/index-pc.html#/detail/10628875?_k=gpov9a
	 */
	materialId: string;

	/**
	 * 商品ID，用于相似商品推荐
	 */
	itemId?: string;

	/**
	 * 是否是超级搜索，1:超级搜索; 0:非超级搜索
	 */
	q?: string;

	/**
	 * 是否有券，1:有券，0:无券
	 */
	hasCoupon?: string;

	/**
	 * 排序_des（降序），排序_asc（升序），销量（total_sales），淘客佣金比率（tk_rate）， 累计推广量（tk_total_sales），总支出佣金（tk_total_commi）
	 */
	sort?: string;

	/**
	 * 页码
	 */
	pageNo?: number;

	/**
	 * 页大小
	 */
	pageSize?: number;

	/**
	 * 链接形式（1:PC，2:无线）
	 */
	platform?: number;

	/**
	 * 商品筛选-设备类型：1-pc，2-无线
	 */
	deviceType?: string;

	/**
	 * 商品筛选-是否海外：1-是，0-否
	 */
	overseas?: string;

	/**
	 * 商品筛选-是否天猫：1-是，0-否
	 */
	isTmall?: string;

	/**
	 * 广告位ID
	 */
	adzoneId?: string;

	/**
	 * 站点ID
	 */
	siteId?: string;
}

/**
 * 淘宝客-公用-淘口令生成参数
 */
export interface TpwdCreateParams {
	/**
	 * 联盟官方渠道获取的淘客推广链接
	 */
	url: string;

	/**
	 * 兼容旧版本api参数，无实际作用
	 */
	text?: string;

	/**
	 * 兼容旧版本api参数，无实际作用
	 */
	logo?: string;

	/**
	 * 兼容旧版本api参数，无实际作用
	 */
	ext?: string;

	/**
	 * 兼容旧版本api参数，无实际作用
	 */
	user_id?: string;
}

/**
 * 淘口令创建响应数据
 */
export interface TpwdCreateResponseData {
	/**
	 * 淘口令
	 */
	password_simple?: string;

	/**
	 * 额外信息
	 */
	model?: string;

	/**
	 * 原始链接
	 */
	url?: string;

	/**
	 * 口令弹框内容
	 */
	content?: string;

	/**
	 * 口令弹框logo
	 */
	logo?: string;
}

/**
 * 创建推广位参数
 */
export interface CreatePromotionZoneParams {
	/**
	 * 广告位名称，注意：这里的名称可以重复
	 */
	name: string;

	/**
	 * 广告位描述
	 */
	memo?: string;

	/**
	 * 备案场景：common（通用备案），etao（一淘备案），mall（店铺备案），tomorrow（明日备案)
	 */
	type?: string;

	/**
	 * 备案场景：common（通用备案），etao（一淘备案），mall（店铺备案），tomorrow（明日备案)
	 */
	scene?: string;
}

/**
 * 创建推广位响应数据
 */
export interface CreatePromotionZoneResponseData {
	/**
	 * 推广位ID
	 */
	pid?: string;

	/**
	 * 推广位创建成功时间
	 */
	create_date?: string;
}

/**
 * 商品链接转换参数
 * 直接使用MCP参数定义，与淘宝API保持一致
 */
export interface LinkConvertParams {
	/**
	 * 业务场景ID
	 */
	biz_scene_id?: string;

	/**
	 * 推广类型
	 */
	promotion_type?: string;

	/**
	 * 物料列表
	 */
	material_list?: string;

	/**
	 * 渠道关系ID
	 */
	relation_id?: string;

	/**
	 * 卖家ID列表
	 */
	seller_id_list?: string;

	/**
	 * 商品ID列表
	 */
	item_id_list?: string;

	/**
	 * 会场ID列表
	 */
	page_id_list?: string;

	/**
	 * 会场页面内定坑商品
	 */
	target_item?: {
		item_id_list?: string[] | string;
	};

	/**
	 * 商品转链参数
	 */
	item_dto?: Array<{
		item_id?: string;
		sku_id?: number;
		is_target_coupon?: number;
		coupon_id?: string;
		external_id?: string;
		dx?: string;
		manage_pub_id?: number;
	}>;

	/**
	 * 会场页面转链参数
	 */
	page_dto?: Array<{
		page_id?: string;
		target_item_list?: string[] | string;
	}>;

	/**
	 * 店铺转链参数
	 */
	shop_dto?: Array<{
		shop_id?: string;
	}>;

	/**
	 * 链接/口令转链参数
	 */
	material_dto?: Array<{
		material_url?: string;
		is_target_coupon?: number;
		coupon_id?: string;
	}>;

	/**
	 * 会员运营ID
	 */
	special_id?: string;

	/**
	 * 加密用户标识
	 */
	uvid?: string;

	/**
	 * 启明系统任务ID
	 */
	qmtid?: string;

	/**
	 * 其他可能的参数
	 */
	[key: string]: any;
}

/**
 * 淘宝客-公用-淘宝客商品详情查询(简版)参数
 */
export interface ItemInfoParams {
	/**
	 * 商品ID串，用,分割，最大40个
	 */
	num_iids: string;

	/**
	 * 链接形式：1：PC，2：无线，默认：１
	 */
	platform?: number;

	/**
	 * ip地址，影响邮费获取，如果不传或者传入不准确，邮费无法精准提供
	 */
	ip?: string;

	/**
	 * 1-动态ID转链场景，2-消费者比价场景，3-商品库导购场景（不填默认为1）
	 */
	biz_scene_id?: string;

	/**
	 * 1-自购省，2-推广赚（代理模式专属ID，代理模式必填，非代理模式不用填写该字段）
	 */
	promotion_type?: string;

	/**
	 * 渠道关系ID
	 */
	relation_id?: string;

	/**
	 * 商品库服务账户(场景id3权限对应的memberid）
	 */
	manage_item_pub_id?: number;
}

/**
 * 商品信息
 */
export interface ItemInfo {
	/**
	 * 商品ID
	 */
	item_id?: string;

	/**
	 * 商品标题
	 */
	title?: string;

	/**
	 * 商品描述
	 */
	desc?: string;

	/**
	 * 商品主图
	 */
	pict_url?: string;

	/**
	 * 商品小图列表
	 */
	small_images?: string[];

	/**
	 * 商品一口价
	 */
	reserve_price?: string;

	/**
	 * 商品折扣价
	 */
	zk_final_price?: string;

	/**
	 * 商品月销量
	 */
	volume?: number;

	/**
	 * 商品链接
	 */
	item_url?: string;

	/**
	 * 店铺名称
	 */
	nick?: string;

	/**
	 * 店铺ID
	 */
	seller_id?: string;

	/**
	 * 店铺类型 0：淘宝，1：天猫
	 */
	user_type?: number;

	/**
	 * 优惠券金额
	 */
	coupon_amount?: number;

	/**
	 * 商品佣金比例
	 */
	commission_rate?: string;
}

/**
 * 链接转换响应数据
 */
export interface LinkConvertResponseData {
	/**
	 * 商品ID
	 */
	item_id?: string;

	/**
	 * 商品信息
	 */
	item_info?: ItemInfo;

	/**
	 * 优惠券信息
	 */
	coupon_info?: string;

	/**
	 * 商品推广链接
	 */
	url?: string;

	/**
	 * 淘口令
	 */
	tpwd?: string;

	/**
	 * 转链结果列表，用于批量转链
	 */
	results?: LinkConvertResponseData[];
}

/**
 * 链接解析参数
 */
export interface LinkParseParams {
	/**
	 * 渠道管理ID
	 */
	relation_id?: string;

	/**
	 * 广告位ID
	 */
	adzone_id?: string;

	/**
	 * 链接/口令转链参数
	 */
	material_dto?: Array<{
		/**
		 * 物料链接，可以为url或淘口令
		 */
		material_url?: string;
	}>;

	/**
	 * 有origin_pid使用场景需入参
	 */
	fields?: string;
}

/**
 * 链接解析响应数据
 */
export interface LinkParseResponseData {
	/**
	 * 商品ID
	 */
	item_id?: string;

	/**
	 * 商品标题
	 */
	title?: string;

	/**
	 * 商品主图
	 */
	pict_url?: string;

	/**
	 * 商品链接
	 */
	url?: string;

	/**
	 * 转换后的淘口令
	 */
	tpwd?: string;

	/**
	 * 优惠券信息
	 */
	coupon_info?: string;

	/**
	 * 商品优惠券链接
	 */
	coupon_click_url?: string;

	/**
	 * 商品佣金比例
	 */
	commission_rate?: string;
}

/**
 * 活动信息查询参数
 */
export interface ActivityInfoParams {
	/**
	 * 活动会场ID
	 */
	activityId: string;

	/**
	 * 推广位ID
	 */
	adzoneId?: string;

	/**
	 * 渠道关系ID
	 */
	relationId?: string;

	/**
	 * 媒体平台类型
	 */
	platform?: number;

	/**
	 * 自定义参数
	 */
	subPid?: string;

	/**
	 * 淘宝联盟ID
	 */
	unionId?: string;
}

/**
 * 活动信息响应数据
 */
export interface ActivityInfoResponseData {
	/**
	 * 活动ID
	 */
	activity_id?: string;

	/**
	 * 活动名称
	 */
	activity_name?: string;

	/**
	 * 活动开始时间
	 */
	start_time?: string;

	/**
	 * 活动结束时间
	 */
	end_time?: string;

	/**
	 * 活动推广链接
	 */
	click_url?: string;

	/**
	 * 淘口令
	 */
	tpwd?: string;
}

/**
 * 订单查询参数
 */
export interface OrderDetailsParams {
	/**
	 * 查询时间类型，1：按照订单淘客创建时间查询，2:按照订单淘客付款时间查询，3:按照订单淘客结算时间查询，4:按照订单更新时间
	 */
	query_type?: number;

	/**
	 * 位点，除第一页之外，都需要传递；前端原样返回。
	 */
	position_index?: string;

	/**
	 * 页大小，默认20，1~100
	 */
	page_size?: number;

	/**
	 * 推广者角色类型,2:二方，3:三方，不传，表示所有角色
	 */
	member_type?: number;

	/**
	 * 淘客订单状态，12-付款，13-关闭，14-确认收货，3-结算成功;不传，表示所有状态
	 */
	tk_status?: number;

	/**
	 * 订单查询结束时间，订单开始时间至订单结束时间，中间时间段日常要求不超过3个小时，但如618、双11、年货节等大促期间预估时间段不可超过20分钟，超过会提示错误，调用时请务必注意时间段的选择，以保证亲能正常调用！
	 */
	end_time: string;

	/**
	 * 订单查询开始时间
	 */
	start_time: string;

	/**
	 * 跳转类型，当向前或者向后翻页必须提供,-1: 向前翻页,1：向后翻页
	 */
	jump_type?: number;

	/**
	 * 第几页，默认1，1~100
	 */
	page_no?: number;

	/**
	 * 筛选订单类型，1:所有订单，2:渠道订单，3:会员运营订单，默认为1
	 */
	order_scene?: number;

	/**
	 * member组ID
	 */
	member_group_id?: number;
}

/**
 * 订单详情数据
 */
export interface OrderDetail {
	/**
	 * 订单ID
	 */
	trade_id?: string;

	/**
	 * 订单标题
	 */
	item_title?: string;

	/**
	 * 商品ID
	 */
	item_id?: string;

	/**
	 * 商品单价
	 */
	item_price?: string;

	/**
	 * 商品数量
	 */
	item_num?: number;

	/**
	 * 订单创建时间
	 */
	create_time?: string;

	/**
	 * 订单支付时间
	 */
	pay_time?: string;

	/**
	 * 订单结算时间
	 */
	settle_time?: string;

	/**
	 * 佣金金额
	 */
	commission?: string;

	/**
	 * 订单状态：12-付款，13-关闭，14-确认收货，3-结算成功
	 */
	tk_status?: number;

	/**
	 * 佣金比例
	 */
	commission_rate?: string;

	/**
	 * 推广位ID
	 */
	adzone_id?: string;
}

/**
 * 订单查询响应数据
 */
export interface OrderDetailsResponseData {
	/**
	 * 是否有下一页
	 */
	has_next?: boolean;

	/**
	 * 位点，用于下一页查询
	 */
	position_index?: string;

	/**
	 * 订单列表
	 */
	order_list?: OrderDetail[];

	/**
	 * 订单总数
	 */
	total_count?: number;
}

/**
 * 优惠券信息
 */
export interface CouponInfo {
	/**
	 * 优惠券ID
	 */
	coupon_id?: string;

	/**
	 * 优惠券金额
	 */
	coupon_amount?: number;

	/**
	 * 优惠券门槛金额
	 */
	coupon_start_fee?: string;

	/**
	 * 优惠券开始时间
	 */
	coupon_start_time?: string;

	/**
	 * 优惠券结束时间
	 */
	coupon_end_time?: string;

	/**
	 * 优惠券总量
	 */
	coupon_total_count?: number;

	/**
	 * 优惠券剩余量
	 */
	coupon_remain_count?: number;
}

/**
 * 通用响应数据
 */
export interface BaseResponseData<T = any> {
	/**
	 * 响应数据
	 */
	result?: T;

	/**
	 * 是否成功
	 */
	success?: boolean;

	/**
	 * 错误码
	 */
	code?: string;

	/**
	 * 错误信息
	 */
	msg?: string;
}

/**
 * 淘宝客-公用-长链转短链参数
 */
export interface SpreadRequestsParams {
	/**
	 * 请求列表，内部包含多个url
	 */
	requests: Array<{
		/**
		 * 原始url, 只支持uland.taobao.com，s.click.taobao.com， ai.taobao.com，temai.taobao.com的域名转换
		 */
		url: string;
	}>;
}

/**
 * 淘宝客-公用-长链转短链响应数据
 */
export interface SpreadRequestsResponseData {
	/**
	 * 传入的原始长链接
	 */
	url?: string;

	/**
	 * 转换后的短链接
	 */
	short_url?: string;

	/**
	 * 响应结果列表
	 */
	results?: Array<{
		url?: string;
		short_url?: string;
	}>;
}

/**
 * 淘宝客-服务商-权益物料精选参数
 */
export interface OptimusPromotionParams {
	/**
	 * 页大小，每次请求限到10以内
	 */
	page_size?: number;

	/**
	 * 第几页，默认：1
	 */
	page_num?: number;

	/**
	 * 官方提供的权益物料Id。有价券-37104、大额店铺券-37116、天猫店铺券-62191、券券补-61809 更多权益物料id敬请期待！
	 */
	promotion_id: number;
}

/**
 * 淘宝客-服务商-权益物料精选响应数据
 */
export interface OptimusPromotionResponseData {
	/**
	 * 权益物料列表
	 */
	result_list?: Array<{
		/**
		 * 商品ID
		 */
		item_id?: string;

		/**
		 * 商品标题
		 */
		title?: string;

		/**
		 * 商品主图
		 */
		pict_url?: string;

		/**
		 * 商品价格
		 */
		reserve_price?: string;

		/**
		 * 商品折扣价
		 */
		zk_final_price?: string;

		/**
		 * 佣金比例
		 */
		commission_rate?: string;

		/**
		 * 优惠券金额
		 */
		coupon_amount?: number;

		/**
		 * 优惠券信息
		 */
		coupon_info?: string;

		/**
		 * 商品链接
		 */
		click_url?: string;
	}>;
}

/**
 * 调用MCP工具的响应格式
 */
export interface McpToolResponse {
	content: MsgContent[];
	isError?: boolean;
}
