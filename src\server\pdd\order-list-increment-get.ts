import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { OrderListIncrementGetParams } from './types.js';

/**
 * 按照更新时间段增量同步推广订单信息 API
 */
export class OrderListIncrementGet extends BaseApi {
	/**
	 * 按照更新时间段增量同步推广订单信息
	 * @param params 查询参数
	 * @returns 查询结果
	 */
	async getOrderList(params: OrderListIncrementGetParams): Promise<any> {
		try {
			log(
				`按照更新时间段增量同步推广订单信息参数: ${JSON.stringify(
					params
				)}`
			);

			// 调用API
			return this.callApi(
				'pdd.ddk.oauth.order.list.increment.get',
				params
			);
		} catch (error) {
			log(`按照更新时间段增量同步推广订单信息失败: ${error}`);
			throw error;
		}
	}
}
