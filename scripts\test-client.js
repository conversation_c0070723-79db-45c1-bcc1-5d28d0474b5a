import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js";
import { fileURLToPath } from "url";
import { dirname, join } from "path";
import { z } from "zod";

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// 定义期望的响应模式
const ToolResultSchema = z.object({
  content: z.array(
    z.object({
      type: z.string(),
      text: z.string().optional(),
      // 其他可能的字段...
    })
  ),
  isError: z.boolean().optional()
});

async function main() {
  console.log("创建MCP客户端...");
  
  // 创建MCP客户端
  const client = new Client({
    name: "TaobaoClientTest",
    version: "1.0.0"
  });
  
  // 使用StdioClientTransport连接到服务器
  const transport = new StdioClientTransport({
    command: "node",
    args: ["dist/index.js"],
    cwd: process.cwd(),
    stderr: "pipe"
  });
  
  try {
    // 添加调试监听
    if (transport.stderr) {
      transport.stderr.on('data', (data) => {
        console.error(`服务器stderr: ${data.toString()}`);
      });
    }
    
    console.log("正在连接到淘宝MCP服务...");
    await client.connect(transport);
    console.log("已连接到淘宝MCP服务");
    
    // 获取服务器信息
    const serverInfo = client.getServerVersion();
    console.log(`服务器信息: ${serverInfo?.name} v${serverInfo?.version}`);
    
    // 尝试使用最直接的调用方式
    console.log("手动构造并发送tools/call请求...");
    
    // 构造未经高级包装的原始请求
    const request = {
      jsonrpc: "2.0",
      id: 123,
      method: "tools/call",
      params: {
        name: "convertLink",
        arguments: {
          url: "https://s.click.taobao.com/jxLuM3s"
        }
      }
    };
    
    console.log("发送原始请求:", JSON.stringify(request, null, 2));
    
    // 直接发送到传输层
    transport.send(request);
    
    // 手动监听响应
    let responseReceived = false;
    const originalOnMessage = transport.onmessage;
    transport.onmessage = (message) => {
      console.log("收到原始响应:", JSON.stringify(message, null, 2));
      responseReceived = true;
      if (originalOnMessage) {
        originalOnMessage.call(transport, message);
      }
    };
    
    // 等待响应或超时
    await new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        if (responseReceived) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 1000);
      
      // 最多等待10秒
      setTimeout(() => {
        clearInterval(checkInterval);
        if (!responseReceived) {
          console.log("等待响应超时");
        }
        resolve();
      }, 10000);
    });
    
  } catch (error) {
    console.error('连接失败:', error);
    if (error.stack) {
      console.error('错误堆栈:', error.stack);
    }
  } finally {
    // 关闭连接
    try {
      console.log("正在断开连接...");
      await client.close();
      console.log("已断开MCP连接");
    } catch (err) {
      console.error("关闭连接时出错:", err);
    }
  }
}

// 运行测试
main().catch(error => {
  console.error("测试客户端出错:", error);
  console.error(error.stack);
  process.exit(1);
}); 