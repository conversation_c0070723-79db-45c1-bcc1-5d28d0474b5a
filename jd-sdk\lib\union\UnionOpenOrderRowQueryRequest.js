/**
 * 联盟订单行查询API
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.order.row.query
 */
const { BaseApi } = require('../api');

/**
 * 联盟订单行查询API请求类
 * 提供订单行数据查询接口，查询最近3个月内联盟推广订单
 */
class UnionOpenOrderRowQueryRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.order.row.query';
  }

  /**
   * 执行订单行查询请求
   * @param {Object} params 请求参数
   * @param {Object} params.orderReq 订单查询请求对象
   * @param {Number} [params.orderReq.pageIndex] 页码
   * @param {Number} [params.orderReq.pageSize] 每页数量，上限500
   * @param {Number} [params.orderReq.type] 订单时间查询类型(1：下单时间，2：完成时间，3：更新时间)
   * @param {String} [params.orderReq.startTime] 开始时间，格式yyyy-MM-dd HH:mm:ss，与endTime间隔不超过1小时
   * @param {String} [params.orderReq.endTime] 结束时间，格式yyyy-MM-dd HH:mm:ss，与startTime间隔不超过1小时
   * @param {String} [params.orderReq.childUnionId] 子站长ID
   * @param {String} [params.orderReq.key] 工具商传入推客的授权key
   * @param {Array<String>} [params.orderReq.fields] 字段列表，可选值：goodsInfo等
   * @returns {Promise<Object>} 请求结果
   */
  async query(params = {}) {
    if (!params.orderReq) {
      throw new Error('orderReq不能为空');
    }

    const apiParams = {
      orderReq: this.cleanParams(params.orderReq)
    };

    return this.execute(apiParams);
  }

  /**
   * 清理参数，移除空值
   * @param {Object} params 原始参数
   * @returns {Object} 清理后的参数
   */
  cleanParams(params) {
    const cleanedParams = {};
    
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        cleanedParams[key] = params[key];
      }
    }
    
    return cleanedParams;
  }
}

module.exports = {
  UnionOpenOrderRowQueryRequest
}; 