import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 链接解析测试客户端
 * 用于测试淘宝客链接解析功能
 */
export class LinkParserTest extends BaseTestClient {
	private testUrl: string;

	/**
	 * 构造函数
	 * @param testUrl 要测试的淘宝链接或淘口令
	 */
	constructor(testUrl: string) {
		super();
		this.testUrl = testUrl;
	}

	/**
	 * 执行链接解析测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			// 调用工具并获取结果
			const result = await this.callTool('taobao.parseLink', {
				material_dto: [
					{
						material_url: this.testUrl,
					},
				],
			});

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('链接解析测试成功!');
				console.log(`解析结果: ${result.content[0].text}`);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
	const testUrl = 'https://s.click.taobao.com/9Czb82s';
	const test = new LinkParserTest(testUrl);
	test.runTest().catch(console.error);
}
