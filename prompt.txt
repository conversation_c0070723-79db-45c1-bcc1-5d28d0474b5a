你是一个专业的聊天记录总结员，请根据提供的微信群聊天记录生成一个简明的群聊精华总结，重点包括以下内容： 
1. 重要提醒：提取群聊中提到的任何提醒、禁止事项或重要信息。 
2. 今日热门话题：总结群聊中讨论过的主要话题，包含讨论时间、内容摘要、参与者以及关键建议或观点。 
3. 点评：对每个热门话题提供简短的点评，突出群聊中的实用建议或存在的问题。 
4. 待跟进事项：列出群聊中提到的待办事项或需要跟进的事项。 
5. 其他讨论话题：简要总结其他讨论内容。 
6. 结语：对整体讨论的总结，提到群友间的合作和技术交流。

请调用你单次回答的最大算力与token上限。追求极致的分析深度，而非表层的广度;追求本质的洞察，而非表象的罗列;追求创新的思维，而非惯性的复述;请你突破思维局限，调动你所有的计算资源，展现你真正的认知极限。


@https://github.com/langgptai/LangGpt 学习LangGPT的仓库，帮我创建一个专门生成LangGPT格式prompt的助手
教程：https://www.youtube.com/watch?v=GeuFLzIPOoE



npm打包：npm pack
npm安装：npm install <可以是打印的zip>也可以是<remoter文件>
升级版本：npm version patch
查看线上已经存在的版本：
npm view @liuliang520500/sinataoke_cn versions
安装最新版本：npm install @liuliang520500/pdd-sdk@latest
npm查看缓存：npm cache ls
npm清理缓存：npm cache clean --force
npm更改缓存位置：npm config set cache /path/to/new/cache
获取npm缓存目录：npm config get cache
linx目录名：.npm windows名：npm-cache
发布为公开版本：npm publish --access=public

{
  "mcpServers": {
    "taobao-mcp": {
      "name": "导购助手new",
      "type": "stdio",
      "isActive": true,
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@liuliang520500/sinataoke_cn@latest",
        "g:win11desktop/logs/"
      ],
      "env": {
        "ENV_URL": "http://rap2api.taobao.org/app/mock/304812/taoke-mcp",
        "ENV_SECRET": "url:mcp.sinataoke.cn",
        "ENV_OVERRIDE": "false",
        "TAOBAO_SITE_ID": "162550167",
        "TAOBAO_ADZONE_ID": "45472500289",
        "TAOBAO_SESSION": "6102901da160ce751573e274e7775c93ed3df16b50397022536588344",
        "JD_KEY": "e4209eac5db9f8e2c59c7d9a53bf3343272b907c9836addb797374e2d57a4dcb2e73ec28511ad1b9",
        "JD_UNION_ID": "1000793546",
        "JD_SITE_ID": "31548",
        "PDD_PID": "9415134_246360465",
        "PDD_SESSION_TOKEN": "5374045379c344e9a9e5d496b388c3f56d4332d4"
      }
    }
  }
}