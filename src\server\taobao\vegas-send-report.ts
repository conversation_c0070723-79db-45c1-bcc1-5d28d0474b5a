import { log } from '../../utils/logger.js';
import { getEnvVar } from '../../utils/env.js';
import { BaseApi } from './base-api.js';

/**
 * 淘宝客-服务商-查询红包发放个数
 * 提供查询红包发放个数功能
 */
export class VegasSendReport extends BaseApi {
	/**
	 * 获取红包发放个数
	 * @param params 查询参数
	 * @returns 原始API响应
	 */
	public async getVegasSendReport(params: any): Promise<any> {
		try {
			log(`执行红包发放个数查询: ${JSON.stringify(params)}`);

			// 获取PID
			const pid = getEnvVar('TAOBAO_PID');

			// 构建API参数，添加pid
			const apiParams = {
				...params,
				pid,
			};

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.sc.vegas.send.report',
				apiParams
			);

			log(`红包发放个数查询结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			throw error;
		}
	}
}
