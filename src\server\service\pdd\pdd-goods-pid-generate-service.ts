import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { PddApiManager } from '../../pdd/api-manager.js';
import { PddParams } from './params.js';

/**
 * 拼多多推广位生成服务类
 * 处理多多进宝推广位创建功能
 */
export class PddGoodsPidGenerateService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 拼多多API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: PddApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册推广位生成工具
	 * 接口名称：pdd.ddk.oauth.goods.pid.generate
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('goods.pid.generate');

		this.mcpServer.tool(
			toolName,
			'多多进宝推广位创建[pdd.ddk.oauth.goods.pid.generate]',
			PddParams.goodsPidGenerate,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);

					try {
						// 执行推广位生成
						const result =
							await this.apiManager.goodsPidGenerate.generate(
								params
							);

						log(
							`工具 ${toolName} 执行结果: ${JSON.stringify(
								result
							)}`
						);

						return {
							content: [
								{
									type: 'text',
									text: JSON.stringify(result, null, 2),
								},
							],
						};
					} catch (apiError) {
						logError(
							`推广位生成API调用失败: ${
								apiError instanceof Error
									? apiError.message
									: String(apiError)
							}`
						);
						throw apiError;
					}
				} catch (error) {
					const errorMessage =
						error instanceof Error ? error.message : String(error);
					logError(`工具 ${toolName} 执行出错: ${errorMessage}`);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${errorMessage}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
