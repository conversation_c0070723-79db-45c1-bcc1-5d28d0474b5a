// 导入淘宝开放平台 SDK

// import { TopClient } from '@liuliang520500/taobao-topclient';
const { TopClient } = await import('@liuliang520500/taobao-topclient');
/**
 * 淘宝开放平台客户端配置接口
 */
export interface TopClientConfig {
  appkey: string;
  appsecret: string;
  url?: string;
}

/**
 * 淘宝开放平台客户端类
 */
export class TaobaoClient {
  private client: any;

  constructor(config: TopClientConfig) {
    this.client = new TopClient({
      appkey: config.appkey,
      appsecret: config.appsecret,
      url: config.url || 'http://gw.api.taobao.com/router/rest'
    });
  }

  /**
   * 执行API调用
   * @param method API方法名
   * @param params API参数
   * @returns Promise
   */
  execute(method: string, params: Record<string, any>): Promise<any> {
    return new Promise((resolve, reject) => {
      this.client.execute(method, params, (error: Error, response: any) => {
        if (error) {
          reject(error);
        } else {
          resolve(response);
        }
      });
    });
  }
}

export { TopClient }; 