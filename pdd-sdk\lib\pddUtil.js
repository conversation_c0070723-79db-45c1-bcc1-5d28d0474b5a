var crypto = require('crypto-js');
var util = require('util');

/**
 * 计算哈希值
 *
 * @param {String} method 哈希方法，例如：'md5'、'sha1'
 * @param {String|Buffer} s 待哈希的字符串或缓冲区
 * @param {String} [format] 输出格式，可以是'hex'或'base64'，默认为'hex'
 * @return {String} 哈希字符串
 * @public
 */
exports.hash = function hash(method, s, format) {
    if (method.toLowerCase() === 'md5') {
        return crypto.MD5(s).toString();
    } else if (method.toLowerCase() === 'sha1') {
        return crypto.SHA1(s).toString();
    } else if (method.toLowerCase() === 'sha256') {
        return crypto.SHA256(s).toString();
    }
    
    throw new Error('Unsupported hash method: ' + method);
};

/**
 * 计算MD5哈希
 *
 * @param {String|Buffer} s 待哈希的字符串或缓冲区
 * @param {String} [format] 输出格式，可以是'hex'或'base64'，默认为'hex'
 * @return {String} MD5哈希字符串
 * @public
 */
exports.md5 = function md5(s, format) {
    return crypto.MD5(s).toString();
};

/**
 * 获取当前时间戳（格式：yyyy-MM-dd HH:mm:ss）
 * @param {Date} [d] 日期对象，默认为当前时间
 * @return {String} 格式化的时间戳字符串
 */
exports.formatDate = function (d) {
    d = d || new Date();
    if (!(d instanceof Date)) {
        d = new Date(d);
    }

    var date = d.getDate();
    if (date < 10) {
        date = '0' + date;
    }
    var month = d.getMonth() + 1;
    if (month < 10) {
        month = '0' + month;
    }
    var hours = d.getHours();
    if (hours < 10) {
        hours = '0' + hours;
    }
    var minutes = d.getMinutes();
    if (minutes < 10) {
        minutes = '0' + minutes;
    }
    var seconds = d.getSeconds();
    if (seconds < 10) {
        seconds = '0' + seconds;
    }
    return d.getFullYear() + '-' + month + '-' + date + ' ' +
        hours + ':' + minutes + ':' + seconds;
};

/**
 * 获取UNIX时间戳（毫秒）
 * @return {Number} 当前时间的毫秒级时间戳
 */
exports.getTimeMillis = function() {
    return new Date().getTime();
};

/**
 * 检查必填参数
 * @param {Object} params 参数对象
 * @param {String|Array} keys 必填参数名称，可以是字符串或数组
 * @return {Error|undefined} 如果缺少必填参数，返回错误对象；否则返回undefined
 */
exports.checkRequired = function (params, keys) {
    if (!Array.isArray(keys)) {
        keys = [keys];
    }
    for (var i = 0, l = keys.length; i < l; i++) {
        var k = keys[i];
        if (!params.hasOwnProperty(k)) {
            var err = new Error('`' + k + '` required');
            err.name = "ParameterMissingError";
            return err;
        }
    }
};

/**
 * 获取API响应名称
 * @param {String} apiName API名称
 * @return {String} API响应名称
 */
exports.getApiResponseName = function(apiName) {
    return apiName + "_response";
};

/**
 * 将对象按键名排序
 * @param {Object} obj 待排序的对象
 * @return {Object} 排序后的对象
 */
function sortObject(obj) {
    if (!obj || Array.isArray(obj) || typeof obj !== 'object') {
        return obj;
    }
    var result = {};
    var keys = Object.keys(obj).sort();
    for (var i = 0; i < keys.length; i++) {
        var key = keys[i];
        result[key] = sortObject(obj[key]);
    }
    return result;
}

/**
 * 获取本地IP地址
 * @return {String} 本地IP地址
 */
exports.getLocalIPAddress = function() {
    var interfaces = require('os').networkInterfaces();
    for (var devName in interfaces) {
        var iface = interfaces[devName];
        for (var i = 0; i < iface.length; i++) {
            var alias = iface[i];
            if (alias.family === 'IPv4' && alias.address !== '127.0.0.1' && !alias.internal) {
                return alias.address;
            }
        }
    }
};

exports.sortObject = sortObject; 