/**
 * 拼多多多多进宝转链API测试
 */

// 导入依赖
const { PddClient } = require('../index');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 加载环境变量（从项目根目录的.env文件）
const envPath = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log(`已加载环境变量文件: ${envPath}`);
} else {
  console.warn(`警告: 环境变量文件不存在: ${envPath}`);
}

// 检查必要的环境变量是否存在
const requiredEnvVars = [
  'PDD_CLIENT_ID', 
  'PDD_CLIENT_SECRET', 
  'PDD_SESSION_TOKEN',
  'PDD_PID'
];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error(`缺少必要的环境变量: ${missingEnvVars.join(', ')}`);
  console.error('请确保.env文件中包含这些变量');
  process.exit(1);
}

// 初始化客户端
const client = new PddClient({
  clientId: process.env.PDD_CLIENT_ID,
  clientSecret: process.env.PDD_CLIENT_SECRET,
  accessToken: process.env.PDD_SESSION_TOKEN,
  debug: true // 启用调试模式
});

// 测试多多进宝转链
async function testZsUnitUrlGenerate() {
  console.log('======= 拼多多多多进宝转链测试 =======');
  console.log('客户端配置:');
  console.log('- clientId:', process.env.PDD_CLIENT_ID);
  console.log('- accessToken:', maskToken(process.env.PDD_SESSION_TOKEN));
  console.log('- PID:', process.env.PDD_PID);
  console.log('\n开始生成转链...');
  
  // 使用固定的源链接
  const sourceUrl = 'https://mobile.yangkeduo.com/duo_coupon_landing.html?goods_id=************&pid=42653152_302936378&display_mod=101&goods_sign=E9z2AMfsxIdl2k6RzcremKdeyigTzp3X_JQaTkqtGm4&authDuoId=8831127&cpsSign=CC_250407_42653152_302936378_6d55d98f0aef10bae6e58d6379ad4958&_x_ddjb_act=%7B%22st%22%3A%221%22%7D&duoduo_type=2&customParameters=%7B%22uid%22%3A%221ap5w2OdomhBfP52LeYh1iA_c_c%22%2C%22from%22%3A%22%22%2C%22chan%22%3A%223aff16ea14b799%22%7D';
  
  // 构建请求参数
  const options = {
    pid: process.env.PDD_PID,
    sourceUrl: sourceUrl,
    generateSchemaUrl: true,
    generateWeAppInfo: true,
    generateShortUrl: true,
    generateWeAppLongLink: true
  };
  
  console.log('请求参数:', JSON.stringify(options, null, 2));
    // 执行请求
    const result = await client.goodsZsUnitUrlGenerate.generate(options);
    
    // 检查是否有错误响应
  
}

// 辅助函数：掩码显示令牌
function maskToken(token) {
  if (!token) return 'undefined';
  return token.substr(0, 6) + '****' + token.substr(-6);
}

// 执行测试
testZsUnitUrlGenerate(); 