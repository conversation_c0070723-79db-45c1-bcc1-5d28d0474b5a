/**
 * 京东API公共类型定义
 */

/**
 * 推广位类型枚举
 */
export enum JdUnionType {
  /**
   * 私域推广位
   */
  PRIVATE = 3,
  
  /**
   * 联盟后台推广位
   */
  UNION = 4
}

/**
 * 站点类型枚举
 */
export enum JdSiteType {
  /**
   * 无线站
   */
  MOBILE = 3,
  
  /**
   * PC站
   */
  PC = 4
}

/**
 * 基础API参数
 */
export interface JdBaseParams {
  /**
   * 京东联盟ID
   */
  unionId?: number;
  
  /**
   * 授权key
   */
  key?: string;
  
  /**
   * 站点ID
   */
  siteId?: number;
}

/**
 * 推广位查询参数
 */
export interface JdPositionQueryParams extends JdBaseParams {
  /**
   * 联盟推广位类型
   * 3: 私域推广位
   * 4: 联盟后台推广位
   */
  unionType: number;
  
  /**
   * 页码
   */
  pageIndex?: number;
  
  /**
   * 每页条数
   */
  pageSize?: number;
}

/**
 * 推广位创建参数
 */
export interface JdPositionCreateParams extends JdBaseParams {
  /**
   * 联盟推广位类型
   * 3: 私域推广位
   * 4: 联盟后台推广位
   */
  unionType: number;
  
  /**
   * 站点类型
   * 3: 无线站
   * 4: PC站
   */
  type: number;
  
  /**
   * 推广位名称列表
   */
  spaceNameList: string[];
  
  /**
   * PID格式
   * 可选参数
   */
  pid?: string;
}

/**
 * API响应基础格式
 */
export interface JdApiResponse<T = any> {
  /**
   * 状态码
   * 200表示成功
   */
  code: number;
  
  /**
   * 响应消息
   */
  message: string;
  
  /**
   * 请求ID
   */
  requestId: string;
  
  /**
   * 响应数据
   */
  data?: T;
}

/**
 * 推广位查询响应数据
 */
export interface JdPositionQueryResponseData {
  /**
   * 总记录数
   */
  totalCount: number;
  
  /**
   * 推广位列表
   */
  positionList: JdPositionInfo[];
}

/**
 * 推广位信息
 */
export interface JdPositionInfo {
  /**
   * 推广位ID
   */
  id: number;
  
  /**
   * 推广位名称
   */
  spaceName: string;
  
  /**
   * 站点ID
   */
  siteId: number;
  
  /**
   * 推广位类型
   */
  type: number;
  
  /**
   * PID
   */
  pid: string;
  
  /**
   * 推广位状态
   * 1：正常
   * 0：无效
   */
  status: number;
  
  /**
   * 创建时间
   * 格式：yyyy-MM-dd HH:mm:ss
   */
  createTime: string;
}

/**
 * 推广位创建响应数据
 */
export interface JdPositionCreateResponseData {
  /**
   * PID
   * 键为推广位名称，值为PID
   */
  pid: Record<string, string>;
  
  /**
   * 推广位ID列表
   * 键为推广位名称，值为推广位ID
   */
  resultList: Record<string, number>;
  
  /**
   * 站点ID
   */
  siteId: number;
  
  /**
   * 推广位类型
   */
  type: number;
  
  /**
   * 联盟ID
   */
  unionId: number;
}

/**
 * 推广链接获取参数
 */
export interface JdPromotionCodeParams {
  /**
   * 推广物料ID
   * 如商品skuId、活动ID、页面ID等
   */
  materialId: string;
  
  /**
   * 站点ID，通过推广位获取
   */
  siteId?: number;
  
  /**
   * 推广位ID，通过推广位获取
   */
  positionId?: number;
  
  /**
   * 子联盟ID，推广的联盟ID
   */
  subUnionId?: string;
  
  /**
   * 联盟ID
   */
  unionId: number;
  
  /**
   * 自定义扩展信息，建议json格式
   */
  ext1?: string;
  
  /**
   * 链接类型，默认1
   * 1: 长链
   * 2: 短链
   * 3: 长链+短链
   */
  chainType?: number;
  
  /**
   * 优惠券领取链接
   */
  couponUrl?: string;
  
  /**
   * 礼金批次ID
   */
  giftCouponKey?: string;
  
  /**
   * 渠道ID
   */
  channelId?: number;
  
  /**
   * 请求命令
   * 1: 查询商品
   * 2: 查店铺
   */
  command?: number;
  
  /**
   * 微信小程序appid
   */
  weChatType?: number;
  
  /**
   * 微信推广二维码尺寸
   */
  qrSize?: number;
  
  /**
   * 社交媒体平台ID
   */
  pid?: string;
}

/**
 * 推广链接获取响应数据
 */
export interface JdPromotionCodeResponseData {
  /**
   * 短链接，微信环境下，绑定到微信开放平台
   */
  shortURL?: string;
  
  /**
   * 长链接，微信环境下，绑定到微信开放平台
   */
  clickURL?: string;
  
  /**
   * 微信收藏短链接
   */
  weChatShortURL?: string;
  
  /**
   * 微信收藏长链接
   */
  weChatURL?: string;
}

/**
 * 商品查询参数
 */
export interface JdGoodsQueryParams extends JdBaseParams {
  /**
   * 搜索关键词，支持多个关键词，用英文逗号分隔
   */
  keyword?: string;
  
  /**
   * 京东skuID串，逗号分隔，最多100个
   */
  skuIds?: string;
  
  /**
   * 一级类目ID
   */
  cid1?: number;
  
  /**
   * 二级类目ID
   */
  cid2?: number;
  
  /**
   * 三级类目ID
   */
  cid3?: number;
  
  /**
   * 商品类型：自营[g]，POP[p]
   */
  owner?: string;
  
  /**
   * 1.已售罄过滤（1：过滤，0：不过滤，不传默认不过滤）
   */
  deliveryType?: number;
  
  /**
   * 是否是优惠券商品，1：有优惠券，0：无优惠券
   */
  isCoupon?: number;
  
  /**
   * 是否是拼购商品，1：拼购商品，0：非拼购商品
   */
  isPG?: number;
  
  /**
   * 是否是爆款，1：爆款商品，0：非爆款商品
   */
  isHot?: number;
  
  /**
   * 价格精确匹配，单位：元
   */
  pricefrom?: number;
  
  /**
   * 价格上限，单位：元
   */
  priceto?: number;
  
  /**
   * 佣金比例区间开始
   */
  commissionShareStart?: number;
  
  /**
   * 佣金比例区间结束
   */
  commissionShareEnd?: number;
  
  /**
   * 排序字段，默认搜索综合排序。
   * price：单价
   * commissionShare：佣金比例
   * commission：佣金
   * inOrderCount30Days：30天引单量
   * inOrderComm30Days：30天支出佣金
   */
  sortName?: string;
  
  /**
   * 排序方式，默认降序
   * asc：升序
   * desc：降序
   */
  sort?: string;
  
  /**
   * 页码
   */
  pageIndex?: number;
  
  /**
   * 每页数量，默认20，上限50
   */
  pageSize?: number;
  
  /**
   * 品牌ID
   */
  brandCode?: string;
  
  /**
   * 店铺ID
   */
  shopId?: number;
  
  /**
   * 联盟ID
   */
  unionId?: number;
  
  /**
   * 是否是总代理，默认总代理
   */
  pidSt?: number;
  
  /**
   * 是否为京喜商品，1：是，0：否
   */
  isJxwl?: number;
  
  /**
   * 是否秒杀商品，1：是，0：否
   */
  isSeckill?: number;
}

/**
 * 商品查询响应数据
 */
export interface JdGoodsQueryResponseData {
  /**
   * 总记录数
   */
  totalCount: number;
  
  /**
   * 商品列表
   */
  data: JdGoodsInfo[];
}

/**
 * 商品信息
 */
export interface JdGoodsInfo {
  /**
   * 商品ID
   */
  skuId: number;
  
  /**
   * 商品名称
   */
  skuName: string;
  
  /**
   * 商品图片
   */
  imageInfo?: {
    /**
     * 图片地址列表
     */
    imageList: Array<{
      /**
       * 图片链接
       */
      url: string
    }>
  };
  
  /**
   * 价格信息
   */
  priceInfo?: {
    /**
     * 商品价格
     */
    price: number;
    /**
     * 促销价
     */
    lowestPrice?: number;
    /**
     * 历史最低价
     */
    lowestPriceType?: number;
  };
  
  /**
   * 佣金信息
   */
  commissionInfo?: {
    /**
     * 佣金
     */
    commission: number;
    /**
     * 佣金比例
     */
    commissionShare: number;
  };
  
  /**
   * 店铺信息
   */
  shopInfo?: {
    /**
     * 店铺ID
     */
    shopId: number;
    /**
     * 店铺名称
     */
    shopName: string;
  };
  
  /**
   * 推广信息
   */
  promotionInfo?: {
    /**
     * 促销标签
     */
    promotionList?: Array<{
      /**
       * 促销文案
       */
      text: string
    }>;
  };
  
  /**
   * 优惠券信息
   */
  couponInfo?: {
    /**
     * 优惠券列表
     */
    couponList: Array<{
      /**
       * 优惠券面额
       */
      discount: number;
      /**
       * 优惠券链接
       */
      link: string;
      /**
       * 优惠券开始时间
       */
      beginTime: string;
      /**
       * 优惠券结束时间
       */
      endTime: string;
    }>;
  };
}

/**
 * 优惠券查询参数
 */
export interface JdCouponQueryParams extends JdBaseParams {
  /**
   * 优惠券链接集合
   * 支持数组格式和字符串格式(逗号分隔)
   */
  couponUrls: string[] | string;
}

/**
 * 优惠券查询响应数据
 */
export interface JdCouponQueryResponseData {
  /**
   * 优惠券响应信息
   */
  data: JdCouponInfo[];
  
  /**
   * 优惠券数量
   */
  totalCount: number;
}

/**
 * 优惠券信息
 */
export interface JdCouponInfo {
  /**
   * 券领取结束时间(时间戳，毫秒)
   */
  takeEndTime: number;
  
  /**
   * 券领取开始时间(时间戳，毫秒)
   */
  takeBeginTime: number;
  
  /**
   * 领券链接
   */
  link: string;
  
  /**
   * 券使用平台 
   * 0: 全平台 
   * 1: 限平台 
   * 2: 限APP
   */
  platform: number;
  
  /**
   * 券消费限额
   */
  quota: number;
  
  /**
   * 券有效使用结束时间(时间戳，毫秒)
   */
  useEndTime: number;
  
  /**
   * 券有效使用开始时间(时间戳，毫秒)
   */
  useBeginTime: number;
  
  /**
   * 券批次ID
   */
  batchKey: string;
  
  /**
   * 券面额
   */
  discount: number;
  
  /**
   * 券剩余张数
   */
  remainNum: number;
  
  /**
   * 券有效状态
   * 1: 有效
   * 2: 失效
   */
  isBest: number;
  
  /**
   * 券总张数
   */
  num: number;
} 