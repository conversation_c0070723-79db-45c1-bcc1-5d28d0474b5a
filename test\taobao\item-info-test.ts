import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 淘宝客商品详情查询测试
 */
export class ItemInfoTest extends BaseTestClient {
	/**
	 * 测试商品详情查询
	 */
	async testItemInfo() {
		try {
			// 连接服务器
			await this.connect();

			// 调用商品详情查询工具
			const result = await this.callTool('taobao.getItemInfo', {
				num_iids: 'wK9rgDotgtDpwW09oKToxkcZUd-x0mx5JatO4yYOykJco', // 测试商品ID
				platform: 1,
			});

			console.log('商品详情查询结果:', result);
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

// 获取当前文件路径
const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 判断是否直接运行此文件
const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
	const test = new ItemInfoTest();
	test.testItemInfo().catch(console.error);
}
