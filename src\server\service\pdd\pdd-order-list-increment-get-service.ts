import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { PddApiManager } from '../../pdd/api-manager.js';
import { PddParams } from './params.js';

/**
 * 拼多多订单列表增量获取服务类
 * 处理多多进宝订单列表增量获取功能
 */
export class PddOrderListIncrementGetService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 拼多多API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: PddApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册按照更新时间段增量同步推广订单信息工具
	 * 接口名称：pdd.ddk.oauth.order.list.increment.get
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('order.list.increment.get');

		this.mcpServer.tool(
			toolName,
			'按照更新时间段增量同步推广订单信息[pdd.ddk.oauth.order.list.increment.get]',
			PddParams.orderListIncrementGet,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);

					try {
						// 执行按照更新时间段增量同步推广订单信息
						const result =
							await this.apiManager.orderListIncrementGet.getOrderList(
								params
							);

						log(
							`工具 ${toolName} 执行结果: ${JSON.stringify(
								result
							)}`
						);

						return {
							content: [
								{
									type: 'text',
									text: JSON.stringify(result, null, 2),
								},
							],
						};
					} catch (apiError) {
						logError(
							`按照更新时间段增量同步推广订单信息API调用失败: ${
								apiError instanceof Error
									? apiError.message
									: String(apiError)
							}`
						);
						throw apiError;
					}
				} catch (error) {
					const errorMessage =
						error instanceof Error ? error.message : String(error);
					logError(`工具 ${toolName} 执行出错: ${errorMessage}`);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${errorMessage}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
