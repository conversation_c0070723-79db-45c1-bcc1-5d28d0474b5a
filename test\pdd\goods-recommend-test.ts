import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 商品推荐查询测试客户端
 * 用于测试拼多多运营频道商品查询功能
 */
export class GoodsRecommendTest extends BaseTestClient {
  private channelType: number;

  /**
   * 构造函数
   * @param channelType 频道类型
   */
  constructor(channelType: number) {
    super();
    this.channelType = channelType;
  }

  /**
   * 执行商品推荐查询测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.goods.recommend", {
        channel_type: this.channelType
      });

      // 打印结果
      if (result.content?.[0]?.text) {
        console.log('运营频道商品查询测试成功!');
        console.log('查询结果:');
        console.log(result.content[0].text);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  // 使用热销榜频道进行测试
  const channelType = 5; // 5-实时热销榜
  const test = new GoodsRecommendTest(channelType);
  test.runTest().catch(console.error);
} 