import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import {
	TpwdCreateParams,
	TaobaoApiResponse,
	TpwdCreateResponseData,
} from './types.js';

/**
 * 淘宝客-公用-淘口令生成API
 * 提供生成淘口令的功能
 */
export class TpwdCreate extends BaseApi {
	/**
	 * 生成淘口令
	 * @param params 淘口令创建参数
	 * @returns 淘口令创建响应
	 */
	async createTpwd(
		params: TpwdCreateParams
	): Promise<TaobaoApiResponse<TpwdCreateResponseData>> {
		try {
			log(`执行淘口令生成: ${JSON.stringify(params)}`);

			// 直接使用参数对象调用API
			// 根据淘宝开放平台文档，只有url是必填的，其他参数无实际作用

			// 调用淘宝API
			const result = await this.callApi('taobao.tbk.tpwd.create', params);

			log(`淘口令生成结果: ${JSON.stringify(result)}`);

			// 返回响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`淘口令生成失败: ${error}`);
			throw error;
		}
	}
}
