{"name": "@liuliang520500/jd-sdk", "version": "1.0.2", "description": "京东联盟开放平台SDK", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "prepare": "npm run build", "build": "echo \"No build step required\"", "publish": "npm publish"}, "keywords": ["jd", "sdk", "api", "union"], "author": "liuliang520500", "license": "MIT", "dependencies": {"axios": "^1.6.2", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "dotenv": "^16.3.1", "form-data": "^4.0.0", "node-fetch": "^2.7.0"}, "devDependencies": {"typescript": "^5.3.3"}, "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/liuliang520500/jd-sdk.git"}, "bugs": {"url": "https://github.com/liuliang520500/jd-sdk/issues"}, "homepage": "https://github.com/liuliang520500/jd-sdk#readme"}