# @mcp/pdd-sdk

拼多多开放平台SDK，支持多多进宝API，提供完整的类型定义和示例代码。

## 功能特点

- 支持多多进宝主要API
- 完整的TypeScript类型定义
- 详细的示例代码
- 简单易用的接口设计
- 内置签名算法

## 安装

```bash
npm install @mcp/pdd-sdk
```

## 快速开始

```javascript
const { PddClient } = require('@mcp/pdd-sdk');

// 初始化客户端
const client = new PddClient({
  clientId: 'YOUR_CLIENT_ID',
  clientSecret: 'YOUR_CLIENT_SECRET',
  accessToken: 'YOUR_ACCESS_TOKEN'
});

// 使用API
async function example() {
  try {
    // 搜索商品
    const searchResult = await client.goodsSearch.search({
      keyword: '手机'
    });
    console.log(searchResult);
    
    // 生成推广链接
    const urlResult = await client.goodsPromUrlGenerate.generate({
      p_id: 'YOUR_PID',
      goods_sign_list: ['GOODS_SIGN']
    });
    console.log(urlResult);
  } catch (error) {
    console.error(error);
  }
}
```

## 支持的API

- 商品API
  - pdd.ddk.oauth.goods.detail (商品详情查询)
  - pdd.ddk.oauth.goods.pid.generate (推广位创建)
  - pdd.ddk.oauth.goods.pid.query (推广位查询)
  - pdd.ddk.oauth.goods.prom.url.generate (商品推广链接生成)
  - pdd.ddk.oauth.goods.search (商品搜索)
  - pdd.ddk.oauth.goods.zs.unit.url.generate (转链API)

- 工具API
  - pdd.ddk.oauth.member.authority.query (会员备案查询)
  - pdd.ddk.oauth.order.detail.get (订单详情查询)
  - pdd.ddk.oauth.resource.url.gen (频道推广)

## 示例代码

SDK提供了丰富的示例代码，位于`examples`目录下：

- goods-detail-test.js - 商品详情查询示例
- goods-pid-generate-test.js - 推广位创建示例
- goods-pid-query-test.js - 推广位查询示例
- goods-prom-url-generate-test.js - 商品推广链接生成示例
- goods-search-test.js - 商品搜索示例
- goods-zs-unit-url-generate-test.js - 转链示例
- member-authority-query-test.js - 会员备案查询示例
- order-detail-test.js - 订单详情查询示例
- resource-url-generate-test.js - 频道推广示例

## 环境变量

在使用示例代码时，需要在项目根目录创建`.env`文件，并配置以下环境变量：

```env
PDD_CLIENT_ID=your_client_id
PDD_CLIENT_SECRET=your_client_secret
PDD_SESSION_TOKEN=your_access_token
PDD_PID=your_pid
```

## API文档

完整的API文档请参考[拼多多开放平台文档](https://open.pinduoduo.com/application/document/api)

## 许可证

MIT License

## 作者

MCP Team (<EMAIL>)

## 问题反馈

如果您在使用过程中遇到任何问题，欢迎提交 Issue 或 Pull Request。 