/**
 * 拼多多推广位查询API测试
 */

// 导入依赖
const { PddClient } = require('../index');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 加载环境变量（从项目根目录的.env文件）
const envPath = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log(`已加载环境变量文件: ${envPath}`);
} else {
  console.warn(`警告: 环境变量文件不存在: ${envPath}`);
}

// 检查必要的环境变量是否存在
const requiredEnvVars = ['PDD_CLIENT_ID', 'PDD_CLIENT_SECRET', 'PDD_SESSION_TOKEN', 'PDD_PID'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error(`缺少必要的环境变量: ${missingEnvVars.join(', ')}`);
  console.error('请确保.env文件中包含这些变量');
  process.exit(1);
}

// 初始化客户端
const client = new PddClient({
  clientId: process.env.PDD_CLIENT_ID,
  clientSecret: process.env.PDD_CLIENT_SECRET,
  accessToken: process.env.PDD_SESSION_TOKEN,
  debug: true // 启用调试模式
});

// 测试推广位查询
async function testPidQuery() {
  try {
    console.log('======= 拼多多推广位查询测试 =======');
    console.log('客户端配置:');
    console.log('- clientId:', process.env.PDD_CLIENT_ID);
    console.log('- accessToken:', maskToken(process.env.PDD_SESSION_TOKEN));
    console.log('- PID:', process.env.PDD_PID);
    console.log('\n开始查询推广位...');
    
    // 测试方式1: 分页查询
    console.log('\n方式1: 分页查询推广位');
    const options1 = {
      page: 1,
      pageSize: 10
    };
    
    console.log('查询参数:', JSON.stringify(options1, null, 2));
    
    // 执行请求
    const result1 = await client.goodsPidQuery.query(options1);
    
    // 输出结果
    console.log('\n查询结果 (分页查询):');
    console.log(JSON.stringify(result1, null, 2));
    
    // 测试方式2: 指定PID查询
    if (process.env.PDD_PID) {
      console.log('\n方式2: 通过PID列表查询推广位');
      const options2 = {
        pidList: [process.env.PDD_PID]
      };
      
      console.log('查询参数:', JSON.stringify(options2, null, 2));
      
      // 执行请求
      const result2 = await client.goodsPidQuery.query(options2);
      
      // 输出结果
      console.log('\n查询结果 (PID列表查询):');
      console.log(JSON.stringify(result2, null, 2));
    }
    
    console.log('\n查询完成!');
  } catch (error) {
    console.error('查询失败:', error.message);
  }
}

// 辅助函数：掩码显示令牌
function maskToken(token) {
  if (!token) return 'undefined';
  return token.substr(0, 6) + '****' + token.substr(-6);
}

// 执行测试
testPidQuery(); 