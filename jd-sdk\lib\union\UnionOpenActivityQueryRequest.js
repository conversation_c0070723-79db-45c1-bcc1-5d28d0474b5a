/**
 * 联盟活动查询API
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.activity.query
 */
const { BaseApi } = require('../api');

/**
 * 联盟活动查询API请求类
 * 提供查询京东联盟推广活动信息的接口
 */
class UnionOpenActivityQueryRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.activity.query';
  }

  /**
   * 执行活动查询请求
   * @param {Object} params 请求参数
   * @param {Object} params.activityReq 活动查询请求对象
   * @param {Number} [params.activityReq.pageIndex] 页码，默认1
   * @param {Number} [params.activityReq.pageSize] 每页数量，默认20
   * @param {Number} [params.activityReq.activityType] 活动类型，1-京东活动，2-京东好券，3-京粉活动，4-京粉优选
   * @returns {Promise<Object>} 请求结果
   */
  async query(params = {}) {
    if (!params.activityReq) {
      throw new Error('activityReq不能为空');
    }

    const apiParams = {
      activityReq: this.cleanParams(params.activityReq)
    };

    return this.execute(apiParams);
  }

  /**
   * 清理参数，移除空值
   * @param {Object} params 原始参数
   * @returns {Object} 清理后的参数
   */
  cleanParams(params) {
    const cleanedParams = {};
    
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        cleanedParams[key] = params[key];
      }
    }
    
    return cleanedParams;
  }
}

module.exports = {
  UnionOpenActivityQueryRequest
}; 