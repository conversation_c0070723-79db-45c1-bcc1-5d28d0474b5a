import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 多多进宝转链接口测试客户端
 * 用于测试拼多多多多进宝转链功能
 */
export class GoodsZsUnitUrlGenerateTest extends BaseTestClient {
  private sourceUrl: string;

  /**
   * 构造函数
   * @param sourceUrl 需转链的链接
   */
  constructor(sourceUrl: string) {
    super();
    this.sourceUrl = sourceUrl;
  }

  /**
   * 执行多多进宝转链测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.goods.zs.unit.url", {
        source_url: this.sourceUrl
      });

      // 打印结果
      if (result.content?.[0]?.text) {
        console.log('多多进宝转链测试成功!');
        console.log('转链结果:');
        console.log(result.content[0].text);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const sourceUrl = 'https://p.pinduoduo.com/SqmVgD9x';
  const test = new GoodsZsUnitUrlGenerateTest(sourceUrl);
  test.runTest().catch(console.error);
} 