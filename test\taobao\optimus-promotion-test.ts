import { BaseTestClient } from './base-test-client.js';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';

// 加载环境变量
dotenv.config();

/**
 * 权益物料精选测试客户端
 */
class OptimusPromotionTestClient extends BaseTestClient {
  /**
   * 构造函数
   */
  constructor() {
    super();
  }

  /**
   * 运行测试
   */
  async runTest(): Promise<void> {
    try {
      // 连接到服务器
      await this.connect();
      
      console.log('开始测试权益物料精选工具...');
      
      // 调用权益物料精选工具，使用官方提供的有价券ID
      const result = await this.callTool('taobao.getOptimusPromotion', {
        // 使用有价券ID 37104
        promotion_id: 37116
      });
      
      // 打印结果
      console.log('权益物料精选工具测试结果:', JSON.stringify(result, null, 2));
      console.log('测试完成');
    } catch (error) {
      console.error('测试出错:', error);
      throw error;
    } finally {
      // 关闭客户端
      await this.disconnect();
    }
  }
}

// 获取当前文件路径
const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 判断是否直接运行此文件
const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const client = new OptimusPromotionTestClient();
  client.runTest().catch(error => {
    console.error('测试失败:', error);
    process.exit(1);
  });
} 