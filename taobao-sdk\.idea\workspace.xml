<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="b32b0614-4319-452c-a422-971ee1346474" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/examples/apiTest.js" beforeDir="false" afterPath="$PROJECT_DIR$/examples/apiTest.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lib/api/topClient.js" beforeDir="false" afterPath="$PROJECT_DIR$/lib/api/topClient.js" afterDir="false" />
    </list>
    <ignored path="$PROJECT_DIR$/.tmp/" />
    <ignored path="$PROJECT_DIR$/temp/" />
    <ignored path="$PROJECT_DIR$/tmp/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/examples/apiTest.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="375">
              <caret line="25" column="31" lean-forward="true" selection-start-line="25" selection-start-column="31" selection-end-line="25" selection-end-column="31" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/package.json">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="255">
              <caret line="17" column="19" selection-start-line="17" selection-start-column="19" selection-end-line="17" selection-end-column="19" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/lib/api/dingtalkClient.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="2175">
              <caret line="145" column="16" selection-start-line="145" selection-start-column="16" selection-end-line="145" selection-end-column="22" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/lib/api/topClient.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="620">
              <caret line="155" column="76" selection-start-line="155" selection-start-column="76" selection-end-line="155" selection-end-column="76" />
            </state>
          </provider>
        </entry>
      </file>
      <file pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/lib/api/network.js">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="1650">
              <caret line="110" column="12" selection-start-line="110" selection-start-column="12" selection-end-line="110" selection-end-column="12" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>invoke</find>
    </findStrings>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/lib/api/dingtalkClient.js" />
        <option value="$PROJECT_DIR$/package.json" />
        <option value="$PROJECT_DIR$/examples/apiTest.js" />
        <option value="$PROJECT_DIR$/lib/api/topClient.js" />
      </list>
    </option>
  </component>
  <component name="ProjectFrameBounds" fullScreen="true">
    <option name="x" value="104" />
    <option name="y" value="63" />
    <option name="width" value="1400" />
    <option name="height" value="987" />
  </component>
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="nodejs" type="b2602c69:ProjectViewProjectNode" />
              <item name="nodejs" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="nodejs" type="b2602c69:ProjectViewProjectNode" />
              <item name="nodejs" type="462c0819:PsiDirectoryNode" />
              <item name="examples" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="Scope" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="nodejs_package_manager_path" value="npm" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="apiTest.js" type="NodeJSConfigurationType" factoryName="Node.js" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/examples/apiTest.js" working-dir="$PROJECT_DIR$" />
    <recent_temporary>
      <list>
        <item itemvalue="Node.js.apiTest.js" />
      </list>
    </recent_temporary>
  </component>
  <component name="SvnConfiguration">
    <configuration />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="b32b0614-4319-452c-a422-971ee1346474" name="Default Changelist" comment="" />
      <created>1557822777755</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1557822777755</updated>
      <workItem from="1557822779033" duration="1613000" />
      <workItem from="1557892442149" duration="33000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="1646000" />
  </component>
  <component name="ToolWindowManager">
    <frame x="104" y="63" width="1400" height="987" extended-state="0" />
    <editor active="true" />
    <layout>
      <window_info active="true" content_ui="combo" id="Project" order="0" visible="true" weight="0.24945612" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info id="Favorites" order="2" side_tool="true" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="Find" order="1" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.3094972" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.4" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="bottom" id="TODO" order="6" />
      <window_info anchor="bottom" id="Docker" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="8" />
      <window_info anchor="bottom" id="Terminal" order="9" weight="0.32960895" />
      <window_info anchor="bottom" id="Event Log" order="10" side_tool="true" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
    </layout>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/lib/api/topClient.js</url>
          <line>74</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/lib/api/topClient.js</url>
          <line>35</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/package.json">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="255">
          <caret line="17" column="19" selection-start-line="17" selection-start-column="19" selection-end-line="17" selection-end-column="19" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/api/dingtalkClient.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2175">
          <caret line="145" column="16" selection-start-line="145" selection-start-column="16" selection-end-line="145" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/api/network.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1650">
          <caret line="110" column="12" selection-start-line="110" selection-start-column="12" selection-end-line="110" selection-end-column="12" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/lib/api/topClient.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="620">
          <caret line="155" column="76" selection-start-line="155" selection-start-column="76" selection-end-line="155" selection-end-column="76" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/examples/apiTest.js">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="375">
          <caret line="25" column="31" lean-forward="true" selection-start-line="25" selection-start-column="31" selection-end-line="25" selection-end-column="31" />
        </state>
      </provider>
    </entry>
  </component>
</project>