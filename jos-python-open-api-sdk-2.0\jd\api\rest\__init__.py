from jd.api.rest.UnionOpenCpActivityGoodsQueryRequest import UnionOpenCpActivityGoodsQueryRequest
from jd.api.rest.UnionOpenGoodsJingfenQueryRequest import UnionOpenGoodsJingfenQueryRequest
from jd.api.rest.UnionOpenOrderQueryRequest import UnionOpenOrderQueryRequest
from jd.api.rest.UnionOpenCidOrderQueryRequest import UnionOpenCidOrderQueryRequest
from jd.api.rest.KplOpenPolcenterSubscribemobileRequest import KplOpenPolcenterSubscribemobileRequest
from jd.api.rest.MfaInnerUserUnifiedAuthenticationRequest import MfaInnerUserUnifiedAuthenticationRequest
from jd.api.rest.KplOpenWareZhaoshangGoodsQueryRequest import KplOpenWareZhaoshangGoodsQueryRequest
from jd.api.rest.KplOpenPolcenterGetexpressinfoRequest import KplOpenPolcenterGetexpressinfoRequest
from jd.api.rest.UnionOpenChannelInvitecodeGetRequest import UnionOpenChannelInvitecodeGetRequest
from jd.api.rest.KplOpenGetsellpriceQueryRequest import KplOpenGetsellpriceQueryRequest
from jd.api.rest.KplOpenConfirmreceivedQueryRequest import KplOpenConfirmreceivedQueryRequest
from jd.api.rest.ProductCheckAreaLimitQueryRequest import ProductCheckAreaLimitQueryRequest
from jd.api.rest.MfaInnerValidateMsgCodeRequest import MfaInnerValidateMsgCodeRequest
from jd.api.rest.MfaInnerEliminateRiskRequest import MfaInnerEliminateRiskRequest
from jd.api.rest.SearchWareRequest import SearchWareRequest
from jd.api.rest.MfaInnerSendCodeToMobileRequest import MfaInnerSendCodeToMobileRequest
from jd.api.rest.TradeDqgPlanListQueryRequest import TradeDqgPlanListQueryRequest
from jd.api.rest.UserGetUserInfoByOpenIdRequest import UserGetUserInfoByOpenIdRequest
from jd.api.rest.UnionOpenPromotionByunionidGetRequest import UnionOpenPromotionByunionidGetRequest
from jd.api.rest.UnionOpenPositionQueryRequest import UnionOpenPositionQueryRequest
from jd.api.rest.YsdkMemberApplyJsfServiceSaveMemberAndProtocolInfoRequest import YsdkMemberApplyJsfServiceSaveMemberAndProtocolInfoRequest
from jd.api.rest.OpenBlueInvoiceUploadRequest import OpenBlueInvoiceUploadRequest
from jd.api.rest.UnionOpenPositionCreateRequest import UnionOpenPositionCreateRequest
from jd.api.rest.UnionOpenGoodsQueryRequest import UnionOpenGoodsQueryRequest
from jd.api.rest.UnionOpenGoodsPromotiongoodsinfoQueryRequest import UnionOpenGoodsPromotiongoodsinfoQueryRequest
from jd.api.rest.UnionOpenPromotionCommonGetRequest import UnionOpenPromotionCommonGetRequest
from jd.api.rest.UnionOpenPromotionBysubunionidGetRequest import UnionOpenPromotionBysubunionidGetRequest
from jd.api.rest.ImgzonePictureUploadRequest import ImgzonePictureUploadRequest
from jd.api.rest.UnionOpenCouponQueryRequest import UnionOpenCouponQueryRequest
from jd.api.rest.UnionOpenExchangeMediaEffectDataSyncRequest import UnionOpenExchangeMediaEffectDataSyncRequest
from jd.api.rest.UnionOpenExchangeMediaOrderSyncRequest import UnionOpenExchangeMediaOrderSyncRequest
from jd.api.rest.UnionOpenExchangeMediaDtorderSyncRequest import UnionOpenExchangeMediaDtorderSyncRequest
from jd.api.rest.PriceSellPriceGetRequest import PriceSellPriceGetRequest
from jd.api.rest.ProductStateQueryRequest import ProductStateQueryRequest
from jd.api.rest.ProductDetailQueryRequest import ProductDetailQueryRequest
from jd.api.rest.UnionOpenExchangeMediaEffectDataQueryRequest import UnionOpenExchangeMediaEffectDataQueryRequest
from jd.api.rest.KplOpenWfpJmiwareAddWareRequest import KplOpenWfpJmiwareAddWareRequest
from jd.api.rest.KplOpenWfpJmiwareUpdateWareStatusRequest import KplOpenWfpJmiwareUpdateWareStatusRequest
from jd.api.rest.KplOpenWfpVscLegalOrderRequest import KplOpenWfpVscLegalOrderRequest
from jd.api.rest.JsfXbXBProductServiceQueryProductsRequest import JsfXbXBProductServiceQueryProductsRequest
from jd.api.rest.KplOpenWfpJmiwareEditWareRequest import KplOpenWfpJmiwareEditWareRequest
from jd.api.rest.KplOpenAlphaHandleventRequest import KplOpenAlphaHandleventRequest
from jd.api.rest.UnionOpenChannelRelationGetRequest import UnionOpenChannelRelationGetRequest
from jd.api.rest.UnionOpenOrderAgentQueryRequest import UnionOpenOrderAgentQueryRequest
from jd.api.rest.UnionOpenCpActivityInfoQueryRequest import UnionOpenCpActivityInfoQueryRequest
from jd.api.rest.KplOpenYaoMultipriceUpdateRequest import KplOpenYaoMultipriceUpdateRequest
from jd.api.rest.KplOpenYaoShelfstateBatchupdateRequest import KplOpenYaoShelfstateBatchupdateRequest
from jd.api.rest.UnionOpenOrderRowQueryRequest import UnionOpenOrderRowQueryRequest
from jd.api.rest.KplOpenWfpVscVerifiedNotifyRequest import KplOpenWfpVscVerifiedNotifyRequest
from jd.api.rest.KplOpenWfpVscMakeCertCallbackRequest import KplOpenWfpVscMakeCertCallbackRequest
from jd.api.rest.KplOpenAlphaConfigAddrsgetRequest import KplOpenAlphaConfigAddrsgetRequest
from jd.api.rest.KplOpenWfpVscPreviewRequest import KplOpenWfpVscPreviewRequest
from jd.api.rest.KplOpenWfpVscVerifyRequest import KplOpenWfpVscVerifyRequest
from jd.api.rest.UnionOpenGoodsLinkQueryRequest import UnionOpenGoodsLinkQueryRequest
from jd.api.rest.InvoiceWaybillRequest import InvoiceWaybillRequest
from jd.api.rest.OrderJdOrderIDByThridOrderIDQueryRequest import OrderJdOrderIDByThridOrderIDQueryRequest
from jd.api.rest.UnionOpenCidOrderSyncRequest import UnionOpenCidOrderSyncRequest
from jd.api.rest.KplOpenAftersaleOrderidRequest import KplOpenAftersaleOrderidRequest
from jd.api.rest.KplOpenRegularPlanCompletedorderRequest import KplOpenRegularPlanCompletedorderRequest
from jd.api.rest.KplOpenSkuQueryinfoRequest import KplOpenSkuQueryinfoRequest
from jd.api.rest.KplOpenYaoOrdersyncListRequest import KplOpenYaoOrdersyncListRequest
from jd.api.rest.MessageGetRequest import MessageGetRequest
from jd.api.rest.AfterSaleServiceListPageQueryRequest import AfterSaleServiceListPageQueryRequest
from jd.api.rest.UnionOpenStatisticsRedpacketQueryRequest import UnionOpenStatisticsRedpacketQueryRequest
from jd.api.rest.BizProductGetSimilarSkuRequest import BizProductGetSimilarSkuRequest
from jd.api.rest.KplOpenXdpSoaGetSessionIdRequest import KplOpenXdpSoaGetSessionIdRequest
from jd.api.rest.ProductDetailQueryRequest import ProductDetailQueryRequest
from jd.api.rest.UnionOpenActivityRecommendQueryRequest import UnionOpenActivityRecommendQueryRequest
from jd.api.rest.UnionOpenGoodsSnapshopQueryRequest import UnionOpenGoodsSnapshopQueryRequest
from jd.api.rest.UnionOpenCouponGiftStopRequest import UnionOpenCouponGiftStopRequest
from jd.api.rest.NewWareBaseproductGetRequest import NewWareBaseproductGetRequest
from jd.api.rest.UnionOpenCidTaskGetRequest import UnionOpenCidTaskGetRequest
from jd.api.rest.KeplerYaoWareStockUpdateRequest import KeplerYaoWareStockUpdateRequest
from jd.api.rest.KplOpenYaoOrdersyncUpdatestateRequest import KplOpenYaoOrdersyncUpdatestateRequest
from jd.api.rest.NewWareMobilebigfieldGetRequest import NewWareMobilebigfieldGetRequest
from jd.api.rest.KplOpenYaoPropertyUpdateRequest import KplOpenYaoPropertyUpdateRequest
from jd.api.rest.KeplerYaoWarePriceUpdateRequest import KeplerYaoWarePriceUpdateRequest
from jd.api.rest.ProductSkuQueryRequest import ProductSkuQueryRequest
from jd.api.rest.KplOpenRegularPlanQueryplanlistnewRequest import KplOpenRegularPlanQueryplanlistnewRequest
from jd.api.rest.OpenOrderGetorderlistRequest import OpenOrderGetorderlistRequest
from jd.api.rest.UnionOpenCouponGiftGetRequest import UnionOpenCouponGiftGetRequest
from jd.api.rest.UnionOpenStatisticsGiftcouponQueryRequest import UnionOpenStatisticsGiftcouponQueryRequest
from jd.api.rest.ProductCommentSummarysQueryRequest import ProductCommentSummarysQueryRequest
from jd.api.rest.AfterSaleWareReturnJdCompQueryRequest import AfterSaleWareReturnJdCompQueryRequest
from jd.api.rest.OrderJdOrderQueryRequest import OrderJdOrderQueryRequest
from jd.api.rest.BizProductGetcategoryRequest import BizProductGetcategoryRequest
from jd.api.rest.UnionOpenExtraRedpacketReceiveValidateRequest import UnionOpenExtraRedpacketReceiveValidateRequest
from jd.api.rest.KplOpenMigumusicChannelkeyRequest import KplOpenMigumusicChannelkeyRequest
from jd.api.rest.KplOpenPolcenterSubscribeorderRequest import KplOpenPolcenterSubscribeorderRequest
from jd.api.rest.UnionOpenPromotionParseRequest import UnionOpenPromotionParseRequest
from jd.api.rest.PriceBalanceQueryRequest import PriceBalanceQueryRequest
from jd.api.rest.KplOpenPolcenterGetexttraceRequest import KplOpenPolcenterGetexttraceRequest
from jd.api.rest.KplOpenPolcenterUnsubscribemobileRequest import KplOpenPolcenterUnsubscribemobileRequest
from jd.api.rest.UnionOpenGoodsCombinationpageGetRequest import UnionOpenGoodsCombinationpageGetRequest
from jd.api.rest.AddressAllProvincesQueryRequest import AddressAllProvincesQueryRequest
from jd.api.rest.DetectionImagesRedLineDetectBatchRequest import DetectionImagesRedLineDetectBatchRequest
from jd.api.rest.UnionOpenStatisticsPromotionQueryRequest import UnionOpenStatisticsPromotionQueryRequest
from jd.api.rest.PopOrderGetmobilelistRequest import PopOrderGetmobilelistRequest
from jd.api.rest.OrderOrderTrackQueryRequest import OrderOrderTrackQueryRequest
from jd.api.rest.ProductSkuGiftQueryRequest import ProductSkuGiftQueryRequest
from jd.api.rest.UnionOpenGoodsBigfieldQueryRequest import UnionOpenGoodsBigfieldQueryRequest
from jd.api.rest.KplOpenYaoOrdersyncGetorderlistRequest import KplOpenYaoOrdersyncGetorderlistRequest
from jd.api.rest.OpenOrangeOrderQueryRequest import OpenOrangeOrderQueryRequest
from jd.api.rest.ProductSkuImageQueryRequest import ProductSkuImageQueryRequest
from jd.api.rest.UnionOpenShPromotionGetRequest import UnionOpenShPromotionGetRequest
from jd.api.rest.KplOpenAlphashoppingconfSaveinfoRequest import KplOpenAlphashoppingconfSaveinfoRequest
from jd.api.rest.KeplerSkuProductServiceRequest import KeplerSkuProductServiceRequest
from jd.api.rest.ProductSkuCheckRequest import ProductSkuCheckRequest
from jd.api.rest.AfterSaleCustomerExpectCompQueryRequest import AfterSaleCustomerExpectCompQueryRequest
from jd.api.rest.BizProductGetSkuByPageRequest import BizProductGetSkuByPageRequest
from jd.api.rest.OrderDoPayRequest import OrderDoPayRequest
from jd.api.rest.KplOpenPolcenterUnsubscribeorderRequest import KplOpenPolcenterUnsubscribeorderRequest
from jd.api.rest.OrderCheckRefuseOrderQueryRequest import OrderCheckRefuseOrderQueryRequest
from jd.api.rest.AfterSaleAfsApplyCreateRequest import AfterSaleAfsApplyCreateRequest
from jd.api.rest.OrderCheckDlokOrderQueryRequest import OrderCheckDlokOrderQueryRequest
from jd.api.rest.KplOpenNosecretpaySavetokenRequest import KplOpenNosecretpaySavetokenRequest
from jd.api.rest.KplOpenAreaCheckareaRequest import KplOpenAreaCheckareaRequest
from jd.api.rest.BizProductGetcategorysRequest import BizProductGetcategorysRequest
from jd.api.rest.UnionOpenExchangeDeviceSupplyRequest import UnionOpenExchangeDeviceSupplyRequest
from jd.api.rest.UnionOpenCpChannelDeleteRequest import UnionOpenCpChannelDeleteRequest
from jd.api.rest.KplOpenSeckillKeplerqbRequest import KplOpenSeckillKeplerqbRequest
from jd.api.rest.UnionOpenExchangeDeviceFileQueryRequest import UnionOpenExchangeDeviceFileQueryRequest
from jd.api.rest.UnionOpenCpChannelDefaultGetRequest import UnionOpenCpChannelDefaultGetRequest
from jd.api.rest.UnionOpenCpChannelQueryRequest import UnionOpenCpChannelQueryRequest
from jd.api.rest.AfterSaleSendSkuUpdateRequest import AfterSaleSendSkuUpdateRequest
from jd.api.rest.KplOpenKeplerQuerySkudescriptionRequest import KplOpenKeplerQuerySkudescriptionRequest
from jd.api.rest.OrderOccupyStockConfirmRequest import OrderOccupyStockConfirmRequest
from jd.api.rest.OrderCancelorderRequest import OrderCancelorderRequest
from jd.api.rest.KplOpenNbdMedicineGetricendtockRequest import KplOpenNbdMedicineGetricendtockRequest
from jd.api.rest.UnionOpenCpActivityCancelQueryRequest import UnionOpenCpActivityCancelQueryRequest
from jd.api.rest.MessageDelRequest import MessageDelRequest
from jd.api.rest.UnionOpenCpChannelGetRequest import UnionOpenCpChannelGetRequest
from jd.api.rest.UnionOpenStatisticsActivityBonusQueryRequest import UnionOpenStatisticsActivityBonusQueryRequest
from jd.api.rest.ProductYanbaoSkuQueryRequest import ProductYanbaoSkuQueryRequest
from jd.api.rest.OrderFreightGetRequest import OrderFreightGetRequest
from jd.api.rest.OrderPromiseCalendarGetRequest import OrderPromiseCalendarGetRequest
from jd.api.rest.StockForListBatgetRequest import StockForListBatgetRequest
from jd.api.rest.JosSecretApiReportGetRequest import JosSecretApiReportGetRequest
from jd.api.rest.UnionOpenOrderRowSupplyQueryRequest import UnionOpenOrderRowSupplyQueryRequest
from jd.api.rest.KplOpenInvoiceQuerythrapplynoRequest import KplOpenInvoiceQuerythrapplynoRequest
from jd.api.rest.UnionOpenStatisticsCpActivityQueryRequest import UnionOpenStatisticsCpActivityQueryRequest
from jd.api.rest.AfterSaleServiceDetailInfoQueryRequest import AfterSaleServiceDetailInfoQueryRequest
from jd.api.rest.KplOpenGetnewstockbyidQueryRequest import KplOpenGetnewstockbyidQueryRequest
from jd.api.rest.KplOpenRegularPlanShipmentorderRequest import KplOpenRegularPlanShipmentorderRequest
from jd.api.rest.KplOpenSelectjdorderQueryRequest import KplOpenSelectjdorderQueryRequest
from jd.api.rest.UnionOpenCpActivityQueryRequest import UnionOpenCpActivityQueryRequest
from jd.api.rest.PriceJincaiCreditQueryRequest import PriceJincaiCreditQueryRequest
from jd.api.rest.UnionOpenStatisticsRedpacketAgentQueryRequest import UnionOpenStatisticsRedpacketAgentQueryRequest
from jd.api.rest.OrderWaybilltrackSearchRequest import OrderWaybilltrackSearchRequest
from jd.api.rest.ProductIsCodQueryRequest import ProductIsCodQueryRequest
from jd.api.rest.AddressTownsByCountyIdQueryRequest import AddressTownsByCountyIdQueryRequest
from jd.api.rest.AddressCitysByProvinceIdQueryRequest import AddressCitysByProvinceIdQueryRequest
from jd.api.rest.MfaUserUnifiedAuthenticationRequest import MfaUserUnifiedAuthenticationRequest
from jd.api.rest.OrderCheckNewOrderQueryRequest import OrderCheckNewOrderQueryRequest
from jd.api.rest.AddressCountysByCityIdQueryRequest import AddressCountysByCityIdQueryRequest
from jd.api.rest.YjcFgcGetOrderDetailRequest import YjcFgcGetOrderDetailRequest
from jd.api.rest.PlaceOrderRequest import PlaceOrderRequest
from jd.api.rest.OpenOrderSplitDeliverNewRequest import OpenOrderSplitDeliverNewRequest
from jd.api.rest.KplOpenShenzhouConfigGetRequest import KplOpenShenzhouConfigGetRequest
from jd.api.rest.UnionOpenCpPersonalInfoQueryRequest import UnionOpenCpPersonalInfoQueryRequest
from jd.api.rest.UnionOpenGoodsMaterialQueryRequest import UnionOpenGoodsMaterialQueryRequest
from jd.api.rest.UnionOpenPromotionIntelligenceQueryRequest import UnionOpenPromotionIntelligenceQueryRequest
from jd.api.rest.UnionOpenChannelRelationQueryRequest import UnionOpenChannelRelationQueryRequest
from jd.api.rest.JdUnionOpenOrderRowSupplyQueryRequest import JdUnionOpenOrderRowSupplyQueryRequest
from jd.api.rest.UnionOpenCpActivityDeleteRequest import UnionOpenCpActivityDeleteRequest
from jd.api.rest.UnionOpenPromotionToolsIntelligenceQueryRequest import UnionOpenPromotionToolsIntelligenceQueryRequest
from jd.api.rest.OrderUniteSubmitRequest import OrderUniteSubmitRequest
from jd.api.rest.UnionOpenCpActivityCancelValidateRequest import UnionOpenCpActivityCancelValidateRequest
from jd.api.rest.PriceBalanceGetRequest import PriceBalanceGetRequest
from jd.api.rest.BizAreaGetJDAddressFromAddressRequest import BizAreaGetJDAddressFromAddressRequest
from jd.api.rest.UnionOpenActivityQueryRequest import UnionOpenActivityQueryRequest
from jd.api.rest.OpenRedInvoiceUploadRequest import OpenRedInvoiceUploadRequest
from jd.api.rest.UnionOpenActivityBonusQueryRequest import UnionOpenActivityBonusQueryRequest
from jd.api.rest.OpenGetSplitOrdersRequest import OpenGetSplitOrdersRequest
from jd.api.rest.UnionDoubanBookServiceQueryBookUrlListRequest import UnionDoubanBookServiceQueryBookUrlListRequest
from jd.api.rest.UnionOpenOauthTestQueryRequest import UnionOpenOauthTestQueryRequest
from jd.api.rest.KplOpenXimalayaGetresourceRequest import KplOpenXimalayaGetresourceRequest
from jd.api.rest.UnionOpenExchangeDeviceSyncRequest import UnionOpenExchangeDeviceSyncRequest
from jd.api.rest.H5OpenMiniGetMiniProgramSchemeRequest import H5OpenMiniGetMiniProgramSchemeRequest
from jd.api.rest.UnionOpenOrderBonusQueryRequest import UnionOpenOrderBonusQueryRequest
from jd.api.rest.PopOrderEncryptMobileNumRequest import PopOrderEncryptMobileNumRequest
from jd.api.rest.KeplerSettledAddressGetareabymehodnameRequest import KeplerSettledAddressGetareabymehodnameRequest
from jd.api.rest.StockOpenApiUpdateVenderStockNumRequest import StockOpenApiUpdateVenderStockNumRequest
from jd.api.rest.SkuReadFindSkuByIdRequest import SkuReadFindSkuByIdRequest
from jd.api.rest.JosMasterKeyGetRequest import JosMasterKeyGetRequest
from jd.api.rest.KplOpenWfpVscQueryCertListRequest import KplOpenWfpVscQueryCertListRequest
from jd.api.rest.UnionOpenUserRegisterValidateRequest import UnionOpenUserRegisterValidateRequest
from jd.api.rest.KplOpenAlphaHandleRequest import KplOpenAlphaHandleRequest
from jd.api.rest.UnionOpenPromotionAppletGetRequest import UnionOpenPromotionAppletGetRequest
from jd.api.rest.UnionOpenCpActivityGetRequest import UnionOpenCpActivityGetRequest
from jd.api.rest.OpenOrderUpdateOrderStateNewRequest import OpenOrderUpdateOrderStateNewRequest
from jd.api.rest.YjcFgcGetOrderListRequest import YjcFgcGetOrderListRequest
from jd.api.rest.YsdkMemberApplyJsfServiceQueryMemberStatusRequest import YsdkMemberApplyJsfServiceQueryMemberStatusRequest
from jd.api.rest.UnionOpenCpActivityGoodsValidateRequest import UnionOpenCpActivityGoodsValidateRequest
from jd.api.rest.StockStateAreaStockStateExportRequest import StockStateAreaStockStateExportRequest
from jd.api.rest.UnionOpenGoodsCombinationQueryRequest import UnionOpenGoodsCombinationQueryRequest
from jd.api.rest.WareBookbigfieldGetRequest import WareBookbigfieldGetRequest
from jd.api.rest.WareProductbigfieldGetRequest import WareProductbigfieldGetRequest
from jd.api.rest.UnionOpenGoodsSeckillQueryRequest import UnionOpenGoodsSeckillQueryRequest
from jd.api.rest.UnionOpenUserPidGetRequest import UnionOpenUserPidGetRequest
from jd.api.rest.UnionOpenStatisticsRedpacketPidQueryRequest import UnionOpenStatisticsRedpacketPidQueryRequest
from jd.api.rest.NewWareMobilebigfieldGetRequest import NewWareMobilebigfieldGetRequest
from jd.api.rest.WareProductimageGetRequest import WareProductimageGetRequest
from jd.api.rest.NewWareBaseproductGetRequest import NewWareBaseproductGetRequest
from jd.api.rest.UnionOpenCategoryGoodsGetRequest import UnionOpenCategoryGoodsGetRequest
from jd.api.rest.WareBasebookGetRequest import WareBasebookGetRequest
from jd.api.rest.InvoiceSubmitRequest import InvoiceSubmitRequest
from jd.api.rest.InvoiceQueryRequest import InvoiceQueryRequest
from jd.api.rest.AfterSaleAuditCancelQueryRequest import AfterSaleAuditCancelQueryRequest
from jd.api.rest.AfterSaleAvailableNumberCompQueryRequest import AfterSaleAvailableNumberCompQueryRequest
from jd.api.rest.JosVoucherInfoGetRequest import JosVoucherInfoGetRequest
from jd.api.rest.UnionOpenGoodsItemidGetRequest import UnionOpenGoodsItemidGetRequest
from jd.api.rest.UnionOpenGoodsRankQueryRequest import UnionOpenGoodsRankQueryRequest
from jd.api.rest.WareProductsortGetRequest import WareProductsortGetRequest
from jd.api.rest.NewWareSameproductskuidsQueryRequest import NewWareSameproductskuidsQueryRequest
from jd.api.rest.NewWareProductsortattGetRequest import NewWareProductsortattGetRequest
from jd.api.rest.NewWareProductsafedaysGetRequest import NewWareProductsafedaysGetRequest
from jd.api.rest.NewWareAttributeGroupsQueryRequest import NewWareAttributeGroupsQueryRequest
from jd.api.rest.NewWareAttributesQueryRequest import NewWareAttributesQueryRequest
from jd.api.rest.NewWareAttributeValuesQueryRequest import NewWareAttributeValuesQueryRequest
