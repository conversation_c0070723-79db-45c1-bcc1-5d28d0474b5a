{"name": "@liuliang520500/pdd-sdk", "version": "1.0.1", "description": "拼多多开放平台SDK，支持多多进宝API", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "test:goods-detail": "node examples/goods-detail-test.js", "test:pid-generate": "node examples/goods-pid-generate-test.js", "test:pid-query": "node examples/goods-pid-query-test.js", "test:prom-url": "node examples/goods-prom-url-generate-test.js", "test:goods-search": "node examples/goods-search-test.js", "test:zs-unit-url": "node examples/goods-zs-unit-url-generate-test.js", "test:member-authority": "node examples/member-authority-query-test.js", "test:order-detail": "node examples/order-detail-test.js", "test:resource-url": "node examples/resource-url-generate-test.js"}, "keywords": ["pindu<PERSON><PERSON>", "pdd", "sdk", "api", "多多进宝", "拼多多", "电商", "推广"], "author": {"name": "vx:liuliangzheng", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/mcp/pdd-sdk"}, "bugs": {"url": "https://github.com/mcp/pdd-sdk/issues"}, "homepage": "https://github.com/mcp/pdd-sdk#readme", "license": "MIT", "engines": {"node": ">=8.0.0"}, "dependencies": {"axios": "^1.6.2", "crypto-js": "^4.1.1", "dotenv": "^16.3.1"}, "files": ["index.js", "index.d.ts", "lib", "examples"]}