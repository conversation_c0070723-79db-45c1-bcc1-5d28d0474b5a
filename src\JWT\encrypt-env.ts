#!/usr/bin/env node

/**
 * 环境变量加密命令行工具
 * 用于加密.env文件并生成JWT令牌
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { encryptEnvFile } from './env-encryptor.js';

// 主函数
async function main() {
	try {
		// 解析命令行参数
		const args = process.argv.slice(2);

		// 显示帮助信息
		if (args.includes('-h') || args.includes('--help')) {
			showHelp();
			return;
		}

		// 获取参数
		const envFilePath =
			getArgValue(args, '-f', '--file') ||
			path.resolve(process.cwd(), '.env');
		const secret =
			getArgValue(args, '-s', '--secret') || 'mcp-default-secret-key';
		const expiresIn = getArgValue(args, '-e', '--expires') || '30d';
		const outputFile = getArgValue(args, '-o', '--output');

		// 检查.env文件是否存在
		if (!fs.existsSync(envFilePath)) {
			// 注意：这里保留console.error，因为这是命令行工具，需要在终端显示错误
			console.error(`错误: 环境变量文件不存在: ${envFilePath}`);
			process.exit(1);
		}

		// 注意：这里保留console.log，因为这是命令行工具，需要在终端显示结果
		console.log(`正在加密环境变量文件: ${envFilePath}`);
		console.log(`使用密钥: ${secret}`);
		console.log(`过期时间: ${expiresIn}`);

		// 加密环境变量
		const token = await encryptEnvFile(envFilePath, secret, expiresIn);

		// 输出结果
		console.log('\n环境变量加密成功!');

		if (outputFile) {
			// 写入文件
			fs.writeFileSync(outputFile, token);
			console.log(`JWT令牌已保存到文件: ${outputFile}`);
		} else {
			// 打印到控制台
			console.log('JWT令牌:');
			console.log(token);
		}
	} catch (error) {
		// 注意：这里保留console.error，因为这是命令行工具，需要在终端显示错误
		console.error(
			'环境变量加密失败:',
			error instanceof Error ? error.message : String(error)
		);
		process.exit(1);
	}
}

// 显示帮助信息
function showHelp() {
	// 注意：这里保留console.log，因为这是命令行工具，需要在终端显示结果
	console.log('环境变量加密工具');
	console.log('用法: encrypt-env [选项]');
	console.log('\n选项:');
	console.log('  -f, --file <path>     环境变量文件路径 (默认: ./.env)');
	console.log(
		'  -s, --secret <key>    JWT密钥 (默认: mcp-default-secret-key)'
	);
	console.log('  -e, --expires <time>  过期时间 (默认: 30d)');
	console.log('  -o, --output <path>   输出文件路径 (默认: 打印到控制台)');
	console.log('  -h, --help            显示帮助信息');
	console.log('\n示例:');
	console.log('  encrypt-env -f .env -s my-secret-key -e 60d -o env.jwt');
}

// 获取参数值
function getArgValue(
	args: string[],
	shortFlag: string,
	longFlag: string
): string | undefined {
	const shortIndex = args.indexOf(shortFlag);
	const longIndex = args.indexOf(longFlag);

	if (shortIndex !== -1 && shortIndex + 1 < args.length) {
		return args[shortIndex + 1];
	}

	if (longIndex !== -1 && longIndex + 1 < args.length) {
		return args[longIndex + 1];
	}

	return undefined;
}

// 检查是否直接运行此文件
const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件，则执行主函数
if (isDirectlyExecuted) {
	main().catch((error) => {
		// 注意：这里保留console.error，因为这是命令行工具，需要在终端显示错误
		console.error('未处理的错误:', error);
		process.exit(1);
	});
}
