import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 推广位查询测试客户端
 * 用于测试拼多多推广位查询功能
 */
export class GoodsPidQueryTest extends BaseTestClient {
  private page: number;
  private pageSize: number;

  /**
   * 构造函数
   * @param page 页码
   * @param pageSize 每页数量
   */
  constructor(page: number, pageSize: number) {
    super();
    this.page = page;
    this.pageSize = pageSize;
  }

  /**
   * 执行推广位查询测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.goods.pid.query", {
        page: this.page,
        page_size: this.pageSize
      });

      // 验证响应
      if (result.content?.[0]?.text) {
        const response = JSON.parse(result.content[0].text);
        console.log('推广位查询测试成功!');
        console.log('总数量:', response.p_id_query_response.total_count);
        console.log('推广位列表:');
        response.p_id_query_response.p_id_list.forEach((item: any) => {
          console.log(`- PID: ${item.p_id}`);
          console.log(`  名称: ${item.pid_name}`);
          console.log(`  创建时间: ${new Date(item.create_time * 1000).toLocaleString()}`);
        });
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const test = new GoodsPidQueryTest(1, 20);
  test.runTest().catch(console.error);
} 