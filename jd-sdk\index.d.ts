/**
 * 京东联盟开放平台SDK类型定义
 */

export interface JdClientConfig {
    /**
     * 应用Key
     */
    appKey?: string;
    /**
     * 应用密钥
     */
    secretKey?: string;
    /**
     * 访问令牌
     */
    accessToken?: string;
  }
  
  export interface JdApiErrorData {
    /**
     * 错误代码
     */
    code: string;
    /**
     * 错误消息
     */
    message: string;
    /**
     * 子错误代码
     */
    subCode?: string;
    /**
     * 子错误消息
     */
    subMessage?: string;
  }
  
  export class JdApiError extends Error {
    /**
     * 错误代码
     */
    code: string;
    /**
     * 子错误代码
     */
    subCode?: string;
    /**
     * 子错误消息
     */
    subMessage?: string;
    
    constructor(code: string, message: string, subCode?: string, subMessage?: string);
  }
  
  /**
   * API基类
   */
  export class BaseApi {
    /**
     * 构造函数
     */
    constructor(config: JdClientConfig);
    
    /**
     * 执行API请求
     */
    execute(params: any, options?: any): Promise<any>;
  }
  
  /**
   * 推广位查询参数
   */
  export interface PositionQueryReq {
    /**
     * 联盟ID
     */
    unionId: number;
    /**
     * 关键词
     */
    key?: string;
    /**
     * 联盟推广位类型，1：cps推广位 2：cpc推广位
     */
    unionType?: number;
    /**
     * 页码，上限100
     */
    pageIndex?: number;
    /**
     * 每页条数，上限100
     */
    pageSize?: number;
  }
  
  /**
   * 推广位查询API
   */
  export class UnionOpenPositionQueryRequest extends BaseApi {
    /**
     * 查询推广位
     */
    query(positionReq: PositionQueryReq): Promise<any>;
  }
  
  /**
   * 推广位创建参数
   */
  export interface PositionCreateReq {
    /**
     * 联盟ID
     */
    unionId: number;
    /**
     * 授权key
     */
    key: string;
    /**
     * 站点类型 1网站推广位2.APP推广位3.社交媒体推广位4.聊天工具推广位5.二维码推广
     */
    type: number;
    /**
     * 联盟推广位类型,1直接转换型推广位2.待定型推广位
     */
    unionType: number;
    /**
     * 推广位名称集合，英文,分割；上限50
     */
    spaceNameList: string[];
    /**
     * 站点ID，仅限于创建推广位的时候使用
     */
    siteId?: number;
  }
  
  /**
   * 推广位创建API
   */
  export class UnionOpenPositionCreateRequest extends BaseApi {
    /**
     * 创建推广位
     */
    create(positionReq: PositionCreateReq): Promise<any>;
  }
  
  /**
   * 推广链接参数
   */
  export interface PromotionCodeReq {
    /**
     * 推广物料ID
     */
    materialId: string;
    /**
     * 站点ID
     */
    siteId?: number;
    /**
     * 推广位ID
     */
    positionId?: number;
    /**
     * 子联盟ID
     */
    subUnionId?: string;
    /**
     * 联盟ID
     */
    unionId: number;
    /**
     * 自定义扩展参数
     */
    ext1?: string;
    /**
     * 链接类型 1.长链接 2.短链接 3.长链接+短链接
     */
    chainType?: number;
    /**
     * 优惠券领取链接
     */
    couponUrl?: string;
    /**
     * 礼金批次ID
     */
    giftCouponKey?: string;
    /**
     * 渠道ID
     */
    channelId?: number;
    /**
     * 请求模式 1.商品推广链接2.商店推广链接
     */
    command?: number;
    /**
     * 微信小程序appid
     */
    weChatType?: number;
  }
  
  /**
   * 推广链接生成API
   */
  export class UnionOpenPromotionByUnionidGetRequest extends BaseApi {
    /**
     * 生成推广链接
     */
    get(params: { promotionCodeReq: PromotionCodeReq }): Promise<any>;
  }
  
  /**
   * 京东开放平台客户端
   */
  export class JdClient {
    /**
     * 创建京东客户端实例
     */
    constructor(config?: JdClientConfig);
    
    /**
     * 创建API实例
     */
    createAPI<T extends BaseApi>(ApiClass: new (config: JdClientConfig) => T): T;
    
    /**
     * 获取推广位查询API
     * @deprecated 推荐使用execute方法
     */
    position(): UnionOpenPositionQueryRequest;

    /**
     * 获取推广位创建API
     * @deprecated 推荐使用execute方法
     */
    positionCreate(): UnionOpenPositionCreateRequest;

    /**
     * 获取推广链接API
     * @deprecated 推荐使用execute方法
     */
    promotion(): UnionOpenPromotionByUnionidGetRequest;

    /**
     * 获取商品查询API
     * @deprecated 推荐使用execute方法
     */
    goods(): any;

    /**
     * 获取优惠券查询API
     * @deprecated 推荐使用execute方法
     */
    coupon(): any;
    
    /**
     * 获取订单查询API
     * @deprecated 推荐使用execute方法
     */
    order(): any;
    
    /**
     * 获取订单行查询API
     * @deprecated 推荐使用execute方法
     */
    orderRow(): any;
    
    /**
     * 获取京粉精选商品查询API
     * @deprecated 推荐使用execute方法
     */
    goodsJingfen(): any;
    
    /**
     * 获取商品类目查询API
     * @deprecated 推荐使用execute方法
     */
    category(): any;
    
    /**
     * 获取活动查询API
     * @deprecated 推荐使用execute方法
     */
    activity(): any;
    
    /**
     * 执行API调用
     * 
     * 使用指定的API名称执行调用，参数会根据API类型自动包装
     * 支持的API名称包括：
     * - jd.union.open.position.query (推广位查询)
     * - jd.union.open.position.create (推广位创建)
     * - jd.union.open.promotion.byunionid.get (转链接)
     * - jd.union.open.goods.query (商品查询)
     * - jd.union.open.coupon.query (优惠券查询)
     * - jd.union.open.order.query (订单查询)
     * - jd.union.open.order.row.query (订单行查询)
     * - jd.union.open.goods.jingfen.query (京粉精选商品查询)
     * - jd.union.open.category.goods.get (类目商品查询)
     * - jd.union.open.activity.query (活动查询)
     * 
     * @param apiName API名称
     * @param params API参数，会根据API类型自动包装
     * @param options 请求选项
     * @returns API响应
     */
    execute(apiName: string, params: any, options?: any): Promise<any>;
    
    /**
     * 预处理API参数
     * @private
     */
    private preprocessApiParams(apiName: string, params: any): any;
  }

  export const ApiTypes: {
    UnionOpenPositionQueryRequest: typeof UnionOpenPositionQueryRequest;
    UnionOpenPositionCreateRequest: typeof UnionOpenPositionCreateRequest;
    UnionOpenPromotionByUnionidGetRequest: typeof UnionOpenPromotionByUnionidGetRequest;
    UnionOpenGoodsQueryRequest: any;
    UnionOpenCouponQueryRequest: any;
    UnionOpenOrderQueryRequest: any;
    UnionOpenOrderRowQueryRequest: any;
    UnionOpenGoodsJingfenQueryRequest: any;
    UnionOpenCategoryGoodsGetRequest: any;
    UnionOpenActivityQueryRequest: any;
  };