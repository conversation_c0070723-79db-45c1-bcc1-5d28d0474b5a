/**
 * 拼多多推广位创建API
 * 接口名称：pdd.ddk.oauth.goods.pid.generate
 */

const BaseApi = require('../core/base-api');

class GoodsPidGenerate extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 创建多多进宝推广位
   * @param {Object} options 创建参数
   * @param {Number} options.number 要生成的推广位数量，默认为10，范围是：1~100
   * @param {Array<String>} [options.pIdNameList] 推广位名称列表，例如["名称1", "名称2"]
   * @param {Number} [options.mediaId] 媒体ID，是PID的第一段数字
   * @returns {Promise} Promise对象
   */
  async generate(options) {
    // 参数校验
    if (!options.number) {
      throw new Error('参数错误: number不能为空');
    }
    
    if (options.number < 1 || options.number > 100) {
      throw new Error('参数错误: number必须在1-100之间');
    }

    // 执行请求
    return this.execute('pdd.ddk.oauth.goods.pid.generate', options);
  }
}

module.exports = GoodsPidGenerate; 