/**
 * 京粉精选商品查询示例
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.goods.jingfen.query
 */
require('dotenv').config();
const { JdClient } = require('../index');

// 检查环境变量
console.log('检查环境变量...');
['appkey', 'secretkey'].forEach(key => {
  console.log(`${key}: ${process.env[key] ? '已设置' : '未设置'}`);
});

console.log('\n环境变量实际值:');
console.log('appkey:', process.env.appkey);

// 创建京东客户端
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main() {
  try {
    // 构建请求参数
    const params = {
      goodsReq: {
        eliteId: 1,           // 频道ID，1-好券商品
        pageIndex: 1,         // 页码
        pageSize: 10,         // 每页数量
        sortName: 'commission', // 排序字段(佣金)
        sort: 'desc',         // 排序方式(降序)
        fields: 'videoInfo'   // 出参数据筛选
      }
    };

    console.log('\n尝试发送的参数:');
    console.log(JSON.stringify(params, null, 2));

    // 执行请求
    const result = await client.execute('UnionOpenGoodsJingfenQueryRequest', params);
    
    console.log('\n完整API响应:');
    console.log(JSON.stringify(result, null, 2));

    // 解析业务响应
    const apiResponse = result.jd_union_open_goods_jingfen_query_responce;
    if (apiResponse && apiResponse.code === '0') {
      const queryResult = JSON.parse(apiResponse.queryResult);
      
      console.log('\n业务响应:');
      console.log('  状态码:', queryResult.code);
      console.log('  消息:', queryResult.message);
      
      if (queryResult.code === 200 && queryResult.data) {
        console.log('\n查询到的商品:');
        console.log('  总数:', queryResult.totalCount || '未知');
        
        queryResult.data.forEach((item, index) => {
          console.log(`\n商品 ${index + 1}:`);
          console.log('  商品ID:', item.skuId);
          console.log('  商品名称:', item.skuName);
          
          if (item.priceInfo) {
            console.log('  价格信息:');
            console.log('    价格:', item.priceInfo.price);
            if (item.priceInfo.lowestPrice) {
              console.log('    最低价:', item.priceInfo.lowestPrice);
            }
            if (item.priceInfo.lowestCouponPrice) {
              console.log('    券后最低价:', item.priceInfo.lowestCouponPrice);
            }
          }
          
          if (item.commissionInfo) {
            console.log('  佣金信息:');
            console.log('    佣金比例:', item.commissionInfo.commissionShare + '%');
            console.log('    佣金:', item.commissionInfo.commission);
          }
          
          if (item.couponInfo && item.couponInfo.couponList && item.couponInfo.couponList.length > 0) {
            console.log('  优惠券信息:');
            item.couponInfo.couponList.forEach((coupon, cidx) => {
              console.log(`    优惠券 ${cidx + 1}:`);
              console.log(`      面额: ${coupon.discount}元`);
              console.log(`      使用门槛: ${coupon.quota}元`);
              if (coupon.getStartTime && coupon.getEndTime) {
                console.log(`      领取时间: ${new Date(coupon.getStartTime).toLocaleDateString()} - ${new Date(coupon.getEndTime).toLocaleDateString()}`);
              }
              if (coupon.useStartTime && coupon.useEndTime) {
                console.log(`      使用时间: ${new Date(coupon.useStartTime).toLocaleDateString()} - ${new Date(coupon.useEndTime).toLocaleDateString()}`);
              }
            });
          }
          
          console.log('  商品链接:', item.materialUrl);
          
          if (item.shopInfo) {
            console.log('  店铺信息:');
            console.log('    店铺名称:', item.shopInfo.shopName);
            console.log('    店铺ID:', item.shopInfo.shopId);
            console.log('    店铺评分:', item.shopInfo.shopLevel);
          }
        });
      }
    } else {
      console.error('API调用失败:', apiResponse);
    }

  } catch (err) {
    console.error('发生错误:', err);
    console.log('请检查您的.env文件中是否设置了正确的参数。');
    console.log('查看京东联盟API文档: https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.goods.jingfen.query');
  }
}

// 执行主函数
main(); 