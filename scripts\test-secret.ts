import dotenv from 'dotenv';
import axios from 'axios';

// 加载本地环境变量
dotenv.config();

// 主函数
async function main() {
  console.log('====== 测试 SERVER_SECRET 验证 ======');
  
  // 获取本地 SERVER_SECRET
  const localSecret = process.env.SERVER_SECRET;
  console.log('本地 SERVER_SECRET:', localSecret);
  
  try {
    // 发送请求获取远程 SERVER_SECRET
    console.log('正在从服务器获取 SERVER_SECRET...');
    const response = await axios.post('http://rap2api.taobao.org/app/mock/304812/taoke-mcp/secret');
    
    // 打印响应数据
    console.log('响应状态码:', response.status);
    console.log('响应数据类型:', typeof response.data);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    if (typeof response.data !== 'object' || !response.data || !('data' in response.data)) {
      throw new Error('响应数据格式不正确');
    }
    
    // 获取远程 SERVER_SECRET
    const serverSecret = response.data.data;
    console.log('远程 SERVER_SECRET:', serverSecret);
    
    // 比较本地和远程 SERVER_SECRET
    if (serverSecret !== localSecret) {
      console.error('SERVER_SECRET 验证失败，本地密钥与远程密钥不匹配');
    } else {
      console.log('SERVER_SECRET 验证通过');
    }
  } catch (error) {
    console.error('验证 SERVER_SECRET 失败:', error instanceof Error ? error.message : String(error));
  }
}

// 执行主函数
main().catch(error => {
  console.error('未处理的错误:', error);
  process.exit(1);
});
