/**
 * 联盟订单查询API
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.order.query
 */
const { BaseApi } = require('../api');

/**
 * 联盟订单查询API请求类
 * 提供批量查询推广订单及订单明细信息的接口
 */
class UnionOpenOrderQueryRequest extends BaseApi {
  /**
   * 获取API名称
   * @returns {string} API名称
   */
  getApiName() {
    return 'jd.union.open.order.query';
  }

  /**
   * 执行订单查询请求
   * @param {Object} params 请求参数
   * @param {Object} params.orderReq 订单查询请求对象
   * @param {Number} [params.orderReq.pageNo] 页码，默认1
   * @param {Number} [params.orderReq.pageSize] 每页数量，默认20
   * @param {Number} [params.orderReq.type] 订单时间查询类型(1：下单时间，2：完成时间，3：更新时间)
   * @param {String} [params.orderReq.time] 查询时间，建议使用时间戳，格式：yyyyMMddHH
   * @param {String} [params.orderReq.childUnionId] 子推客unionID
   * @param {String} [params.orderReq.key] 工具商传入推客的授权key
   * @returns {Promise<Object>} 请求结果
   */
  async query(params = {}) {
    if (!params.orderReq) {
      throw new Error('orderReq不能为空');
    }

    const apiParams = {
      orderReq: this.cleanParams(params.orderReq)
    };

    return this.execute(apiParams);
  }

  /**
   * 清理参数，移除空值
   * @param {Object} params 原始参数
   * @returns {Object} 清理后的参数
   */
  cleanParams(params) {
    const cleanedParams = {};
    
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null && params[key] !== '') {
        cleanedParams[key] = params[key];
      }
    }
    
    return cleanedParams;
  }
}

module.exports = {
  UnionOpenOrderQueryRequest
}; 