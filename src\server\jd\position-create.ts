import { log, logError } from "../../utils/logger.js";
import { BaseApi } from "./base-api.js";
import {
  JdPositionCreateParams,
  JdApiResponse,
  JdPositionCreateResponseData,
  JdUnionType,
  JdSiteType
} from "./types.js";

/**
 * 京东推广位创建API
 */
export class PositionCreate extends BaseApi {
  /**
   * 构造函数
   */
  constructor() {
    super();
    log('京东推广位创建API初始化完成');
  }

  /**
   * 创建推广位
   * @param params 创建参数
   * @returns 创建结果
   */
  async create(params: JdPositionCreateParams): Promise<JdApiResponse<JdPositionCreateResponseData>> {
    try {
      // 准备创建参数
      const createParams: JdPositionCreateParams = {
        unionId: params.unionId || this.unionId,
        key: params.key || this.key,
        unionType: params.unionType,
        type: params.type,
        spaceNameList: params.spaceNameList,
        siteId: params.siteId !== undefined ? params.siteId : this.siteId,
        pid: params.pid
      };

      log(`开始创建京东推广位, 参数:
      unionId: ${createParams.unionId}
      key: ${createParams.key ? '已设置(长度:' + createParams.key.length + ')' : '未设置'}
      unionType: ${createParams.unionType}
      type: ${createParams.type}
      spaceNameList: ${JSON.stringify(createParams.spaceNameList)}
      siteId: ${createParams.siteId}
      pid: ${createParams.pid || '未设置'}
      `);

      // 验证参数
      this.validateParams(createParams);

      // 使用基类方法调用API - 直接传递参数对象，不要包装成positionReq
      // SDK会自动将参数包装成 { positionReq: params }
      return await this.callApi('jd.union.open.position.create', createParams);
    } catch (error) {
      logError(`京东推广位创建错误: ${error instanceof Error ? error.message : String(error)}`);
      process.exit(1);
    }
  }

  /**
   * 验证参数
   * @param params 创建参数
   */
  private validateParams(params: JdPositionCreateParams): void {
    // 检查必填参数
    if (!params.key) {
      logError('业务参数key是必填的');
      process.exit(1);
    }

    if (!params.unionId) {
      logError('业务参数unionId是必填的');
      process.exit(1);
    }

    // 验证unionType的值是否有效
    if (params.unionType !== JdUnionType.PRIVATE && params.unionType !== JdUnionType.UNION) {
      logError(`业务参数unionType的值无效，应为${JdUnionType.PRIVATE}(私域推广位)或${JdUnionType.UNION}(联盟后台推广位)`);
      process.exit(1);
    }

    // 验证type的值是否有效
    if (params.type !== JdSiteType.MOBILE && params.type !== JdSiteType.PC) {
      logError(`业务参数type的值无效，应为${JdSiteType.MOBILE}(无线站)或${JdSiteType.PC}(PC站)`);
      process.exit(1);
    }

    // 验证spaceNameList
    if (!params.spaceNameList || !Array.isArray(params.spaceNameList) || params.spaceNameList.length === 0) {
      logError('业务参数spaceNameList应为非空数组');
      process.exit(1);
    }

    // 验证siteId
    if (params.siteId === undefined) {
      logError('业务参数siteId是必填的，无ID时传入0');
      process.exit(1);
    }
  }
}