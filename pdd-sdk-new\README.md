# 拼多多开放平台 SDK

一个简单易用的拼多多开放平台 SDK，支持所有多多客 API。

## 特点

- 支持 TypeScript
- 使用 axios 作为 HTTP 客户端
- 支持所有多多客 API
- 自动处理签名
- 支持调试模式

## 安装

```bash
npm install @liuliang520500/pdd-sdk-new
```

## 使用示例

```typescript
import { PddClient } from '@liuliang520500/pdd-sdk-new';

// 初始化客户端
const client = new PddClient({
  appKey: 'your-app-key',
  appSecret: 'your-app-secret',
  debug: true // 开启调试模式
});

// 调用商品详情 API
async function getGoodsDetail() {
  try {
    const params = {
      type: 'pdd.ddk.oauth.goods.detail',
      goods_sign: 'your-goods-sign',
      pid: 'your-pid'
    };

    const response = await client.execute(params);
    console.log(response);
  } catch (error) {
    console.error(error);
  }
}
```

## API 文档

### PddClient

#### 构造函数

```typescript
constructor(config: PddClientConfig)
```

配置参数：

- `appKey`: 应用的 AppKey
- `appSecret`: 应用的 AppSecret
- `serverUrl`: 接口地址（可选，默认为 https://gw-api.pinduoduo.com/api/router）
- `debug`: 是否开启调试模式（可选，默认为 false）

#### execute 方法

```typescript
async execute<T = any>(params: Record<string, any>): Promise<T>
```

参数：

- `params`: API 请求参数，必须包含 `type` 字段指定接口名称

返回值：

- 返回 Promise，解析为接口响应数据

## 常见问题

### 1. 签名错误

确保 AppKey 和 AppSecret 正确，参数值不要包含特殊字符。

### 2. 请求超时

默认超时时间为 30 秒，如需修改，可以在初始化时配置：

```typescript
const client = new PddClient({
  appKey: 'your-app-key',
  appSecret: 'your-app-secret',
  timeout: 60000 // 设置为 60 秒
});
```

## 许可证

MIT 