import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 活动转换测试客户端
 * 用于测试淘宝客活动转换功能
 */
export class ActivityConverterTest extends BaseTestClient {
	private activityId: string;

	/**
	 * 构造函数
	 * @param activityId 要测试的淘宝活动ID
	 */
	constructor(activityId: string) {
		super();
		this.activityId = activityId;
	}

	/**
	 * 执行活动转换测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			// 调用工具并获取结果
			const result = await this.callTool('taobao.convertActivity', {
				activity_material_id: this.activityId,
			});

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('活动转换测试成功!');
				console.log(`转换结果: ${result.content[0].text}`);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
	const testActivityId = '20150318020017611';
	const test = new ActivityConverterTest(testActivityId);
	test.runTest().catch(console.error);
}
