# MCP 服务架构文档

## 整体架构

本项目采用单一服务入口模式，遵循 MCP 协议规范设计，主要由以下几个核心组件组成：

1. **McpServerManager**：服务器管理器，单一入口点
2. **McpServer**：MCP 协议服务器实现
3. **API 管理器**：各平台 API 的管理器，采用延迟初始化模式
4. **工具函数**：注册到 MCP 服务器的各种功能工具

## 架构图

```
+---------------------+       +---------------------+
|                     |       |                     |
|   McpServerManager  |------>|  McpServer          |
|   (服务器管理器)     |       |  (MCP服务器)        |
|                     |       |                     |
+----------+----------+       +---------+-----------+
           |                            |
           |                            |
           v                            v
+----------+----------+       +---------+-----------+
|                     |       |                     |
|     ApiManagers     |<------|  MCP Tools          |
|  (延迟初始化API管理器)|       |  (工具函数)         |
|                     |       |                     |
+-----+-----+-----+---+       +---------------------+
      |     |     |
      |     |     |
      v     v     v
+-----+--+ +--+---+--+ +------------+
|        | |         | |            |
|  淘宝   | |  京东    | |  拼多多    |
|  API   | |  API    | |   API      |
|        | |         | |            |
+--------+ +---------+ +------------+
```

## 延迟初始化模式

本项目采用延迟初始化（Lazy Initialization）模式来管理 API 客户端，具有以下优点：

1. **资源优化**：只在实际需要使用时才创建 API 客户端，减少内存占用和初始化时间
2. **性能提升**：避免一次性初始化所有 API 客户端带来的启动延迟
3. **容错能力**：某个平台的 API 配置错误或不可用，不会影响其他平台功能

### 实现方式

```typescript
private apiManagers: {
  taobao?: TaobaoApiManager;
  // 未来可添加更多平台的API管理器
} = {};

private getTaobaoApiManager(): TaobaoApiManager {
  if (!this.apiManagers.taobao) {
    log('延迟初始化淘宝API管理器');
    this.apiManagers.taobao = new TaobaoApiManager();
  }
  return this.apiManagers.taobao;
}
```

### 何时触发初始化

API 客户端只在以下情况下才会被初始化：

1. 收到对应平台工具的调用请求
2. 第一次调用该平台的工具
3. API 客户端尚未初始化

这种方式确保了系统资源的高效利用，特别是在同时支持多个电商平台（淘宝、京东、拼多多等）的情况下。

## 工具注册流程

所有工具函数在服务器启动时就已注册完成，但底层 API 客户端则采用延迟初始化：

```typescript
// 注册工具
this.mcpServer.tool(
	'taobao.convertLink',
	'转换淘宝链接为推广链接',
	{
		url: z.string().url().describe('淘宝商品链接'),
	},
	async ({ url }) => {
		// 此处调用getTaobaoApiManager()，触发延迟初始化
		const result =
			await this.getTaobaoApiManager().linkConverter.convertLink(url);
		return {
			content: [
				{
					type: 'text',
					text: JSON.stringify(result),
				},
			],
		};
	}
);
```

## 符合 MCP 协议的设计

本架构严格遵循 MCP 协议规范，完整支持协议中定义的工具发现和调用流程：

1. **工具发现**：客户端通过 tools/list 请求获取所有可用工具
2. **工具调用**：客户端通过 tools/call 请求调用特定工具
3. **工具更新通知**：服务器可以发送 tools/list_changed 通知客户端工具列表变更

## 扩展新平台的方法

要添加新的电商平台支持（例如京东、拼多多），只需按以下步骤操作：

1. 创建新平台的 API 管理器：`JdApiManager`, `PddApiManager`等
2. 在`apiManagers`对象中添加相应的属性
3. 实现获取 API 管理器的方法：`getJdApiManager()`, `getPddApiManager()`等
4. 注册新平台的工具，使用相应的前缀：`jd.convertLink`, `pdd.parseLink`等

这种设计确保了系统的可扩展性，同时保持代码结构清晰。

## MCP 交互流程

```
LLM                   Client                  Server
 |                       |                       |
 |                       |                       |
 |                       |     tools/list        |
 |                       |---------------------->|
 |                       |                       |
 |                       |   工具列表响应         |
 |                       |<----------------------|
 |                       |                       |
 |    选择使用工具         |                       |
 |<----------------------|                       |
 |                       |                       |
 |    确认使用工具         |                       |
 |---------------------->|                       |
 |                       |                       |
 |                       |    tools/call         |
 |                       |---------------------->|
 |                       |                       |
 |                       |                       |
 |                       |   延迟初始化API        |
 |                       |   执行工具函数         |
 |                       |                       |
 |                       |                       |
 |                       |    工具结果响应        |
 |                       |<----------------------|
 |                       |                       |
 |    处理工具结果        |                       |
 |<----------------------|                       |
 |                       |                       |
```

## 错误处理机制

系统采用多层错误处理机制：

1. **API 层错误**：在 API 管理器中捕获并处理 API 调用异常
2. **工具层错误**：在工具函数中捕获异常，格式化为 MCP 协议规定的错误响应
3. **服务器层错误**：处理整体服务异常，确保服务稳定运行

所有错误都会被记录到日志系统，便于问题排查。

JWT 加密：npx tsx src/JWT/encrypt-env.ts -f .env.example -s url:mcp.sinataoke.cn -e 1000d -o env.jwt

先从：src\JWT\secret-config.ts 中获取云端三个环境变量，其中第一个 secret 用于 AES 密钥

另个两个用于 JWT 解密验证

加密： JWT-AES
解密： AES-JWT

然后和两层解密后的 SERVER_SECRET 作对比，再一次验证

默认日志目录：C:\Users\<USER>\AppData\Local\Temp\mcp-logs

运行服务端：npx tsx src/index.ts logs --test-mode
