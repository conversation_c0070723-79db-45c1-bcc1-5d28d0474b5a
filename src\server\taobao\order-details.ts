import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';

/**
 * 淘宝订单查询API
 * 提供订单查询功能
 */
export class OrderDetails extends BaseApi {
	/**
	 * 获取订单详情
	 * @param params 订单查询参数
	 * @returns 原始API响应
	 */
	public async getOrderDetails(params: any): Promise<any> {
		try {
			log(`执行订单查询: ${JSON.stringify(params)}`);

			// 调用淘宝API
			const result = await this.callApi(
				'taobao.tbk.sc.order.details.get',
				params
			);

			log(`订单查询结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`订单查询失败: ${error}`);
			throw error;
		}
	}
}
