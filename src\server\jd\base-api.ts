import { JdClient } from "@liuliang520500/jd-sdk";
import { log, logError, logDebug } from "../../utils/logger.js";
import { getEnvVar } from '../../utils/env.js';

// 为JdClient类扩展类型
declare module '@liuliang520500/jd-sdk' {
  interface JdClient {
    execute(method: string, params: any, options?: any): Promise<any>;
  }
}

/**
 * 京东API基础类
 * 提供通用的API客户端初始化和响应处理方法
 */
export class BaseApi {
  /**
   * 京东API客户端
   */
  protected client: JdClient;

  /**
   * 京东联盟ID
   */
  protected unionId: number;

  /**
   * 京东授权key
   */
  protected key: string;

  /**
   * 站点ID
   */
  protected siteId: number;

  /**
   * 构造函数
   */
  constructor() {
    // 获取环境变量
    const appKey = getEnvVar('JD_APP_KEY');
    const secretKey = getEnvVar('JD_APP_SECRET');
    this.key = getEnvVar('JD_KEY');
    this.unionId = Number(getEnvVar('JD_UNION_ID'));
    this.siteId = Number(getEnvVar('JD_SITE_ID') || '0');

    // 初始化京东API客户端
    try {
      this.client = new JdClient({
        appKey,
        secretKey
      });
      log('京东API客户端初始化成功');
    } catch (error) {
      const errMsg = `京东API客户端初始化失败: ${error instanceof Error ? error.message : String(error)}`;
      logError(errMsg);
      process.exit(1);
    }
  }

  /**
   * 调用京东API并处理响应
   * @param method API方法名
   * @param params API参数
   * @returns 原始API响应
   */
  protected async callApi(method: string, params: any): Promise<any> {
    try {
      // 安全地记录参数，避免泄露敏感信息
      const safeParams = { ...params };
      if (safeParams.key) {
        safeParams.key = '已设置';
      }
      logDebug(`调用京东API: ${method}, 参数: ${JSON.stringify(safeParams)}`);

      // 执行API调用
      const response = await this.client.execute(method, params);

      // 记录原始响应
      logDebug(`API ${method} 原始响应: ${JSON.stringify(response)}`);

      // 直接返回原始响应，不做解析
      return response;
    } catch (error) {
      const errMsg = `京东API调用失败: ${error instanceof Error ? error.message : String(error)}`;
      logError(errMsg);
      process.exit(1);
    }
  }

  /**
   * 获取通用参数
   * @returns 通用参数对象
   */
  protected getCommonParams(): any {
    return {
      unionId: this.unionId,
      key: this.key
    };
  }
}