import chalk from 'chalk';
import { BaseJdTestClient } from './base-test-client.js';
import dotenv from 'dotenv';

// 加载环境变量
dotenv.config();

/**
 * 测试京东推广位查询
 */
async function testPositionQuery() {
	console.log(chalk.blue('开始测试京东推广位查询API...'));

	// 客户端不需要检查环境变量，这些环境变量是服务端从网络JWT中获取的

	const client = new BaseJdTestClient();

	try {
		// 等待服务启动
		await new Promise((resolve) => setTimeout(resolve, 2000));

		console.log(chalk.gray('正在调用京东推广位查询API...'));

		// 测试联盟后台推广位查询
		const unionPositionResult = await client.callJdTool('positionQuery', {
			unionType: 4, // 联盟后台推广位
			pageIndex: 1,
			pageSize: 10,
		});

		// 打印响应结果
		console.log('联盟后台推广位查询结果:');
		console.log(JSON.stringify(unionPositionResult, null, 2));

		console.log(chalk.green('京东推广位查询API测试成功!'));
	} catch (error) {
		console.error(chalk.red('京东推广位查询API测试失败:'), error);
		process.exit(1);
	} finally {
		// 关闭客户端
		client.close();
	}
}

// 直接执行测试函数
testPositionQuery().catch((error) => {
	console.error('测试执行失败:', error);
	process.exit(1);
});

export { testPositionQuery };
