import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 创建推广位测试客户端
 * 用于测试淘宝客创建推广位功能
 */
export class CreatePromotionZoneTest extends BaseTestClient {
	private adzoneName: string;

	/**
	 * 构造函数
	 * @param adzoneName 广告位名称
	 */
	constructor(adzoneName: string) {
		super();
		this.adzoneName = adzoneName;
	}

	/**
	 * 执行创建推广位测试
	 */
	public async runTest(): Promise<void> {
		try {
			// 连接服务器
			await this.connect();

			console.log(
				'发送创建推广位请求参数:',
				JSON.stringify(
					{
						adzone_name: this.adzoneName,
					},
					null,
					2
				)
			);

			// 调用工具并获取结果
			const result = await this.callTool('taobao.createPromotionZone', {
				adzone_name: this.adzoneName,
			});

			// 验证响应
			if (result.content?.[0]?.text) {
				console.log('创建推广位测试成功!');
				console.log(`创建结果: ${result.content[0].text}`);
			} else {
				throw new Error('响应格式不正确');
			}
		} catch (error) {
			console.error('测试失败:', error);
			throw error;
		} finally {
			// 断开连接
			await this.disconnect();
		}
	}
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

// 如果直接运行此文件,则执行测试
if (currentFilePath === entryPointPath) {
	// 生成带时间戳的广告位名称避免重复
	const adzoneName = '测试广告位_' + new Date().getTime();

	console.log('启动创建推广位测试');
	const test = new CreatePromotionZoneTest(adzoneName);
	test.runTest().catch(console.error);
}
