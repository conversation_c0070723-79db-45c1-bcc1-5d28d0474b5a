import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { TaobaoApiManager } from '../../taobao/api-manager.js';
import { TaobaoParams } from './params.js';

/**
 * 淘宝客-服务商-权益物料精选服务类
 * 处理淘宝客权益物料精选功能
 */
export class TaobaoOptimusPromotionService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 淘宝API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: TaobaoApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册权益物料精选工具
	 * 基于淘宝开放平台API文档：taobao.tbk.sc.optimus.promotion
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('getOptimusPromotion');

		this.mcpServer.tool(
			toolName,
			'权益物料精选[淘宝客-服务商-权益物料精选][官方提供的权益物料Id。有价券-37104、大额店铺券-37116、天猫店铺券-62191、券券补-61809 更多权益物料id敬请期待！]',
			TaobaoParams.optimusPromotion,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);

					const result =
						await this.apiManager.optimusPromotion.getOptimusPromotion(
							params
						);
					log(`工具 ${toolName} 执行结果: ${JSON.stringify(result)}`);
					return {
						content: [
							{
								type: 'text',
								text: JSON.stringify(result),
							},
						],
					};
				} catch (error) {
					logError(
						`工具 ${toolName} 执行出错: ${
							error instanceof Error
								? error.message
								: String(error)
						}`
					);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${
									error instanceof Error
										? error.message
										: JSON.stringify(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
