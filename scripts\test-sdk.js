/**
 * 京东SDK测试脚本
 */
import { config } from 'dotenv';
import { JdClient } from '@liuliang520500/jd-sdk';

// 加载环境变量
config();

// 检查必要的环境变量
console.log('环境变量信息:');
console.log('JD_APP_KEY:', process.env.JD_APP_KEY ? '已设置' : '未设置');
console.log('JD_APP_SECRET:', process.env.JD_APP_SECRET ? '已设置' : '未设置');
console.log('JD_KEY:', process.env.JD_KEY ? '已设置' : '未设置');
console.log('JD_UNION_ID:', process.env.JD_UNION_ID ? '已设置' : '未设置');

// 创建京东客户端实例
console.log('创建JdClient实例...');
const client = new JdClient({
  appKey: process.env.JD_APP_KEY,
  secretKey: process.env.JD_APP_SECRET
});
console.log('JdClient实例创建成功');

// 测试推广位查询
async function testPositionQuery() {
  try {
    console.log('开始测试推广位查询...');
    
    // 执行查询
    const result = await client.execute('jd.union.open.position.query', {
      unionId: Number(process.env.JD_UNION_ID),
      key: process.env.JD_KEY,
      unionType: 3, // 私域推广位
      pageIndex: 1,
      pageSize: 10
    });
    
    console.log('查询成功，响应结果:');
    console.log(JSON.stringify(result, null, 2));
    
    // 解析返回结果
    if (result && result.jd_union_open_position_query_responce) {
      const response = result.jd_union_open_position_query_responce;
      if (response.queryResult) {
        const queryResult = JSON.parse(response.queryResult);
        console.log('查询结果:');
        console.log(JSON.stringify(queryResult, null, 2));
      }
    }
  } catch (error) {
    console.error('推广位查询出错:', error);
  }
}

// 执行测试
testPositionQuery(); 