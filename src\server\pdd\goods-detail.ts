import { BaseApi } from './base-api.js';
import { getEnvVar } from '../../utils/env.js';
import { log } from '../../utils/logger.js';
import { GoodsDetailParams } from './types.js';

/**
 * 商品详情 API
 */
export class GoodsDetail extends BaseApi {
	/**
	 * 查询商品详情
	 * @param params 商品详情查询参数
	 * @returns 商品详情查询结果
	 */
	async getDetail(params: GoodsDetailParams): Promise<any> {
		// 从环境变量获取 PID，如果存在则添加到参数中
		const pid = getEnvVar('PDD_PID');
		// 构造完整参数
		const fullParams: Record<string, any> = {
			...params,
			zs_duo_id: 22165238, // 将 zs_duo_id 写死为 22165238
			pid: pid,
		};

		// 调用 API
		return this.callApi('pdd.ddk.oauth.goods.detail', fullParams);
	}
}
