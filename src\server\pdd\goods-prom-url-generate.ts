import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { getRequiredEnvVar } from '../../utils/env.js';
import { GoodsPromUrlGenerateParams } from './types.js';

/**
 * 商品推广链接生成 API
 */
export class GoodsPromUrlGenerate extends BaseApi {
	/**
	 * 生成商品推广链接
	 * @param params 生成参数
	 * @returns 生成结果
	 */
	async generate(params: GoodsPromUrlGenerateParams): Promise<any> {
		try {
			// 从环境变量获取 PID（必填参数）
			const pid = getRequiredEnvVar('PDD_PID');

			// 构造完整参数
			const fullParams = {
				...params,
				p_id: pid, // 如果没有提供p_id_list，则使用环境变量中的PID
				zs_duo_id: 22165238, // 将 zs_duo_id 写死为 22165238
			};

			log(`完整参数: ${JSON.stringify(fullParams)}`);

			// 调用API
			return this.callApi(
				'pdd.ddk.oauth.goods.prom.url.generate',
				fullParams
			);
		} catch (error) {
			log(`生成商品推广链接失败: ${error}`);
			throw error;
		}
	}
}
