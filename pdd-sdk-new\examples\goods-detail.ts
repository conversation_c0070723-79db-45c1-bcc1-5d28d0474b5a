import { PddClient, PddResponse } from '../index.js';
import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config();

// 初始化客户端
const client = new PddClient({
  clientId: process.env.PDD_CLIENT_ID!,
  clientSecret: process.env.PDD_CLIENT_SECRET!,
  accessToken: process.env.PDD_SESSION_TOKEN
});

interface GoodsDetailResponse {
  goods_detail_response: {
    goods_details: Array<{
      goods_id: number;
      goods_name: string;
      goods_desc: string;
      goods_thumbnail_url: string;
      goods_image_url: string;
      goods_gallery_urls: string[];
      min_group_price: number;
      min_normal_price: number;
      mall_name: string;
      [key: string]: any;
    }>;
  };
}

async function testGoodsDetail() {
  try {
    const params = {
      type: 'pdd.ddk.oauth.goods.detail',
      goods_sign: 'E9j2AdzphfZiwIxFwvTel54m3Q953ZdY_JQcAb6DCCX',
      pid: process.env.PDD_PID,
    };

    const response = await client.execute<PddResponse<GoodsDetailResponse>>(params);
    
    if (response.goods_detail_response) {
      const goods = response.goods_detail_response.goods_details[0];
      console.log(`商品 ${goods.goods_name} (ID: ${goods.goods_id}) 查询成功`);
    }
  } catch (error) {
    console.error('查询失败：', error);
  }
}

testGoodsDetail(); 