/**
 * 环境变量加密工具
 * 用于生成加密的环境变量JWT令牌，并可选择性地使用AES进行二次加密
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import axios from 'axios';
import {
	generateToken,
	encryptWithAes,
	encryptWithJwtAndAes,
} from './jwt-utils.js';

/**
 * 从远程服务器获取 Secret 配置
 * @returns Promise<{issuer: string, audience: string}> Secret 配置
 */
async function fetchSecretConfig(): Promise<{
	secret: string;
	issuer: string;
	audience: string;
}> {
	try {
		console.log('正在从远程服务器获取 Secret 配置...');

		// 发送请求获取数据
		const response = await axios.post(
			'http://rap2api.taobao.org/app/mock/304812/taoke-mcp/secret'
		);

		if (response.status !== 200) {
			throw new Error(`请求失败，状态码: ${response.status}`);
		}

		if (
			typeof response.data !== 'object' ||
			!response.data ||
			!('data' in response.data)
		) {
			throw new Error('响应数据格式不正确');
		}

		// 获取数据
		const secretData = response.data.data;

		if (typeof secretData !== 'string' || !secretData.trim()) {
			throw new Error('获取到的 Secret 数据无效');
		}

		// 解析数据
		const parts = secretData.split('|');

		if (parts.length !== 3) {
			throw new Error(
				`Secret 数据格式不正确，期望 3 个部分，实际 ${parts.length} 个部分`
			);
		}

		const [secret, issuer, audience] = parts;

		return {
			secret,
			issuer,
			audience,
		};
	} catch (error) {
		console.error(
			`获取 Secret 配置失败: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
		throw error;
	}
}

/**
 * 从.env文件加载环境变量并生成JWT令牌，可选择性地使用AES进行二次加密
 * @param envFilePath .env文件路径
 * @param secret JWT密钥
 * @param expiresIn 过期时间
 * @param aesKey 可选的AES密钥，如果提供则使用AES进行二次加密
 * @returns Promise<string> 加密后的数据
 */
export async function encryptEnvFile(
	envFilePath: string,
	secret: string,
	expiresIn: string = '30d',
	aesKey?: string
): Promise<string> {
	// 检查文件是否存在
	if (!fs.existsSync(envFilePath)) {
		throw new Error(`环境变量文件不存在: ${envFilePath}`);
	}

	// 读取.env文件
	const envContent = fs.readFileSync(envFilePath, 'utf8');

	// 解析环境变量
	const envVars = dotenv.parse(envContent);

	// 从远程服务器获取 Secret 配置
	let issuer = 'mcp-env';
	let audience = 'taoke-mcp-server';

	try {
		const config = await fetchSecretConfig();
		issuer = config.issuer;
		audience = config.audience;
		aesKey = config.secret;
		console.log(
			`使用从远程服务器获取的加密参数:aesKey = ${aesKey}, issuer=${issuer}, audience=${audience}`
		);
	} catch (error) {
		console.warn(
			`无法从远程服务器获取 Secret 配置，使用默认值: ${
				error instanceof Error ? error.message : String(error)
			}`
		);
	}

	// 如果提供AES密钥，则使用JWT+AES双重加密
	if (aesKey) {
		// 使用JWT和AES双重加密
		return encryptWithJwtAndAes(envVars, secret, aesKey, {
			expiresIn,
			issuer,
			audience,
		});
	} else {
		// 只使用JWT加密
		const token = generateToken(envVars, {
			secret,
			expiresIn,
			issuer,
			audience,
		});

		return token;
	}
}

// 检查是否直接运行此文件
// const currentFilePath = fileURLToPath(import.meta.url);

const currentFilePath = __filename;
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件，则执行加密
if (isDirectlyExecuted) {
	try {
		// 获取命令行参数
		const args = process.argv.slice(2);
		const envFilePath = args[0] || path.resolve(process.cwd(), '.env');
		const secret = args[1] || 'mcp-default-secret-key';
		const expiresIn = args[2] || '30d';
		const aesKey = args[3]; // 可选的AES密钥

		// 加密环境变量
		(async () => {
			const encryptedData = await encryptEnvFile(
				envFilePath,
				secret,
				expiresIn,
				aesKey
			);

			// 注意：这里保留console.log，因为这是命令行工具，需要在终端显示结果
			console.log('环境变量加密成功!');
			if (aesKey) {
				console.log('JWT-AES双重加密数据:');
			} else {
				console.log('JWT令牌:');
			}
			console.log(encryptedData);
		})();
	} catch (error) {
		console.error(
			'环境变量加密失败:',
			error instanceof Error ? error.message : String(error)
		);
		process.exit(1);
	}
}



