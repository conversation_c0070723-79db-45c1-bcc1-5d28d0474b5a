/**
 * 发布前准备脚本
 * 
 * 在发布到npm前自动执行，用于确保发布内容正确
 */

const fs = require('fs');
const path = require('path');

console.log('> 开始发布前准备工作...');

// 确保脚本目录存在
const scriptsDir = path.join(__dirname);
if (!fs.existsSync(scriptsDir)) {
  fs.mkdirSync(scriptsDir, { recursive: true });
}

// 检查index.d.ts是否存在
const typesPath = path.join(__dirname, '..', 'index.d.ts');
if (!fs.existsSync(typesPath)) {
  console.error('错误: index.d.ts 文件不存在，请确保该文件已创建');
  process.exit(1);
}

// 检查index.js是否存在
const indexPath = path.join(__dirname, '..', 'index.js');
if (!fs.existsSync(indexPath)) {
  console.error('错误: index.js 文件不存在');
  process.exit(1);
}

// 检查lib目录是否存在
const libDir = path.join(__dirname, '..', 'lib');
if (!fs.existsSync(libDir)) {
  console.error('错误: lib 目录不存在');
  process.exit(1);
}

console.log('√ 文件检查完成');
console.log('√ 准备完成，可以发布到npm'); 