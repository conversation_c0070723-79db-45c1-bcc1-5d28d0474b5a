/**
 * 拼多多商品推广链接生成API测试
 */

// 导入依赖
const { PddClient } = require('../index');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 加载环境变量（从项目根目录的.env文件）
const envPath = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log(`已加载环境变量文件: ${envPath}`);
} else {
  console.warn(`警告: 环境变量文件不存在: ${envPath}`);
}

// 检查必要的环境变量是否存在
const requiredEnvVars = [
  'PDD_CLIENT_ID', 
  'PDD_CLIENT_SECRET', 
  'PDD_SESSION_TOKEN', 
  'PDD_PID'
  // 不再需要 PDD_GOODS_SIGN
];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error(`缺少必要的环境变量: ${missingEnvVars.join(', ')}`);
  console.error('请确保.env文件中包含这些变量');
  process.exit(1);
}

// 初始化客户端
const client = new PddClient({
  clientId: process.env.PDD_CLIENT_ID,
  clientSecret: process.env.PDD_CLIENT_SECRET,
  accessToken: process.env.PDD_SESSION_TOKEN,
  debug: true // 启用调试模式
});

// 测试商品推广链接生成
async function testPromUrlGenerate() {

    // 使用固定的goodsSign，而不是从环境变量获取
    const goodsSign = 'E9j2AdzphfZiwIxFwvTel54m3Q953ZdY_JQcAb6DCCX';
    
    console.log('======= 拼多多商品推广链接生成测试 =======');
    console.log('客户端配置:');
    console.log('- clientId:', process.env.PDD_CLIENT_ID);
    console.log('- accessToken:', maskToken(process.env.PDD_SESSION_TOKEN));
    console.log('- PID:', process.env.PDD_PID);
    console.log('- 商品sign:', goodsSign);
    console.log('\n开始生成推广链接...');
    
    // 构建请求参数-
    const options = {
      pid: process.env.PDD_PID,
      goodsSignList: [goodsSign], // 使用指定的goodsSign
      generateShortUrl: true,
      generateWeApp: true,
    };
    
    console.log('请求参数:', JSON.stringify(options, null, 2));
    
    // 执行请求
    const result = await client.goodsPromUrlGenerate.generate(options);

    console.log('响应结果:', JSON.stringify(result, null, 2));
    
}


// 辅助函数：掩码显示令牌
function maskToken(token) {
  if (!token) return 'undefined';
  return token.substr(0, 6) + '****' + token.substr(-6);
}

// 执行测试
testPromUrlGenerate(); 