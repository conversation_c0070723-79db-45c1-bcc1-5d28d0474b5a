import dotenv from 'dotenv';
import {
	loadEnvFromNetwork,
	checkRequiredEnvVars,
} from './src/JWT/env-loader.js';
import axios from 'axios';

// 加载本地环境变量
dotenv.config();

// 主函数
async function startServer(): Promise<void> {
	console.log('====== 启动服务器 ======');
	console.log('正在加载环境变量...');

	// 获取环境变量
	const envUrl = process.env.ENV_JWT_URL;
	const envSecret = process.env.ENV_JWT_SECRET || 'mcp-default-secret-key';

	console.log('环境变量 URL:', envUrl);
	console.log('环境变量 Secret:', envSecret ? '已设置' : '未设置');

	// 尝试从网络加载环境变量
	const result = await loadEnvFromNetwork({
		url: envUrl!,
		secret: envSecret,
		override: true,
	});

	console.log('从网络加载环境变量结果:', result ? '成功' : '失败');

	// 显示一些关键环境变量
	console.log('\n关键环境变量:');
	console.log(
		'TAOBAO_APP_KEY:',
		process.env.TAOBAO_APP_KEY ? '已设置' : '未设置'
	);
	console.log(
		'TAOBAO_APP_SECRET:',
		process.env.TAOBAO_APP_SECRET ? '已设置' : '未设置'
	);
	console.log(
		'TAOBAO_SESSION:',
		process.env.TAOBAO_SESSION ? '已设置' : '未设置'
	);
	console.log(
		'TAOBAO_ADZONE_ID:',
		process.env.TAOBAO_ADZONE_ID ? '已设置' : '未设置'
	);
	console.log(
		'TAOBAO_SITE_ID:',
		process.env.TAOBAO_SITE_ID ? '已设置' : '未设置'
	);

	console.log('\n正在检查环境变量...');

	// 检查必要的环境变量 - 从 .env.example 中提取所有环境变量
	const requiredEnvVars = [
		// 淘宝客API配置
		'TAOBAO_APP_KEY',
		'TAOBAO_APP_SECRET',
		'TAOBAO_SITE_ID',
		'TAOBAO_ADZONE_ID',
		'TAOBAO_SESSION',

		// 京东联盟API配置
		'JD_APP_KEY',
		'JD_APP_SECRET',
		'JD_KEY',
		'JD_UNION_ID',
		'JD_SITE_ID',

		// 拼多多API配置
		'PDD_CLIENT_ID',
		'PDD_CLIENT_SECRET',
		'PDD_PID',
		'PDD_SESSION_TOKEN',

		// 服务器密钥
		'SERVER_SECRET',
	];

	// 使用环境变量加载器检查必要的环境变量
	const checkResult = checkRequiredEnvVars(requiredEnvVars);
	console.log('环境变量检查结果:', checkResult ? '通过' : '失败');

	if (!checkResult) {
		console.error('缺少必要的环境变量，无法启动服务器');
		return;
	}

	console.log('\n环境变量检查通过，服务器可以启动');
}

// 执行主函数
startServer().catch((error) => {
	console.error('未处理的错误:', error);
	process.exit(1);
});
