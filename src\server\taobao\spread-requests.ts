import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import {
	TaobaoApiResponse,
	SpreadRequestsResponseData,
	SpreadRequestsParams,
} from './types.js';

/**
 * 淘宝客-公用-长链转短链API
 * 提供批量转换淘宝链接为短链接的功能
 */
export class SpreadRequests extends BaseApi {
	/**
	 * 批量转换淘宝链接为短链接
	 * @param params 请求参数，包含 requests 列表
	 * @returns 原始API响应
	 */
	async spreadRequests(
		params: SpreadRequestsParams
	): Promise<TaobaoApiResponse<SpreadRequestsResponseData>> {
		try {
			log(`执行批量链接转短链: ${JSON.stringify(params)}`);

			// 根据官方文档正确构造请求参数
			// requests参数需要是一个JSON字符串，格式为：[{"url":"链接1"},{"url":"链接2"}]
			const requests = JSON.stringify(params.requests);

			// 调用淘宝API
			const result = await this.callApi('taobao.tbk.spread.get', {
				requests,
			});

			log(`批量链接转短链结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`批量链接转短链失败: ${error}`);
			throw error;
		}
	}
}
