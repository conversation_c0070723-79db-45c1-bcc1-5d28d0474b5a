/**
 * 商品分类查询示例
 * @see https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.category.goods.get
 */
require('dotenv').config();
const { JdClient } = require('../index');

// 检查环境变量
console.log('检查环境变量...');
['appkey', 'secretkey'].forEach(key => {
  console.log(`${key}: ${process.env[key] ? '已设置' : '未设置'}`);
});

console.log('\n环境变量实际值:');
console.log('appkey:', process.env.appkey);

// 创建京东客户端
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main() {
  try {
    // 构建请求参数
    const params = {
      req: {
        parentId: 0,  // 父类目ID，默认为0，表示查询一级类目
        grade: 0      // 类目级别，0：一级，1：二级，2：三级
      }
    };

    console.log('\n尝试发送的参数:');
    console.log(JSON.stringify(params, null, 2));

    // 执行请求
    const result = await client.execute('UnionOpenCategoryGoodsGetRequest', params);
    
    console.log('\n完整API响应:');
    console.log(JSON.stringify(result, null, 2));

    // 解析业务响应
    const apiResponse = result.jd_union_open_category_goods_get_responce;
    if (apiResponse && apiResponse.code === '0') {
      const getResult = JSON.parse(apiResponse.getResult);
      
      console.log('\n业务响应:');
      console.log('  状态码:', getResult.code);
      console.log('  消息:', getResult.message);
      
      if (getResult.code === 200 && getResult.data) {
        console.log('\n查询到的类目:');
        console.log('  类目数量:', getResult.data.length);
        
        getResult.data.forEach((category, index) => {
          console.log(`\n类目 ${index + 1}:`);
          console.log('  类目ID:', category.id);
          console.log('  类目名称:', category.name);
          console.log('  级别:', category.grade);
          if (category.parentId !== undefined) {
            console.log('  父类目ID:', category.parentId);
          }
        });
        
        console.log('\n使用示例:');
        console.log('1. 要获取二级类目，设置 parentId 为一级类目ID，grade 为 1');
        console.log('2. 要获取三级类目，设置 parentId 为二级类目ID，grade 为 2');
      }
    } else {
      console.error('API调用失败:', apiResponse);
    }

  } catch (err) {
    console.error('发生错误:', err);
    console.log('请检查您的.env文件中是否设置了正确的参数。');
    console.log('查看京东联盟API文档: https://union.jd.com/openplatform/api/v2?apiName=jd.union.open.category.goods.get');
  }
}

// 执行主函数
main(); 