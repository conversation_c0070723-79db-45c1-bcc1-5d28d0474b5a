/**
 * 推广位创建的使用示例
 */
const { JdClient, ApiTypes } = require('../index');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 检查环境变量
console.log('检查环境变量...');
console.log('key:', process.env.key ? '已设置' : '未设置');
console.log('appkey:', process.env.appkey ? '已设置' : '未设置');
console.log('secretkey:', process.env.secretkey ? '已设置' : '未设置');
console.log('unionId:', process.env.unionId ? '已设置' : '未设置');
console.log('siteId:', process.env.siteId ? '已设置' : '未设置，将使用默认值0');

// 显示所有环境变量的具体值，便于排查
console.log('\n环境变量实际值:');
console.log('key:', process.env.key);
console.log('appkey:', process.env.appkey);
console.log('unionId:', process.env.unionId);
console.log('siteId:', process.env.siteId);

// 创建客户端实例
const client = new JdClient({
  appKey: process.env.appkey,
  secretKey: process.env.secretkey
});

// 主函数
async function main() {
  try {
    // 从环境变量获取siteId，如果未设置则使用0
    const siteId = process.env.siteId ? Number(process.env.siteId) : 0;
    
    // 根据成功的测试参数设置
    const positionReq = {
      unionId: Number(process.env.unionId || 0),
      key: process.env.key,
      unionType: 4, // 联盟后台推广位
      type: 3, // 修改为3，与成功参数一致
      spaceNameList: [`test_${Date.now()}`], // 使用时间戳确保名称唯一
      siteId: siteId
    };
    
    console.log('\n尝试发送的参数:');
    console.log(JSON.stringify(positionReq, null, 2));
    
    // 使用execute方法调用API
    const result = await client.execute('jd.union.open.position.create', positionReq);

    console.log('\n完整API响应:');
    console.log(JSON.stringify(result, null, 2));
    
    // 分析API响应
    if (result.jd_union_open_position_create_responce) {
      const response = result.jd_union_open_position_create_responce;
      if (response.createResult) {
        try {
          const createResult = JSON.parse(response.createResult);
          console.log('\n业务响应:');
          console.log('  状态码:', createResult.code);
          console.log('  消息:', createResult.message);
          console.log('  请求ID:', createResult.requestId);
          
          if (createResult.code === 200 && createResult.data) {
            console.log('\n创建成功的推广位:');
            if (createResult.data.resultList && createResult.data.resultList.length > 0) {
              createResult.data.resultList.forEach(item => {
                console.log(`  - ID: ${item.id}, 名称: ${item.spaceName || '未命名'}`);
              });
            } else if (createResult.data.pid) {
              console.log('  PID:', createResult.data.pid);
              console.log('  推广位ID:', createResult.data.resultList || '未返回ID');
            }
          } else {
            console.error(`\n错误: ${createResult.message}`);
            console.log('错误代码:', createResult.code);
            console.log('请求ID:', createResult.requestId);
          }
        } catch (e) {
          console.error('解析API响应时出错:', e.message);
        }
      }
    }
  } catch (error) {
    console.error('推广位创建失败:', error.message);
    console.error('可能的解决方案:');
    console.error('1. 检查.env文件中的业务参数是否正确设置');
    console.error('2. 确认unionId和key参数的正确性');
    console.error('3. 参考京东联盟API文档: https://union.jd.com/openplatform/api/10428');
  }
}

// 运行主函数
main(); 