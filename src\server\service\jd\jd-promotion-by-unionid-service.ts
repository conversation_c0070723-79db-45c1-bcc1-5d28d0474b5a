import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { log, logError } from "../../../utils/logger.js";
import { JdApiManager } from "../../jd/api-manager.js";

/**
 * 京东推广链接生成服务类
 * 处理京东联盟转推广链接功能
 */
export class JdPromotionByUnionidService {
  /**
   * 构造函数
   * @param mcpServer MCP服务器实例
   * @param apiManager 京东API管理器实例
   * @param getFullToolName 获取完整工具名称的函数
   */
  constructor(
    private mcpServer: McpServer,
    private apiManager: JdApiManager,
    private getFullToolName: (name: string) => string
  ) {}

  /**
   * 注册转推广链接工具
   */
  public registerTool(): void {
    const toolName = this.getFullToolName("promotion.byunionid.get");
    
    this.mcpServer.tool(
      toolName,
      "生成京东商品推广链接[京东联盟-转推广链接]",
      {
        materialId: z.string().describe("【必填】推广物料ID，如商品skuId、活动ID、商品链接等"),
        positionId: z.number().int().optional().describe("【非必填】推广位ID，如不填写则生成无推广位链接"),
        subUnionId: z.string().optional().describe("【非必填】子联盟ID，用于区分子账号推广效果"),
        ext1: z.string().optional().describe("【非必填】自定义扩展信息，建议json格式"),
        chainType: z.number().int().min(1).max(3).optional().describe("【非必填】链接类型，1-长链接，2-短链接，3-长链接+短链接，默认1"),
        couponUrl: z.string().optional().describe("【非必填】优惠券领取链接，填写则生成二合一推广链接"),
        giftCouponKey: z.string().optional().describe("【非必填】礼金批次ID"),
        channelId: z.number().int().optional().describe("【非必填】渠道ID")
      },
      async ({ materialId, positionId, subUnionId, ext1, chainType, couponUrl, giftCouponKey, channelId }) => {
        try {
          log(`执行工具 ${toolName}: ${JSON.stringify({ materialId, positionId, subUnionId, ext1, chainType, couponUrl, giftCouponKey, channelId })}`);
          
          // 获取环境变量
          const unionId = process.env.JD_UNION_ID;
          
          // 检查必要的环境变量
          if (!unionId) {
            throw new Error('环境变量JD_UNION_ID未设置');
          }
          
          log(`使用环境变量: JD_UNION_ID=${unionId}`);
          
          try {
            // 执行推广链接生成
            const result = await this.apiManager.promotionByUnionid.getPromotionLink({
              materialId,
              unionId: Number(unionId),
              positionId,
              subUnionId,
              ext1,
              chainType,
              couponUrl,
              giftCouponKey,
              channelId
            });
            
            log(`工具 ${toolName} 执行结果: ${JSON.stringify(result, null, 2)}`);
            
            // 结果已经是解析后的JSON对象，直接返回
            return {
              content: [{
                type: "text",
                text: JSON.stringify(result, null, 2)
              }]
            };
          } catch (apiError) {
            logError(`转推广链接API调用失败: ${apiError instanceof Error ? apiError.message : String(apiError)}`);
            throw apiError;
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          logError(`工具 ${toolName} 执行出错: ${errorMessage}`);
          return {
            content: [{
              type: "text",
              text: `工具执行失败: ${errorMessage}`
            }],
            isError: true
          };
        }
      }
    );
  }
}
