import { BaseTestClient } from './base-test-client.js';
import { fileURLToPath } from 'url';
import path from 'path';

/**
 * 查询是否绑定备案测试客户端
 * 用于测试拼多多查询是否绑定备案功能
 */
export class MemberAuthorityQueryTest extends BaseTestClient {
  /**
   * 构造函数
   */
  constructor() {
    super();
  }

  /**
   * 执行查询是否绑定备案测试
   */
  public async runTest(): Promise<void> {
    try {
      // 连接服务器
      await this.connect();

      // 调用工具并获取结果
      const result = await this.callTool("pdd.member.authority.query", {});

      // 验证响应
      if (result.content?.[0]?.text) {
        const response = JSON.parse(result.content[0].text);
        console.log('查询是否绑定备案测试成功!');
        console.log(`是否绑定备案: ${response.authority_query_response.bind ? '是' : '否'}`);
        console.log('完整响应:');
        console.log(result.content[0].text);
      } else {
        throw new Error('响应格式不正确');
      }
    } catch (error) {
      console.error('测试失败:', error);
      throw error;
    } finally {
      // 断开连接
      await this.disconnect();
    }
  }
}

const currentFilePath = fileURLToPath(import.meta.url);
const entryPointPath = path.resolve(process.argv[1]);

const isDirectlyExecuted = currentFilePath === entryPointPath;

// 如果直接运行此文件,则执行测试
if (isDirectlyExecuted) {
  const test = new MemberAuthorityQueryTest();
  test.runTest().catch(console.error);
}
