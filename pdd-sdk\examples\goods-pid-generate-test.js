/**
 * 拼多多推广位生成API测试
 */

// 导入依赖
const { PddClient } = require('../index');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 加载环境变量（从项目根目录的.env文件）
const envPath = path.resolve(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  dotenv.config({ path: envPath });
  console.log(`已加载环境变量文件: ${envPath}`);
} else {
  console.warn(`警告: 环境变量文件不存在: ${envPath}`);
}

// 检查必要的环境变量是否存在
const requiredEnvVars = ['PDD_CLIENT_ID', 'PDD_CLIENT_SECRET', 'PDD_SESSION_TOKEN', 'PDD_PID'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingEnvVars.length > 0) {
  console.error(`缺少必要的环境变量: ${missingEnvVars.join(', ')}`);
  console.error('请确保.env文件中包含这些变量');
  process.exit(1);
}

// 初始化客户端
const client = new PddClient({
  clientId: process.env.PDD_CLIENT_ID,
  clientSecret: process.env.PDD_CLIENT_SECRET,
  accessToken: process.env.PDD_SESSION_TOKEN,
  debug: true // 启用调试模式
});

// 测试推广位生成
async function testPidGenerate() {
  try {
    console.log('======= 拼多多推广位生成测试 =======');
    console.log('客户端配置:');
    console.log('- clientId:', process.env.PDD_CLIENT_ID);
    console.log('- accessToken:', maskToken(process.env.PDD_SESSION_TOKEN));
    console.log('- PID:', process.env.PDD_PID);
    console.log('\n开始生成推广位...');

    // 从PID中提取媒体ID
    const mediaId = extractMediaId(process.env.PDD_PID);
    console.log('- 提取的mediaId:', mediaId, '(仅供参考，测试中不使用)');
    
    // 构建创建参数 - 只使用必要参数
    const options = {
      number: 2,
      pIdNameList: ["测试推广位A", "测试推广位B"]
    };
    
    console.log('请求参数:', JSON.stringify(options, null, 2));
    
    // 执行请求
    const result = await client.goodsPidGenerate.generate(options);
    
    // 输出结果
    console.log('\n生成结果:');
    console.log(JSON.stringify(result, null, 2));
    
    console.log('\n生成完成!');
  } catch (error) {
    console.error('生成失败:', error.message);
  }
}

/**
 * 从PID中提取媒体ID
 * PID格式: mediaId_adsenseId
 * @param {String} pid PID字符串
 * @returns {String} 媒体ID
 */
function extractMediaId(pid) {
  if (!pid) return '';
  const parts = pid.split('_');
  return parts[0] || '';
}

// 辅助函数：掩码显示令牌
function maskToken(token) {
  if (!token) return 'undefined';
  return token.substr(0, 6) + '****' + token.substr(-6);
}

// 执行测试
testPidGenerate(); 