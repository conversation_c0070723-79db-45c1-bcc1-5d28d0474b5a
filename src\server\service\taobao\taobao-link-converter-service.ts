import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { TaobaoApiManager } from '../../taobao/api-manager.js';
import { TaobaoParams } from './params.js';

/**
 * 淘宝链接转换服务类
 * 处理淘宝客链接转换功能
 */
export class TaobaoLinkConverterService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 淘宝API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: TaobaoApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册链接转换工具
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('convertLink');

		this.mcpServer.tool(
			toolName,
			'转推广链接[淘宝客-服务商-万能转链]',
			TaobaoParams.linkConverter,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);

					// 直接将MCP参数传递给API调用，不做不必要的转换
					const result =
						await this.apiManager.linkConverter.convertLink(params);
					log(`工具 ${toolName} 执行结果: ${JSON.stringify(result)}`);
					return {
						content: [
							{
								type: 'text',
								text: JSON.stringify(result),
							},
						],
					};
				} catch (error) {
					logError(
						`工具 ${toolName} 执行出错: ${
							error instanceof Error
								? error.message
								: String(error)
						}`
					);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${
									error instanceof Error
										? error.message
										: JSON.stringify(error)
								}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
