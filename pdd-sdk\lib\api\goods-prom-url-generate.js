/**
 * 拼多多商品推广链接生成API
 * 接口名称：pdd.ddk.oauth.goods.prom.url.generate
 */

const BaseApi = require('../core/base-api');

class GoodsPromUrlGenerate extends BaseApi {
  /**
   * 构造函数
   * @param {Object} config 配置参数
   */
  constructor(config) {
    super(config);
  }

  /**
   * 生成商品推广链接
   * @param {Object} options 生成参数
   * @param {String} options.pid 推广位ID
   * @param {Array<String>} [options.goodsSignList] 商品ID列表，例如["c9r2omogKbZmu7"]
   * @param {Boolean} [options.generateShortUrl] 是否生成短链接
   * @param {Boolean} [options.multiGroup] 是否多人团，true-生成多人团推广链接 false-生成单人团推广链接（默认false）
   * @param {Boolean} [options.generateWeApp] 是否生成小程序推广
   * @param {String} [options.customParameters] 自定义参数，格式:["uid":"11111","sid":"22222"]
   * @returns {Promise} Promise对象
   */
  async generate(options = {}) {
    // 必要参数检查
    if (!options.pid) {
      throw new Error('推广位ID (pid) 不能为空');
    }
    
    if (!options.goodsSignList || options.goodsSignList.length === 0) {
      throw new Error('商品ID列表 (goodsSignList) 不能为空');
    }
    
    // 由于拼多多API对参数名称有特殊要求，需要手动处理一些参数名称
    // 拼多多API使用p_id而不是pid，这里我们手动映射
    const apiParams = {};
    
    // 映射pid为p_id
    apiParams.p_id = options.pid;
    
    // 映射goodsSignList为goods_sign_list
    if (options.goodsSignList) {
      apiParams.goods_sign_list = JSON.stringify(options.goodsSignList);
    }
    
    // 映射其他驼峰参数为下划线格式
    if (options.generateShortUrl !== undefined) {
      apiParams.generate_short_url = options.generateShortUrl;
    }
    
    if (options.generateWeApp !== undefined) {
      apiParams.generate_we_app = options.generateWeApp;
    }
    
    if (options.multiGroup !== undefined) {
      apiParams.multi_group = options.multiGroup;
    }
    
    if (options.customParameters) {
      apiParams.custom_parameters = options.customParameters;
    }
    
    // 执行请求时不转换参数，因为我们已经手动进行了转换
    return this.execute('pdd.ddk.oauth.goods.prom.url.generate', apiParams, false);
  }
}

module.exports = GoodsPromUrlGenerate; 