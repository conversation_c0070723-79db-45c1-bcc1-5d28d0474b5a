{"name": "@liuliang520500/pdd-sdk-new", "version": "1.0.2", "description": "拼多多开放平台 SDK，支持多多进宝商品推广、订单查询等接口", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "prepublishOnly": "npm run build", "test": "tsx examples/goods-detail.ts"}, "keywords": ["拼多多", "多多进宝", "pdd", "sdk", "pindu<PERSON><PERSON>", "ddk"], "author": {"name": "liuliang520500", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/liuliang520500/pdd-sdk-new.git"}, "bugs": {"url": "https://github.com/liuliang520500/pdd-sdk-new/issues"}, "homepage": "https://github.com/liuliang520500/pdd-sdk-new#readme", "license": "MIT", "dependencies": {"axios": "^1.6.8"}, "devDependencies": {"@types/node": "^20.11.30", "dotenv": "^16.4.5", "rimraf": "^5.0.10", "tsx": "^4.7.1", "typescript": "^5.4.2"}, "files": ["dist", "README.md", "LICENSE"], "engines": {"node": ">=14.0.0"}}