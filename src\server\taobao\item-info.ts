import { BaseApi } from './base-api.js';
import { log } from '../../utils/logger.js';
import { ItemInfoParams, TaobaoApiResponse } from './types.js';

/**
 * 淘宝客-公用-淘宝客商品详情查询(简版)API
 * 提供根据商品ID查询商品详情的功能
 */
export class ItemInfo extends BaseApi {
	/**
	 * 查询商品详情
	 * @param params 商品详情查询参数
	 * @returns 原始API响应
	 */
	async getItemInfo(params: ItemInfoParams): Promise<TaobaoApiResponse> {
		try {
			log(`执行商品详情查询: ${JSON.stringify(params)}`);

			// 调用淘宝API
			const result = await this.callApi('taobao.tbk.item.info.get', params);

			log(`商品详情查询结果: ${JSON.stringify(result)}`);

			// 返回原始响应
			return this.handleApiResponse(result);
		} catch (error) {
			log(`商品详情查询失败: ${error}`);
			throw error;
		}
	}
}
