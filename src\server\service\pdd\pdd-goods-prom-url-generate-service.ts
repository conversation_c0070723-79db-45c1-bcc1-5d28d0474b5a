import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { log, logError } from '../../../utils/logger.js';
import { PddApiManager } from '../../pdd/api-manager.js';
import { PddParams } from './params.js';

/**
 * 拼多多商品推广链接生成服务类
 * 处理多多进宝商品推广链接生成功能
 */
export class PddGoodsPromUrlGenerateService {
	/**
	 * 构造函数
	 * @param mcpServer MCP服务器实例
	 * @param apiManager 拼多多API管理器实例
	 * @param getFullToolName 获取完整工具名称的函数
	 */
	constructor(
		private mcpServer: McpServer,
		private apiManager: PddApiManager,
		private getFullToolName: (name: string) => string
	) {}

	/**
	 * 注册商品推广链接生成工具
	 * 接口名称：pdd.ddk.oauth.goods.prom.url.generate
	 */
	public registerTool(): void {
		const toolName = this.getFullToolName('goods.prom.url');

		this.mcpServer.tool(
			toolName,
			'生成多多进宝商品推广链接[pdd.ddk.oauth.goods.prom.url.generate]',
			PddParams.goodsPromUrlGenerate,
			async (params) => {
				try {
					log(`执行工具 ${toolName}: ${JSON.stringify(params)}`);

					try {
						// 执行商品推广链接生成
						const result =
							await this.apiManager.goodsPromUrlGenerate.generate(
								params
							);

						log(
							`工具 ${toolName} 执行结果: ${JSON.stringify(
								result
							)}`
						);

						return {
							content: [
								{
									type: 'text',
									text: JSON.stringify(result, null, 2),
								},
							],
						};
					} catch (apiError) {
						logError(
							`商品推广链接生成API调用失败: ${
								apiError instanceof Error
									? apiError.message
									: String(apiError)
							}`
						);
						throw apiError;
					}
				} catch (error) {
					const errorMessage =
						error instanceof Error ? error.message : String(error);
					logError(`工具 ${toolName} 执行出错: ${errorMessage}`);
					return {
						content: [
							{
								type: 'text',
								text: `工具执行失败: ${errorMessage}`,
							},
						],
						isError: true,
					};
				}
			}
		);
	}
}
